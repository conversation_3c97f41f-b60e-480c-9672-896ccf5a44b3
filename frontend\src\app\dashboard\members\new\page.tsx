"use client";

import { useState } from "react";
import { useRout<PERSON> } from "next/navigation";
import { useSession } from "next-auth/react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@/components/ui/form";
import { useApi } from "@/hooks/use-api";
import { UserRole } from "@/types";

const memberSchema = z.object({
	firstName: z.string().min(2, "Le prénom doit contenir au moins 2 caractères"),
	lastName: z.string().min(2, "Le nom doit contenir au moins 2 caractères"),
	email: z.string().email("Adresse email invalide").optional().or(z.literal("")),
	phone: z.string().optional(),
	address: z.string().optional(),
});

type MemberForm = z.infer<typeof memberSchema>;

export default function NewMemberPage() {
	const { data: session } = useSession();
	const router = useRouter();
	const api = useApi();

	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);

	// Vérifier les permissions
	const canCreateMembers =
		session?.user && (session.user as any).role === UserRole.SECRETARY_GENERAL;

	const form = useForm<MemberForm>({
		resolver: zodResolver(memberSchema),
		defaultValues: {
			firstName: "",
			lastName: "",
			email: "",
			phone: "",
			address: "",
		},
	});

	const onSubmit = async (data: MemberForm) => {
		setIsLoading(true);
		setError(null);

		try {
			// Nettoyer les champs vides
			const cleanData = {
				firstName: data.firstName,
				lastName: data.lastName,
				...(data.email && { email: data.email }),
				...(data.phone && { phone: data.phone }),
				...(data.address && { address: data.address }),
			};

			await api.createMember(cleanData);
			router.push("/dashboard/members");
		} catch (error) {
			console.error("Erreur lors de la création du membre:", error);
			if (error instanceof Error) {
				setError(error.message);
			} else {
				setError("Erreur lors de la création du membre. Veuillez réessayer.");
			}
		} finally {
			setIsLoading(false);
		}
	};

	if (!canCreateMembers) {
		return (
			<div className="space-y-6">
				<div className="flex items-center gap-4">
					<Link href="/dashboard/members">
						<Button variant="ghost" size="sm">
							<ArrowLeft className="h-4 w-4 mr-2" />
							Retour
						</Button>
					</Link>
				</div>
				<div className="flex items-center justify-center h-64">
					<div className="text-center">
						<h2 className="text-lg font-semibold text-gray-900">
							Accès refusé
						</h2>
						<p className="text-gray-600">
							Seuls les administrateurs peuvent créer des membres.
						</p>
					</div>
				</div>
			</div>
		);
	}

	return (
		<div className="space-y-6">
			{/* En-tête */}
			<div className="flex items-center gap-4">
				<Link href="/dashboard/members">
					<Button variant="ghost" size="sm">
						<ArrowLeft className="h-4 w-4 mr-2" />
						Retour
					</Button>
				</Link>
				<div>
					<h1 className="text-2xl font-bold text-gray-900">Nouveau Membre</h1>
					<p className="text-gray-600">Créer un nouveau membre de la tontine</p>
				</div>
			</div>

			{/* Formulaire */}
			<Card className="max-w-2xl">
				<CardHeader>
					<CardTitle>Informations du membre</CardTitle>
					<CardDescription>
						Remplissez tous les champs pour créer un nouveau membre
					</CardDescription>
				</CardHeader>
				<CardContent>
					<Form {...form}>
						<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
							{/* Informations personnelles */}
							<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
								<FormField
									control={form.control}
									name="firstName"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Prénom *</FormLabel>
											<FormControl>
												<Input
													placeholder="Prénom"
													{...field}
													disabled={isLoading}
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
								<FormField
									control={form.control}
									name="lastName"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Nom *</FormLabel>
											<FormControl>
												<Input
													placeholder="Nom de famille"
													{...field}
													disabled={isLoading}
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
							</div>

							{/* Informations de contact */}
							<FormField
								control={form.control}
								name="email"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Email</FormLabel>
										<FormControl>
											<Input
												type="email"
												placeholder="<EMAIL>"
												{...field}
												disabled={isLoading}
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="phone"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Téléphone</FormLabel>
										<FormControl>
											<Input
												placeholder="+237123456789"
												{...field}
												disabled={isLoading}
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="address"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Adresse</FormLabel>
										<FormControl>
											<Textarea
												placeholder="Adresse complète"
												{...field}
												disabled={isLoading}
												rows={3}
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>



							{/* Message d'erreur */}
							{error && (
								<div className="text-red-600 text-sm bg-red-50 p-3 rounded">
									{error}
								</div>
							)}

							{/* Actions */}
							<div className="flex justify-end gap-4 pt-6">
								<Link href="/dashboard/members">
									<Button variant="outline" disabled={isLoading}>
										Annuler
									</Button>
								</Link>
								<Button type="submit" disabled={isLoading}>
									{isLoading ? "Création..." : "Créer le membre"}
								</Button>
							</div>
						</form>
					</Form>
				</CardContent>
			</Card>
		</div>
	);
}
