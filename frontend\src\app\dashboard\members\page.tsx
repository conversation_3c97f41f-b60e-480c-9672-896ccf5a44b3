"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import {
	Plus,
	Search,
	MoreHorizontal,
	Edit,
	Trash2,
	Eye,
	Mail,
	Phone,
	MapPin,
} from "lucide-react";
import Link from "next/link";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@/components/ui/table";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuLabel,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { useApi } from "@/hooks/use-api";
import { Member, MemberStats, UserRole } from "@/types";

export default function MembersPage() {
	const { data: session } = useSession();
	const api = useApi();

	const [members, setMembers] = useState<Member[]>([]);
	const [stats, setStats] = useState<MemberStats | null>(null);
	const [loading, setLoading] = useState(true);
	const [searchTerm, setSearchTerm] = useState("");

	// Vérifier les permissions
	const canManageMembers =
		session?.user &&
		((session.user as any).role === UserRole.SECRETARY_GENERAL ||
			(session.user as any).role === UserRole.CONTROLLER);
	const canEditMembers =
		session?.user && (session.user as any).role === UserRole.SECRETARY_GENERAL;

	const loadData = async () => {
		try {
			setLoading(true);

			// Charger les membres et les statistiques
			const [membersData] = await Promise.all([
				api.getMembers(),
			]);

			// Filtrer côté client pour la recherche
			let filteredMembers = membersData;
			if (searchTerm) {
				const search = searchTerm.toLowerCase();
				filteredMembers = membersData.filter(
					(member) =>
						member.firstName.toLowerCase().includes(search) ||
						member.lastName.toLowerCase().includes(search) ||
						member.email?.toLowerCase().includes(search) ||
						member.phone?.includes(search)
				);
			}

			setMembers(filteredMembers);

			// Calculer les stats
			const statsData: MemberStats = {
				total: membersData.length,
				withEmail: membersData.filter(m => m.email).length,
				withPhone: membersData.filter(m => m.phone).length,
				withAddress: membersData.filter(m => m.address).length,
			};
			setStats(statsData);
		} catch (error) {
			console.error("Erreur lors du chargement des données:", error);
		} finally {
			setLoading(false);
		}
	};

	useEffect(() => {
		if (session?.accessToken) {
			loadData();
		}
	}, [session, searchTerm]);

	const handleDeleteMember = async (memberId: string) => {
		if (!confirm("Êtes-vous sûr de vouloir supprimer ce membre ?")) {
			return;
		}

		try {
			await api.deleteMember(memberId);
			loadData();
		} catch (error) {
			console.error("Erreur lors de la suppression:", error);
		}
	};

	if (!canManageMembers) {
		return (
			<div className="flex items-center justify-center h-64">
				<div className="text-center">
					<h2 className="text-lg font-semibold text-gray-900">Accès refusé</h2>
					<p className="text-gray-600">
						Vous n'avez pas les permissions pour accéder à cette page.
					</p>
				</div>
			</div>
		);
	}

	return (
		<div className="space-y-6">
			{/* En-tête */}
			<div className="flex justify-between items-center">
				<div>
					<h1 className="text-2xl font-bold text-gray-900">
						Gestion des Membres
					</h1>
					<p className="text-gray-600">Gérez les membres de votre tontine</p>
				</div>
				{canEditMembers && (
					<Link href="/dashboard/members/new">
						<Button>
							<Plus className="h-4 w-4 mr-2" />
							Nouveau membre
						</Button>
					</Link>
				)}
			</div>

			{/* Statistiques */}
			{stats && (
				<div className="grid grid-cols-1 md:grid-cols-4 gap-4">
					<Card>
						<CardHeader className="pb-2">
							<CardTitle className="text-sm font-medium text-gray-600">
								Total Membres
							</CardTitle>
						</CardHeader>
						<CardContent>
							<div className="text-2xl font-bold">{stats.total}</div>
						</CardContent>
					</Card>
					<Card>
						<CardHeader className="pb-2">
							<CardTitle className="text-sm font-medium text-gray-600">
								Avec Email
							</CardTitle>
						</CardHeader>
						<CardContent>
							<div className="text-2xl font-bold text-blue-600">
								{stats.withEmail}
							</div>
						</CardContent>
					</Card>
					<Card>
						<CardHeader className="pb-2">
							<CardTitle className="text-sm font-medium text-gray-600">
								Avec Téléphone
							</CardTitle>
						</CardHeader>
						<CardContent>
							<div className="text-2xl font-bold text-green-600">
								{stats.withPhone}
							</div>
						</CardContent>
					</Card>
					<Card>
						<CardHeader className="pb-2">
							<CardTitle className="text-sm font-medium text-gray-600">
								Avec Adresse
							</CardTitle>
						</CardHeader>
						<CardContent>
							<div className="text-2xl font-bold text-purple-600">
								{stats.withAddress}
							</div>
						</CardContent>
					</Card>
				</div>
			)}

			{/* Recherche */}
			<Card>
				<CardHeader>
					<CardTitle>Recherche</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="relative">
						<Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
						<Input
							placeholder="Rechercher par nom, prénom, email, téléphone..."
							value={searchTerm}
							onChange={(e) => setSearchTerm(e.target.value)}
							className="pl-10"
						/>
					</div>
				</CardContent>
			</Card>

			{/* Liste des membres */}
			<Card>
				<CardHeader>
					<CardTitle>Liste des Membres</CardTitle>
					<CardDescription>
						{members.length} membre(s) trouvé(s)
					</CardDescription>
				</CardHeader>
				<CardContent>
					{loading ? (
						<div className="flex justify-center py-8">
							<div className="text-gray-500">Chargement...</div>
						</div>
					) : (
						<Table>
							<TableHeader>
								<TableRow>
									<TableHead>Nom</TableHead>
									<TableHead>Contact</TableHead>
									<TableHead>Adresse</TableHead>
									<TableHead>Informations</TableHead>
									{canEditMembers && <TableHead>Actions</TableHead>}
								</TableRow>
							</TableHeader>
							<TableBody>
								{members.map((member) => (
									<TableRow key={member._id}>
										<TableCell>
											<div className="font-medium">
												{member.firstName} {member.lastName}
											</div>
										</TableCell>
										<TableCell>
											<div className="space-y-1">
												{member.email && (
													<div className="flex items-center text-sm text-gray-600">
														<Mail className="h-3 w-3 mr-1" />
														{member.email}
													</div>
												)}
												{member.phone && (
													<div className="flex items-center text-sm text-gray-600">
														<Phone className="h-3 w-3 mr-1" />
														{member.phone}
													</div>
												)}
											</div>
										</TableCell>
										<TableCell>
											{member.address && (
												<div className="flex items-center text-sm text-gray-600">
													<MapPin className="h-3 w-3 mr-1" />
													{member.address}
												</div>
											)}
										</TableCell>
										<TableCell>
											<div className="flex gap-1">
												{member.email && <Badge variant="secondary">Email</Badge>}
												{member.phone && <Badge variant="secondary">Tél</Badge>}
												{member.address && <Badge variant="secondary">Adresse</Badge>}
											</div>
										</TableCell>
										{canEditMembers && (
											<TableCell>
												<DropdownMenu>
													<DropdownMenuTrigger asChild>
														<Button variant="ghost" className="h-8 w-8 p-0">
															<MoreHorizontal className="h-4 w-4" />
														</Button>
													</DropdownMenuTrigger>
													<DropdownMenuContent align="end">
														<DropdownMenuLabel>Actions</DropdownMenuLabel>
														<DropdownMenuItem asChild>
															<Link href={`/dashboard/members/${member._id}`}>
																<Eye className="h-4 w-4 mr-2" />
																Voir détails
															</Link>
														</DropdownMenuItem>
														<DropdownMenuItem asChild>
															<Link
																href={`/dashboard/members/${member._id}/edit`}
															>
																<Edit className="h-4 w-4 mr-2" />
																Modifier
															</Link>
														</DropdownMenuItem>
														<DropdownMenuSeparator />
														<DropdownMenuItem
															onClick={() => handleDeleteMember(member._id)}
															className="text-red-600"
														>
															<Trash2 className="h-4 w-4 mr-2" />
															Supprimer
														</DropdownMenuItem>
													</DropdownMenuContent>
												</DropdownMenu>
											</TableCell>
										)}
									</TableRow>
								))}
							</TableBody>
						</Table>
					)}
				</CardContent>
			</Card>
		</div>
	);
}
