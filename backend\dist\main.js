"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
require("reflect-metadata");
const common_1 = require("@nestjs/common");
const core_1 = require("@nestjs/core");
const app_module_1 = require("./app.module");
const swagger_1 = require("@nestjs/swagger");
const mongoose_1 = require("@nestjs/mongoose");
const history_schema_1 = require("./history/schemas/history.schema");
const history_interceptor_1 = require("./common/interceptors/history.interceptor");
async function bootstrap() {
    const app = await core_1.NestFactory.create(app_module_1.AppModule);
    // Active la validation globale des DTOs
    app.useGlobalPipes(new common_1.ValidationPipe({
        whitelist: true, // Ignore les propriétés non définies dans le DTO
        forbidNonWhitelisted: true, // Erreur si des propriétés non attendues sont envoyées
        forbidUnknownValues: true, // Erreur si le body est vide ou non conforme
        transform: true, // Transforme le payload en instance du DTO
    }));
    const config = new swagger_1.DocumentBuilder()
        .setTitle("Tontine API")
        .setDescription("API de gestion de tontine")
        .setVersion("1.0")
        .addBearerAuth()
        .build();
    const document = swagger_1.SwaggerModule.createDocument(app, config);
    swagger_1.SwaggerModule.setup("api", app, document);
    // Intercepteur global pour journaliser les actions
    const historyModel = app.get((0, mongoose_1.getModelToken)(history_schema_1.History.name));
    app.useGlobalInterceptors(new history_interceptor_1.HistoryInterceptor(historyModel));
    app.enableCors({
        origin: ['http://localhost:3000', 'https://myapp.com'],
        methods: 'GET,HEAD,PUT,PATCH,POST,DELETE',
        credentials: true,
    });
    const port = process.env.PORT || 4000;
    await app.listen(port);
    console.log(`Application is running on: http://localhost:${port}`);
}
bootstrap();
