"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { Plus, DollarSign, TrendingUp, TrendingDown } from "lucide-react";
import Link from "next/link";

import { Button } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { useApi } from "@/hooks/use-api";
import { UserRole, PaymentStats } from "@/types";

export default function PaymentsPage() {
	const { data: session } = useSession();
	const api = useApi();

	const [stats, setStats] = useState<PaymentStats | null>(null);
	const [loading, setLoading] = useState(true);

	// Vérifier les permissions
	const canManagePayments =
		session?.user &&
		((session.user as any).role === UserRole.SECRETARY_GENERAL ||
			(session.user as any).role === UserRole.CONTROLLER ||
			(session.user as any).role === UserRole.CASHIER);

	const canCreatePayments =
		session?.user &&
		((session.user as any).role === UserRole.SECRETARY_GENERAL ||
			(session.user as any).role === UserRole.CONTROLLER ||
			(session.user as any).role === UserRole.CASHIER);

	useEffect(() => {
		if (session?.accessToken) {
			loadStats();
		}
	}, [session]);

	const loadStats = async () => {
		try {
			setLoading(true);
			// Pour l'instant, on simule les stats car l'API n'a pas d'endpoint dédié
			// Dans une vraie implémentation, on ferait un appel API pour récupérer les stats
			const mockStats: PaymentStats = {
				totalIn: 0,
				totalOut: 0,
				netAmount: 0,
				contributionsTotal: 0,
				transfersTotal: 0,
				externalTotal: 0,
			};
			setStats(mockStats);
		} catch (error) {
			console.error("Erreur lors du chargement des statistiques:", error);
		} finally {
			setLoading(false);
		}
	};

	const formatCurrency = (amount: number) => {
		return new Intl.NumberFormat('fr-FR', {
			style: 'currency',
			currency: 'XAF',
		}).format(amount);
	};

	if (!canManagePayments) {
		return (
			<div className="flex items-center justify-center h-64">
				<div className="text-center">
					<h2 className="text-lg font-semibold text-gray-900">Accès refusé</h2>
					<p className="text-gray-600">
						Vous n'avez pas les permissions pour accéder à cette page.
					</p>
				</div>
			</div>
		);
	}

	return (
		<div className="space-y-6">
			{/* En-tête */}
			<div className="flex justify-between items-center">
				<div>
					<h1 className="text-2xl font-bold text-gray-900">
						Gestion des Paiements
					</h1>
					<p className="text-gray-600">Enregistrez et gérez les paiements de la tontine</p>
				</div>
				{canCreatePayments && (
					<Link href="/dashboard/payments/new">
						<Button>
							<Plus className="h-4 w-4 mr-2" />
							Nouveau paiement
						</Button>
					</Link>
				)}
			</div>

			{/* Statistiques */}
			{stats && (
				<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
					<Card>
						<CardHeader className="pb-2">
							<CardTitle className="text-sm font-medium text-gray-600 flex items-center">
								<TrendingUp className="h-4 w-4 mr-2 text-green-600" />
								Total Entrées
							</CardTitle>
						</CardHeader>
						<CardContent>
							<div className="text-2xl font-bold text-green-600">
								{formatCurrency(stats.totalIn)}
							</div>
						</CardContent>
					</Card>
					<Card>
						<CardHeader className="pb-2">
							<CardTitle className="text-sm font-medium text-gray-600 flex items-center">
								<TrendingDown className="h-4 w-4 mr-2 text-red-600" />
								Total Sorties
							</CardTitle>
						</CardHeader>
						<CardContent>
							<div className="text-2xl font-bold text-red-600">
								{formatCurrency(stats.totalOut)}
							</div>
						</CardContent>
					</Card>
					<Card>
						<CardHeader className="pb-2">
							<CardTitle className="text-sm font-medium text-gray-600 flex items-center">
								<DollarSign className="h-4 w-4 mr-2" />
								Solde Net
							</CardTitle>
						</CardHeader>
						<CardContent>
							<div className={`text-2xl font-bold ${stats.netAmount >= 0 ? 'text-green-600' : 'text-red-600'}`}>
								{formatCurrency(stats.netAmount)}
							</div>
						</CardContent>
					</Card>
				</div>
			)}

			{/* Statistiques détaillées */}
			{stats && (
				<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
					<Card>
						<CardHeader className="pb-2">
							<CardTitle className="text-sm font-medium text-gray-600">
								Cotisations
							</CardTitle>
						</CardHeader>
						<CardContent>
							<div className="text-2xl font-bold text-blue-600">
								{formatCurrency(stats.contributionsTotal)}
							</div>
						</CardContent>
					</Card>
					<Card>
						<CardHeader className="pb-2">
							<CardTitle className="text-sm font-medium text-gray-600">
								Transferts
							</CardTitle>
						</CardHeader>
						<CardContent>
							<div className="text-2xl font-bold text-purple-600">
								{formatCurrency(stats.transfersTotal)}
							</div>
						</CardContent>
					</Card>
					<Card>
						<CardHeader className="pb-2">
							<CardTitle className="text-sm font-medium text-gray-600">
								Paiements Externes
							</CardTitle>
						</CardHeader>
						<CardContent>
							<div className="text-2xl font-bold text-orange-600">
								{formatCurrency(stats.externalTotal)}
							</div>
						</CardContent>
					</Card>
				</div>
			)}

			{/* Actions rapides */}
			<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
				<Card className="cursor-pointer hover:shadow-md transition-shadow">
					<Link href="/dashboard/payments/new?type=contribution">
						<CardHeader>
							<CardTitle className="text-lg flex items-center">
								<TrendingUp className="h-5 w-5 mr-2 text-green-600" />
								Enregistrer une cotisation
							</CardTitle>
							<CardDescription>
								Enregistrer le paiement d'une cotisation par un membre
							</CardDescription>
						</CardHeader>
					</Link>
				</Card>

				<Card className="cursor-pointer hover:shadow-md transition-shadow">
					<Link href="/dashboard/payments/new?type=transfer">
						<CardHeader>
							<CardTitle className="text-lg flex items-center">
								<DollarSign className="h-5 w-5 mr-2 text-purple-600" />
								Effectuer un transfert
							</CardTitle>
							<CardDescription>
								Transférer des fonds entre caisses
							</CardDescription>
						</CardHeader>
					</Link>
				</Card>

				<Card className="cursor-pointer hover:shadow-md transition-shadow">
					<Link href="/dashboard/payments/new?type=external">
						<CardHeader>
							<CardTitle className="text-lg flex items-center">
								<TrendingDown className="h-5 w-5 mr-2 text-orange-600" />
								Paiement externe
							</CardTitle>
							<CardDescription>
								Enregistrer un paiement externe (frais, achats, etc.)
							</CardDescription>
						</CardHeader>
					</Link>
				</Card>
			</div>

			{/* Information */}
			<Card>
				<CardHeader>
					<CardTitle>Information</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="space-y-2 text-sm text-gray-600">
						<p>• <strong>Cotisations :</strong> Paiements des membres pour leurs parts dans la session</p>
						<p>• <strong>Transferts :</strong> Mouvements de fonds entre caisses (principale ↔ réunion)</p>
						<p>• <strong>Paiements externes :</strong> Sorties pour frais, achats ou autres dépenses</p>
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
