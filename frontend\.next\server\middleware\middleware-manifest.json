{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/frontend_a8ef3cd8._.js", "server/edge/chunks/07131_@auth_core_b6d1e115._.js", "server/edge/chunks/836f6_jose_dist_webapi_9125ab7e._.js", "server/edge/chunks/948cc_zod_v4_de5cece9._.js", "server/edge/chunks/3a74a_tailwind-merge_dist_bundle-mjs_mjs_6b04a0b0._.js", "server/edge/chunks/9e883__pnpm_43258dde._.js", "server/edge/chunks/[root-of-the-server]__3a3d429a._.js", "server/edge/chunks/turbopack-frontend_edge-wrapper_03e5c9f9.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "apf+SPZMzT5yW82p9rfjV4S0C00MQFYgvHSGcDkfCl0=", "__NEXT_PREVIEW_MODE_ID": "06c874e964d3130ec5ccf08e567b016a", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "1c0617c2bc6cd1168a481cbfa854dc35d8dac8896a8d285a60e0a5127a175348", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "8d469d19e3cef5852175ae19aba5ad0d4ae5f6d8a68976a7c09309ed977fc122"}}}, "instrumentation": null, "functions": {}}