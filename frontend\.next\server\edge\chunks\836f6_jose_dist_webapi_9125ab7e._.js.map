{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/frontend/node_modules/.pnpm/jose@6.1.0/node_modules/jose/dist/webapi/lib/buffer_utils.js"], "sourcesContent": ["export const encoder = new TextEncoder();\nexport const decoder = new TextDecoder();\nconst MAX_INT32 = 2 ** 32;\nexport function concat(...buffers) {\n    const size = buffers.reduce((acc, { length }) => acc + length, 0);\n    const buf = new Uint8Array(size);\n    let i = 0;\n    for (const buffer of buffers) {\n        buf.set(buffer, i);\n        i += buffer.length;\n    }\n    return buf;\n}\nfunction writeUInt32BE(buf, value, offset) {\n    if (value < 0 || value >= MAX_INT32) {\n        throw new RangeError(`value must be >= 0 and <= ${MAX_INT32 - 1}. Received ${value}`);\n    }\n    buf.set([value >>> 24, value >>> 16, value >>> 8, value & 0xff], offset);\n}\nexport function uint64be(value) {\n    const high = Math.floor(value / MAX_INT32);\n    const low = value % MAX_INT32;\n    const buf = new Uint8Array(8);\n    writeUInt32BE(buf, high, 0);\n    writeUInt32BE(buf, low, 4);\n    return buf;\n}\nexport function uint32be(value) {\n    const buf = new Uint8Array(4);\n    writeUInt32BE(buf, value);\n    return buf;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAO,MAAM,UAAU,IAAI;AACpB,MAAM,UAAU,IAAI;AAC3B,MAAM,YAAY,KAAK;AAChB,SAAS,OAAO,GAAG,OAAO;IAC7B,MAAM,OAAO,QAAQ,MAAM,CAAC,CAAC,KAAK,EAAE,MAAM,EAAE,GAAK,MAAM,QAAQ;IAC/D,MAAM,MAAM,IAAI,WAAW;IAC3B,IAAI,IAAI;IACR,KAAK,MAAM,UAAU,QAAS;QAC1B,IAAI,GAAG,CAAC,QAAQ;QAChB,KAAK,OAAO,MAAM;IACtB;IACA,OAAO;AACX;AACA,SAAS,cAAc,GAAG,EAAE,KAAK,EAAE,MAAM;IACrC,IAAI,QAAQ,KAAK,SAAS,WAAW;QACjC,MAAM,IAAI,WAAW,CAAC,0BAA0B,EAAE,YAAY,EAAE,WAAW,EAAE,OAAO;IACxF;IACA,IAAI,GAAG,CAAC;QAAC,UAAU;QAAI,UAAU;QAAI,UAAU;QAAG,QAAQ;KAAK,EAAE;AACrE;AACO,SAAS,SAAS,KAAK;IAC1B,MAAM,OAAO,KAAK,KAAK,CAAC,QAAQ;IAChC,MAAM,MAAM,QAAQ;IACpB,MAAM,MAAM,IAAI,WAAW;IAC3B,cAAc,KAAK,MAAM;IACzB,cAAc,KAAK,KAAK;IACxB,OAAO;AACX;AACO,SAAS,SAAS,KAAK;IAC1B,MAAM,MAAM,IAAI,WAAW;IAC3B,cAAc,KAAK;IACnB,OAAO;AACX", "ignoreList": [0]}}, {"offset": {"line": 57, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/frontend/node_modules/.pnpm/jose@6.1.0/node_modules/jose/dist/webapi/lib/base64.js"], "sourcesContent": ["export function encodeBase64(input) {\n    if (Uint8Array.prototype.toBase64) {\n        return input.toBase64();\n    }\n    const CHUNK_SIZE = 0x8000;\n    const arr = [];\n    for (let i = 0; i < input.length; i += CHUNK_SIZE) {\n        arr.push(String.fromCharCode.apply(null, input.subarray(i, i + CHUNK_SIZE)));\n    }\n    return btoa(arr.join(''));\n}\nexport function decodeBase64(encoded) {\n    if (Uint8Array.fromBase64) {\n        return Uint8Array.fromBase64(encoded);\n    }\n    const binary = atob(encoded);\n    const bytes = new Uint8Array(binary.length);\n    for (let i = 0; i < binary.length; i++) {\n        bytes[i] = binary.charCodeAt(i);\n    }\n    return bytes;\n}\n"], "names": [], "mappings": ";;;;;;AAAO,SAAS,aAAa,KAAK;IAC9B,IAAI,WAAW,SAAS,CAAC,QAAQ,EAAE;QAC/B,OAAO,MAAM,QAAQ;IACzB;IACA,MAAM,aAAa;IACnB,MAAM,MAAM,EAAE;IACd,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,KAAK,WAAY;QAC/C,IAAI,IAAI,CAAC,OAAO,YAAY,CAAC,KAAK,CAAC,MAAM,MAAM,QAAQ,CAAC,GAAG,IAAI;IACnE;IACA,OAAO,KAAK,IAAI,IAAI,CAAC;AACzB;AACO,SAAS,aAAa,OAAO;IAChC,IAAI,WAAW,UAAU,EAAE;QACvB,OAAO,WAAW,UAAU,CAAC;IACjC;IACA,MAAM,SAAS,KAAK;IACpB,MAAM,QAAQ,IAAI,WAAW,OAAO,MAAM;IAC1C,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;QACpC,KAAK,CAAC,EAAE,GAAG,OAAO,UAAU,CAAC;IACjC;IACA,OAAO;AACX", "ignoreList": [0]}}, {"offset": {"line": 89, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/frontend/node_modules/.pnpm/jose@6.1.0/node_modules/jose/dist/webapi/util/base64url.js"], "sourcesContent": ["import { encoder, decoder } from '../lib/buffer_utils.js';\nimport { encodeBase64, decodeBase64 } from '../lib/base64.js';\nexport function decode(input) {\n    if (Uint8Array.fromBase64) {\n        return Uint8Array.fromBase64(typeof input === 'string' ? input : decoder.decode(input), {\n            alphabet: 'base64url',\n        });\n    }\n    let encoded = input;\n    if (encoded instanceof Uint8Array) {\n        encoded = decoder.decode(encoded);\n    }\n    encoded = encoded.replace(/-/g, '+').replace(/_/g, '/').replace(/\\s/g, '');\n    try {\n        return decodeBase64(encoded);\n    }\n    catch {\n        throw new TypeError('The input to be decoded is not correctly encoded.');\n    }\n}\nexport function encode(input) {\n    let unencoded = input;\n    if (typeof unencoded === 'string') {\n        unencoded = encoder.encode(unencoded);\n    }\n    if (Uint8Array.prototype.toBase64) {\n        return unencoded.toBase64({ alphabet: 'base64url', omitPadding: true });\n    }\n    return encodeBase64(unencoded).replace(/=/g, '').replace(/\\+/g, '-').replace(/\\//g, '_');\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AACO,SAAS,OAAO,KAAK;IACxB,IAAI,WAAW,UAAU,EAAE;QACvB,OAAO,WAAW,UAAU,CAAC,OAAO,UAAU,WAAW,QAAQ,6OAAO,CAAC,MAAM,CAAC,QAAQ;YACpF,UAAU;QACd;IACJ;IACA,IAAI,UAAU;IACd,IAAI,mBAAmB,YAAY;QAC/B,UAAU,6OAAO,CAAC,MAAM,CAAC;IAC7B;IACA,UAAU,QAAQ,OAAO,CAAC,MAAM,KAAK,OAAO,CAAC,MAAM,KAAK,OAAO,CAAC,OAAO;IACvE,IAAI;QACA,OAAO,IAAA,4OAAY,EAAC;IACxB,EACA,OAAM;QACF,MAAM,IAAI,UAAU;IACxB;AACJ;AACO,SAAS,OAAO,KAAK;IACxB,IAAI,YAAY;IAChB,IAAI,OAAO,cAAc,UAAU;QAC/B,YAAY,6OAAO,CAAC,MAAM,CAAC;IAC/B;IACA,IAAI,WAAW,SAAS,CAAC,QAAQ,EAAE;QAC/B,OAAO,UAAU,QAAQ,CAAC;YAAE,UAAU;YAAa,aAAa;QAAK;IACzE;IACA,OAAO,IAAA,4OAAY,EAAC,WAAW,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,OAAO,KAAK,OAAO,CAAC,OAAO;AACxF", "ignoreList": [0]}}, {"offset": {"line": 133, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/frontend/node_modules/.pnpm/jose@6.1.0/node_modules/jose/dist/webapi/lib/private_symbols.js"], "sourcesContent": ["export const unprotected = Symbol();\n"], "names": [], "mappings": ";;;;AAAO,MAAM,cAAc", "ignoreList": [0]}}, {"offset": {"line": 142, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/frontend/node_modules/.pnpm/jose@6.1.0/node_modules/jose/dist/webapi/util/errors.js"], "sourcesContent": ["export class JOSEError extends Error {\n    static code = 'ERR_JOSE_GENERIC';\n    code = 'ERR_JOSE_GENERIC';\n    constructor(message, options) {\n        super(message, options);\n        this.name = this.constructor.name;\n        Error.captureStackTrace?.(this, this.constructor);\n    }\n}\nexport class JWTClaimValidationFailed extends JOSEError {\n    static code = 'ERR_JWT_CLAIM_VALIDATION_FAILED';\n    code = 'ERR_JWT_CLAIM_VALIDATION_FAILED';\n    claim;\n    reason;\n    payload;\n    constructor(message, payload, claim = 'unspecified', reason = 'unspecified') {\n        super(message, { cause: { claim, reason, payload } });\n        this.claim = claim;\n        this.reason = reason;\n        this.payload = payload;\n    }\n}\nexport class JWTExpired extends JOSEError {\n    static code = 'ERR_JWT_EXPIRED';\n    code = 'ERR_JWT_EXPIRED';\n    claim;\n    reason;\n    payload;\n    constructor(message, payload, claim = 'unspecified', reason = 'unspecified') {\n        super(message, { cause: { claim, reason, payload } });\n        this.claim = claim;\n        this.reason = reason;\n        this.payload = payload;\n    }\n}\nexport class JOSEAlgNotAllowed extends J<PERSON><PERSON><PERSON>r {\n    static code = 'ERR_JOSE_ALG_NOT_ALLOWED';\n    code = 'ERR_JOSE_ALG_NOT_ALLOWED';\n}\nexport class JOSENotSupported extends JOSEError {\n    static code = 'ERR_JOSE_NOT_SUPPORTED';\n    code = 'ERR_JOSE_NOT_SUPPORTED';\n}\nexport class JWEDecryptionFailed extends JOSEError {\n    static code = 'ERR_JWE_DECRYPTION_FAILED';\n    code = 'ERR_JWE_DECRYPTION_FAILED';\n    constructor(message = 'decryption operation failed', options) {\n        super(message, options);\n    }\n}\nexport class JWEInvalid extends JOSEError {\n    static code = 'ERR_JWE_INVALID';\n    code = 'ERR_JWE_INVALID';\n}\nexport class JWSInvalid extends JOSEError {\n    static code = 'ERR_JWS_INVALID';\n    code = 'ERR_JWS_INVALID';\n}\nexport class JWTInvalid extends JOSEError {\n    static code = 'ERR_JWT_INVALID';\n    code = 'ERR_JWT_INVALID';\n}\nexport class JWKInvalid extends JOSEError {\n    static code = 'ERR_JWK_INVALID';\n    code = 'ERR_JWK_INVALID';\n}\nexport class JWKSInvalid extends JOSEError {\n    static code = 'ERR_JWKS_INVALID';\n    code = 'ERR_JWKS_INVALID';\n}\nexport class JWKSNoMatchingKey extends JOSEError {\n    static code = 'ERR_JWKS_NO_MATCHING_KEY';\n    code = 'ERR_JWKS_NO_MATCHING_KEY';\n    constructor(message = 'no applicable key found in the JSON Web Key Set', options) {\n        super(message, options);\n    }\n}\nexport class JWKSMultipleMatchingKeys extends JOSEError {\n    [Symbol.asyncIterator];\n    static code = 'ERR_JWKS_MULTIPLE_MATCHING_KEYS';\n    code = 'ERR_JWKS_MULTIPLE_MATCHING_KEYS';\n    constructor(message = 'multiple matching keys found in the JSON Web Key Set', options) {\n        super(message, options);\n    }\n}\nexport class JWKSTimeout extends JOSEError {\n    static code = 'ERR_JWKS_TIMEOUT';\n    code = 'ERR_JWKS_TIMEOUT';\n    constructor(message = 'request timed out', options) {\n        super(message, options);\n    }\n}\nexport class JWSSignatureVerificationFailed extends JOSEError {\n    static code = 'ERR_JWS_SIGNATURE_VERIFICATION_FAILED';\n    code = 'ERR_JWS_SIGNATURE_VERIFICATION_FAILED';\n    constructor(message = 'signature verification failed', options) {\n        super(message, options);\n    }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAO,MAAM,kBAAkB;IAC3B,OAAO,OAAO,mBAAmB;IACjC,OAAO,mBAAmB;IAC1B,YAAY,OAAO,EAAE,OAAO,CAAE;QAC1B,KAAK,CAAC,SAAS;QACf,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI;QACjC,MAAM,iBAAiB,GAAG,IAAI,EAAE,IAAI,CAAC,WAAW;IACpD;AACJ;AACO,MAAM,iCAAiC;IAC1C,OAAO,OAAO,kCAAkC;IAChD,OAAO,kCAAkC;IACzC,MAAM;IACN,OAAO;IACP,QAAQ;IACR,YAAY,OAAO,EAAE,OAAO,EAAE,QAAQ,aAAa,EAAE,SAAS,aAAa,CAAE;QACzE,KAAK,CAAC,SAAS;YAAE,OAAO;gBAAE;gBAAO;gBAAQ;YAAQ;QAAE;QACnD,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,OAAO,GAAG;IACnB;AACJ;AACO,MAAM,mBAAmB;IAC5B,OAAO,OAAO,kBAAkB;IAChC,OAAO,kBAAkB;IACzB,MAAM;IACN,OAAO;IACP,QAAQ;IACR,YAAY,OAAO,EAAE,OAAO,EAAE,QAAQ,aAAa,EAAE,SAAS,aAAa,CAAE;QACzE,KAAK,CAAC,SAAS;YAAE,OAAO;gBAAE;gBAAO;gBAAQ;YAAQ;QAAE;QACnD,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,OAAO,GAAG;IACnB;AACJ;AACO,MAAM,0BAA0B;IACnC,OAAO,OAAO,2BAA2B;IACzC,OAAO,2BAA2B;AACtC;AACO,MAAM,yBAAyB;IAClC,OAAO,OAAO,yBAAyB;IACvC,OAAO,yBAAyB;AACpC;AACO,MAAM,4BAA4B;IACrC,OAAO,OAAO,4BAA4B;IAC1C,OAAO,4BAA4B;IACnC,YAAY,UAAU,6BAA6B,EAAE,OAAO,CAAE;QAC1D,KAAK,CAAC,SAAS;IACnB;AACJ;AACO,MAAM,mBAAmB;IAC5B,OAAO,OAAO,kBAAkB;IAChC,OAAO,kBAAkB;AAC7B;AACO,MAAM,mBAAmB;IAC5B,OAAO,OAAO,kBAAkB;IAChC,OAAO,kBAAkB;AAC7B;AACO,MAAM,mBAAmB;IAC5B,OAAO,OAAO,kBAAkB;IAChC,OAAO,kBAAkB;AAC7B;AACO,MAAM,mBAAmB;IAC5B,OAAO,OAAO,kBAAkB;IAChC,OAAO,kBAAkB;AAC7B;AACO,MAAM,oBAAoB;IAC7B,OAAO,OAAO,mBAAmB;IACjC,OAAO,mBAAmB;AAC9B;AACO,MAAM,0BAA0B;IACnC,OAAO,OAAO,2BAA2B;IACzC,OAAO,2BAA2B;IAClC,YAAY,UAAU,iDAAiD,EAAE,OAAO,CAAE;QAC9E,KAAK,CAAC,SAAS;IACnB;AACJ;AACO,MAAM,iCAAiC;IAC1C,CAAC,OAAO,aAAa,CAAC,CAAC;IACvB,OAAO,OAAO,kCAAkC;IAChD,OAAO,kCAAkC;IACzC,YAAY,UAAU,sDAAsD,EAAE,OAAO,CAAE;QACnF,KAAK,CAAC,SAAS;IACnB;AACJ;AACO,MAAM,oBAAoB;IAC7B,OAAO,OAAO,mBAAmB;IACjC,OAAO,mBAAmB;IAC1B,YAAY,UAAU,mBAAmB,EAAE,OAAO,CAAE;QAChD,KAAK,CAAC,SAAS;IACnB;AACJ;AACO,MAAM,uCAAuC;IAChD,OAAO,OAAO,wCAAwC;IACtD,OAAO,wCAAwC;IAC/C,YAAY,UAAU,+BAA+B,EAAE,OAAO,CAAE;QAC5D,KAAK,CAAC,SAAS;IACnB;AACJ", "ignoreList": [0]}}, {"offset": {"line": 289, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/frontend/node_modules/.pnpm/jose@6.1.0/node_modules/jose/dist/webapi/lib/iv.js"], "sourcesContent": ["import { JOSENotSupported } from '../util/errors.js';\nexport function bitLength(alg) {\n    switch (alg) {\n        case 'A128GCM':\n        case 'A128GCMKW':\n        case 'A192GCM':\n        case 'A192GCMKW':\n        case 'A256GCM':\n        case 'A256GCMKW':\n            return 96;\n        case 'A128CBC-HS256':\n        case 'A192CBC-HS384':\n        case 'A256CBC-HS512':\n            return 128;\n        default:\n            throw new JOSENotSupported(`Unsupported JWE Algorithm: ${alg}`);\n    }\n}\nexport default (alg) => crypto.getRandomValues(new Uint8Array(bitLength(alg) >> 3));\n"], "names": [], "mappings": ";;;;;;AAAA;;AACO,SAAS,UAAU,GAAG;IACzB,OAAQ;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACD,OAAO;QACX,KAAK;QACL,KAAK;QACL,KAAK;YACD,OAAO;QACX;YACI,MAAM,IAAI,iPAAgB,CAAC,CAAC,2BAA2B,EAAE,KAAK;IACtE;AACJ;uCACe,CAAC,MAAQ,OAAO,eAAe,CAAC,IAAI,WAAW,UAAU,QAAQ", "ignoreList": [0]}}, {"offset": {"line": 319, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/frontend/node_modules/.pnpm/jose@6.1.0/node_modules/jose/dist/webapi/lib/check_iv_length.js"], "sourcesContent": ["import { JWEInvalid } from '../util/errors.js';\nimport { bitLength } from './iv.js';\nexport default (enc, iv) => {\n    if (iv.length << 3 !== bitLength(enc)) {\n        throw new JWEInvalid('Invalid Initialization Vector length');\n    }\n};\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;uCACe,CAAC,KAAK;IACjB,IAAI,GAAG,MAAM,IAAI,MAAM,IAAA,qOAAS,EAAC,MAAM;QACnC,MAAM,IAAI,2OAAU,CAAC;IACzB;AACJ", "ignoreList": [0]}}, {"offset": {"line": 336, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/frontend/node_modules/.pnpm/jose@6.1.0/node_modules/jose/dist/webapi/lib/check_cek_length.js"], "sourcesContent": ["import { JWEInvalid } from '../util/errors.js';\nexport default (cek, expected) => {\n    const actual = cek.byteLength << 3;\n    if (actual !== expected) {\n        throw new JWEInvalid(`Invalid Content Encryption Key length. Expected ${expected} bits, got ${actual} bits`);\n    }\n};\n"], "names": [], "mappings": ";;;;AAAA;;uCACe,CAAC,KAAK;IACjB,MAAM,SAAS,IAAI,UAAU,IAAI;IACjC,IAAI,WAAW,UAAU;QACrB,MAAM,IAAI,2OAAU,CAAC,CAAC,gDAAgD,EAAE,SAAS,WAAW,EAAE,OAAO,KAAK,CAAC;IAC/G;AACJ", "ignoreList": [0]}}, {"offset": {"line": 352, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/frontend/node_modules/.pnpm/jose@6.1.0/node_modules/jose/dist/webapi/lib/crypto_key.js"], "sourcesContent": ["function unusable(name, prop = 'algorithm.name') {\n    return new TypeError(`CryptoKey does not support this operation, its ${prop} must be ${name}`);\n}\nfunction isAlgorithm(algorithm, name) {\n    return algorithm.name === name;\n}\nfunction getHashLength(hash) {\n    return parseInt(hash.name.slice(4), 10);\n}\nfunction getNamedCurve(alg) {\n    switch (alg) {\n        case 'ES256':\n            return 'P-256';\n        case 'ES384':\n            return 'P-384';\n        case 'ES512':\n            return 'P-521';\n        default:\n            throw new Error('unreachable');\n    }\n}\nfunction checkUsage(key, usage) {\n    if (usage && !key.usages.includes(usage)) {\n        throw new TypeError(`CryptoKey does not support this operation, its usages must include ${usage}.`);\n    }\n}\nexport function checkSigCryptoKey(key, alg, usage) {\n    switch (alg) {\n        case 'HS256':\n        case 'HS384':\n        case 'HS512': {\n            if (!isAlgorithm(key.algorithm, 'HMAC'))\n                throw unusable('HMAC');\n            const expected = parseInt(alg.slice(2), 10);\n            const actual = getHashLength(key.algorithm.hash);\n            if (actual !== expected)\n                throw unusable(`SHA-${expected}`, 'algorithm.hash');\n            break;\n        }\n        case 'RS256':\n        case 'RS384':\n        case 'RS512': {\n            if (!isAlgorithm(key.algorithm, 'RSASSA-PKCS1-v1_5'))\n                throw unusable('RSASSA-PKCS1-v1_5');\n            const expected = parseInt(alg.slice(2), 10);\n            const actual = getHashLength(key.algorithm.hash);\n            if (actual !== expected)\n                throw unusable(`SHA-${expected}`, 'algorithm.hash');\n            break;\n        }\n        case 'PS256':\n        case 'PS384':\n        case 'PS512': {\n            if (!isAlgorithm(key.algorithm, 'RSA-PSS'))\n                throw unusable('RSA-PSS');\n            const expected = parseInt(alg.slice(2), 10);\n            const actual = getHashLength(key.algorithm.hash);\n            if (actual !== expected)\n                throw unusable(`SHA-${expected}`, 'algorithm.hash');\n            break;\n        }\n        case 'Ed25519':\n        case 'EdDSA': {\n            if (!isAlgorithm(key.algorithm, 'Ed25519'))\n                throw unusable('Ed25519');\n            break;\n        }\n        case 'ML-DSA-44':\n        case 'ML-DSA-65':\n        case 'ML-DSA-87': {\n            if (!isAlgorithm(key.algorithm, alg))\n                throw unusable(alg);\n            break;\n        }\n        case 'ES256':\n        case 'ES384':\n        case 'ES512': {\n            if (!isAlgorithm(key.algorithm, 'ECDSA'))\n                throw unusable('ECDSA');\n            const expected = getNamedCurve(alg);\n            const actual = key.algorithm.namedCurve;\n            if (actual !== expected)\n                throw unusable(expected, 'algorithm.namedCurve');\n            break;\n        }\n        default:\n            throw new TypeError('CryptoKey does not support this operation');\n    }\n    checkUsage(key, usage);\n}\nexport function checkEncCryptoKey(key, alg, usage) {\n    switch (alg) {\n        case 'A128GCM':\n        case 'A192GCM':\n        case 'A256GCM': {\n            if (!isAlgorithm(key.algorithm, 'AES-GCM'))\n                throw unusable('AES-GCM');\n            const expected = parseInt(alg.slice(1, 4), 10);\n            const actual = key.algorithm.length;\n            if (actual !== expected)\n                throw unusable(expected, 'algorithm.length');\n            break;\n        }\n        case 'A128KW':\n        case 'A192KW':\n        case 'A256KW': {\n            if (!isAlgorithm(key.algorithm, 'AES-KW'))\n                throw unusable('AES-KW');\n            const expected = parseInt(alg.slice(1, 4), 10);\n            const actual = key.algorithm.length;\n            if (actual !== expected)\n                throw unusable(expected, 'algorithm.length');\n            break;\n        }\n        case 'ECDH': {\n            switch (key.algorithm.name) {\n                case 'ECDH':\n                case 'X25519':\n                    break;\n                default:\n                    throw unusable('ECDH or X25519');\n            }\n            break;\n        }\n        case 'PBES2-HS256+A128KW':\n        case 'PBES2-HS384+A192KW':\n        case 'PBES2-HS512+A256KW':\n            if (!isAlgorithm(key.algorithm, 'PBKDF2'))\n                throw unusable('PBKDF2');\n            break;\n        case 'RSA-OAEP':\n        case 'RSA-OAEP-256':\n        case 'RSA-OAEP-384':\n        case 'RSA-OAEP-512': {\n            if (!isAlgorithm(key.algorithm, 'RSA-OAEP'))\n                throw unusable('RSA-OAEP');\n            const expected = parseInt(alg.slice(9), 10) || 1;\n            const actual = getHashLength(key.algorithm.hash);\n            if (actual !== expected)\n                throw unusable(`SHA-${expected}`, 'algorithm.hash');\n            break;\n        }\n        default:\n            throw new TypeError('CryptoKey does not support this operation');\n    }\n    checkUsage(key, usage);\n}\n"], "names": [], "mappings": ";;;;;;AAAA,SAAS,SAAS,IAAI,EAAE,OAAO,gBAAgB;IAC3C,OAAO,IAAI,UAAU,CAAC,+CAA+C,EAAE,KAAK,SAAS,EAAE,MAAM;AACjG;AACA,SAAS,YAAY,SAAS,EAAE,IAAI;IAChC,OAAO,UAAU,IAAI,KAAK;AAC9B;AACA,SAAS,cAAc,IAAI;IACvB,OAAO,SAAS,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI;AACxC;AACA,SAAS,cAAc,GAAG;IACtB,OAAQ;QACJ,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX;YACI,MAAM,IAAI,MAAM;IACxB;AACJ;AACA,SAAS,WAAW,GAAG,EAAE,KAAK;IAC1B,IAAI,SAAS,CAAC,IAAI,MAAM,CAAC,QAAQ,CAAC,QAAQ;QACtC,MAAM,IAAI,UAAU,CAAC,mEAAmE,EAAE,MAAM,CAAC,CAAC;IACtG;AACJ;AACO,SAAS,kBAAkB,GAAG,EAAE,GAAG,EAAE,KAAK;IAC7C,OAAQ;QACJ,KAAK;QACL,KAAK;QACL,KAAK;YAAS;gBACV,IAAI,CAAC,YAAY,IAAI,SAAS,EAAE,SAC5B,MAAM,SAAS;gBACnB,MAAM,WAAW,SAAS,IAAI,KAAK,CAAC,IAAI;gBACxC,MAAM,SAAS,cAAc,IAAI,SAAS,CAAC,IAAI;gBAC/C,IAAI,WAAW,UACX,MAAM,SAAS,CAAC,IAAI,EAAE,UAAU,EAAE;gBACtC;YACJ;QACA,KAAK;QACL,KAAK;QACL,KAAK;YAAS;gBACV,IAAI,CAAC,YAAY,IAAI,SAAS,EAAE,sBAC5B,MAAM,SAAS;gBACnB,MAAM,WAAW,SAAS,IAAI,KAAK,CAAC,IAAI;gBACxC,MAAM,SAAS,cAAc,IAAI,SAAS,CAAC,IAAI;gBAC/C,IAAI,WAAW,UACX,MAAM,SAAS,CAAC,IAAI,EAAE,UAAU,EAAE;gBACtC;YACJ;QACA,KAAK;QACL,KAAK;QACL,KAAK;YAAS;gBACV,IAAI,CAAC,YAAY,IAAI,SAAS,EAAE,YAC5B,MAAM,SAAS;gBACnB,MAAM,WAAW,SAAS,IAAI,KAAK,CAAC,IAAI;gBACxC,MAAM,SAAS,cAAc,IAAI,SAAS,CAAC,IAAI;gBAC/C,IAAI,WAAW,UACX,MAAM,SAAS,CAAC,IAAI,EAAE,UAAU,EAAE;gBACtC;YACJ;QACA,KAAK;QACL,KAAK;YAAS;gBACV,IAAI,CAAC,YAAY,IAAI,SAAS,EAAE,YAC5B,MAAM,SAAS;gBACnB;YACJ;QACA,KAAK;QACL,KAAK;QACL,KAAK;YAAa;gBACd,IAAI,CAAC,YAAY,IAAI,SAAS,EAAE,MAC5B,MAAM,SAAS;gBACnB;YACJ;QACA,KAAK;QACL,KAAK;QACL,KAAK;YAAS;gBACV,IAAI,CAAC,YAAY,IAAI,SAAS,EAAE,UAC5B,MAAM,SAAS;gBACnB,MAAM,WAAW,cAAc;gBAC/B,MAAM,SAAS,IAAI,SAAS,CAAC,UAAU;gBACvC,IAAI,WAAW,UACX,MAAM,SAAS,UAAU;gBAC7B;YACJ;QACA;YACI,MAAM,IAAI,UAAU;IAC5B;IACA,WAAW,KAAK;AACpB;AACO,SAAS,kBAAkB,GAAG,EAAE,GAAG,EAAE,KAAK;IAC7C,OAAQ;QACJ,KAAK;QACL,KAAK;QACL,KAAK;YAAW;gBACZ,IAAI,CAAC,YAAY,IAAI,SAAS,EAAE,YAC5B,MAAM,SAAS;gBACnB,MAAM,WAAW,SAAS,IAAI,KAAK,CAAC,GAAG,IAAI;gBAC3C,MAAM,SAAS,IAAI,SAAS,CAAC,MAAM;gBACnC,IAAI,WAAW,UACX,MAAM,SAAS,UAAU;gBAC7B;YACJ;QACA,KAAK;QACL,KAAK;QACL,KAAK;YAAU;gBACX,IAAI,CAAC,YAAY,IAAI,SAAS,EAAE,WAC5B,MAAM,SAAS;gBACnB,MAAM,WAAW,SAAS,IAAI,KAAK,CAAC,GAAG,IAAI;gBAC3C,MAAM,SAAS,IAAI,SAAS,CAAC,MAAM;gBACnC,IAAI,WAAW,UACX,MAAM,SAAS,UAAU;gBAC7B;YACJ;QACA,KAAK;YAAQ;gBACT,OAAQ,IAAI,SAAS,CAAC,IAAI;oBACtB,KAAK;oBACL,KAAK;wBACD;oBACJ;wBACI,MAAM,SAAS;gBACvB;gBACA;YACJ;QACA,KAAK;QACL,KAAK;QACL,KAAK;YACD,IAAI,CAAC,YAAY,IAAI,SAAS,EAAE,WAC5B,MAAM,SAAS;YACnB;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YAAgB;gBACjB,IAAI,CAAC,YAAY,IAAI,SAAS,EAAE,aAC5B,MAAM,SAAS;gBACnB,MAAM,WAAW,SAAS,IAAI,KAAK,CAAC,IAAI,OAAO;gBAC/C,MAAM,SAAS,cAAc,IAAI,SAAS,CAAC,IAAI;gBAC/C,IAAI,WAAW,UACX,MAAM,SAAS,CAAC,IAAI,EAAE,UAAU,EAAE;gBACtC;YACJ;QACA;YACI,MAAM,IAAI,UAAU;IAC5B;IACA,WAAW,KAAK;AACpB", "ignoreList": [0]}}, {"offset": {"line": 502, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/frontend/node_modules/.pnpm/jose@6.1.0/node_modules/jose/dist/webapi/lib/invalid_key_input.js"], "sourcesContent": ["function message(msg, actual, ...types) {\n    types = types.filter(<PERSON><PERSON><PERSON>);\n    if (types.length > 2) {\n        const last = types.pop();\n        msg += `one of type ${types.join(', ')}, or ${last}.`;\n    }\n    else if (types.length === 2) {\n        msg += `one of type ${types[0]} or ${types[1]}.`;\n    }\n    else {\n        msg += `of type ${types[0]}.`;\n    }\n    if (actual == null) {\n        msg += ` Received ${actual}`;\n    }\n    else if (typeof actual === 'function' && actual.name) {\n        msg += ` Received function ${actual.name}`;\n    }\n    else if (typeof actual === 'object' && actual != null) {\n        if (actual.constructor?.name) {\n            msg += ` Received an instance of ${actual.constructor.name}`;\n        }\n    }\n    return msg;\n}\nexport default (actual, ...types) => {\n    return message('Key must be ', actual, ...types);\n};\nexport function withAlg(alg, actual, ...types) {\n    return message(`Key for the ${alg} algorithm must be `, actual, ...types);\n}\n"], "names": [], "mappings": ";;;;;;AAAA,SAAS,QAAQ,GAAG,EAAE,MAAM,EAAE,GAAG,KAAK;IAClC,QAAQ,MAAM,MAAM,CAAC;IACrB,IAAI,MAAM,MAAM,GAAG,GAAG;QAClB,MAAM,OAAO,MAAM,GAAG;QACtB,OAAO,CAAC,YAAY,EAAE,MAAM,IAAI,CAAC,MAAM,KAAK,EAAE,KAAK,CAAC,CAAC;IACzD,OACK,IAAI,MAAM,MAAM,KAAK,GAAG;QACzB,OAAO,CAAC,YAAY,EAAE,KAAK,CAAC,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC;IACpD,OACK;QACD,OAAO,CAAC,QAAQ,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC;IACjC;IACA,IAAI,UAAU,MAAM;QAChB,OAAO,CAAC,UAAU,EAAE,QAAQ;IAChC,OACK,IAAI,OAAO,WAAW,cAAc,OAAO,IAAI,EAAE;QAClD,OAAO,CAAC,mBAAmB,EAAE,OAAO,IAAI,EAAE;IAC9C,OACK,IAAI,OAAO,WAAW,YAAY,UAAU,MAAM;QACnD,IAAI,OAAO,WAAW,EAAE,MAAM;YAC1B,OAAO,CAAC,yBAAyB,EAAE,OAAO,WAAW,CAAC,IAAI,EAAE;QAChE;IACJ;IACA,OAAO;AACX;uCACe,CAAC,QAAQ,GAAG;IACvB,OAAO,QAAQ,gBAAgB,WAAW;AAC9C;AACO,SAAS,QAAQ,GAAG,EAAE,MAAM,EAAE,GAAG,KAAK;IACzC,OAAO,QAAQ,CAAC,YAAY,EAAE,IAAI,mBAAmB,CAAC,EAAE,WAAW;AACvE", "ignoreList": [0]}}, {"offset": {"line": 539, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/frontend/node_modules/.pnpm/jose@6.1.0/node_modules/jose/dist/webapi/lib/is_key_like.js"], "sourcesContent": ["export function assertCrypto<PERSON><PERSON>(key) {\n    if (!isCrypto<PERSON>ey(key)) {\n        throw new Error('CryptoKey instance expected');\n    }\n}\nexport function isCrypto<PERSON>ey(key) {\n    return key?.[Symbol.toStringTag] === 'CryptoKey';\n}\nexport function isKeyObject(key) {\n    return key?.[Symbol.toStringTag] === 'KeyObject';\n}\nexport default (key) => {\n    return isCryptoKey(key) || isKeyObject(key);\n};\n"], "names": [], "mappings": ";;;;;;;;;;AAAO,SAAS,gBAAgB,GAAG;IAC/B,IAAI,CAAC,YAAY,MAAM;QACnB,MAAM,IAAI,MAAM;IACpB;AACJ;AACO,SAAS,YAAY,GAAG;IAC3B,OAAO,KAAK,CAAC,OAAO,WAAW,CAAC,KAAK;AACzC;AACO,SAAS,YAAY,GAAG;IAC3B,OAAO,KAAK,CAAC,OAAO,WAAW,CAAC,KAAK;AACzC;uCACe,CAAC;IACZ,OAAO,YAAY,QAAQ,YAAY;AAC3C", "ignoreList": [0]}}, {"offset": {"line": 567, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/frontend/node_modules/.pnpm/jose@6.1.0/node_modules/jose/dist/webapi/lib/encrypt.js"], "sourcesContent": ["import { concat, uint64be } from './buffer_utils.js';\nimport checkIvLength from './check_iv_length.js';\nimport checkCekLength from './check_cek_length.js';\nimport { checkEncCryptoKey } from './crypto_key.js';\nimport invalidKeyInput from './invalid_key_input.js';\nimport generateIv from './iv.js';\nimport { JOSENotSupported } from '../util/errors.js';\nimport { isCryptoKey } from './is_key_like.js';\nasync function cbcEncrypt(enc, plaintext, cek, iv, aad) {\n    if (!(cek instanceof Uint8Array)) {\n        throw new TypeError(invalidKeyInput(cek, 'Uint8Array'));\n    }\n    const keySize = parseInt(enc.slice(1, 4), 10);\n    const encKey = await crypto.subtle.importKey('raw', cek.subarray(keySize >> 3), 'AES-CBC', false, ['encrypt']);\n    const macKey = await crypto.subtle.importKey('raw', cek.subarray(0, keySize >> 3), {\n        hash: `SHA-${keySize << 1}`,\n        name: 'HMAC',\n    }, false, ['sign']);\n    const ciphertext = new Uint8Array(await crypto.subtle.encrypt({\n        iv,\n        name: 'AES-CBC',\n    }, encKey, plaintext));\n    const macData = concat(aad, iv, ciphertext, uint64be(aad.length << 3));\n    const tag = new Uint8Array((await crypto.subtle.sign('HMAC', macKey, macData)).slice(0, keySize >> 3));\n    return { ciphertext, tag, iv };\n}\nasync function gcmEncrypt(enc, plaintext, cek, iv, aad) {\n    let encKey;\n    if (cek instanceof Uint8Array) {\n        encKey = await crypto.subtle.importKey('raw', cek, 'AES-GCM', false, ['encrypt']);\n    }\n    else {\n        checkEncCryptoKey(cek, enc, 'encrypt');\n        encKey = cek;\n    }\n    const encrypted = new Uint8Array(await crypto.subtle.encrypt({\n        additionalData: aad,\n        iv,\n        name: 'AES-GCM',\n        tagLength: 128,\n    }, encKey, plaintext));\n    const tag = encrypted.slice(-16);\n    const ciphertext = encrypted.slice(0, -16);\n    return { ciphertext, tag, iv };\n}\nexport default async (enc, plaintext, cek, iv, aad) => {\n    if (!isCryptoKey(cek) && !(cek instanceof Uint8Array)) {\n        throw new TypeError(invalidKeyInput(cek, 'CryptoKey', 'KeyObject', 'Uint8Array', 'JSON Web Key'));\n    }\n    if (iv) {\n        checkIvLength(enc, iv);\n    }\n    else {\n        iv = generateIv(enc);\n    }\n    switch (enc) {\n        case 'A128CBC-HS256':\n        case 'A192CBC-HS384':\n        case 'A256CBC-HS512':\n            if (cek instanceof Uint8Array) {\n                checkCekLength(cek, parseInt(enc.slice(-3), 10));\n            }\n            return cbcEncrypt(enc, plaintext, cek, iv, aad);\n        case 'A128GCM':\n        case 'A192GCM':\n        case 'A256GCM':\n            if (cek instanceof Uint8Array) {\n                checkCekLength(cek, parseInt(enc.slice(1, 4), 10));\n            }\n            return gcmEncrypt(enc, plaintext, cek, iv, aad);\n        default:\n            throw new JOSENotSupported('Unsupported JWE Content Encryption Algorithm');\n    }\n};\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AACA,eAAe,WAAW,GAAG,EAAE,SAAS,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG;IAClD,IAAI,CAAC,CAAC,eAAe,UAAU,GAAG;QAC9B,MAAM,IAAI,UAAU,IAAA,kPAAe,EAAC,KAAK;IAC7C;IACA,MAAM,UAAU,SAAS,IAAI,KAAK,CAAC,GAAG,IAAI;IAC1C,MAAM,SAAS,MAAM,OAAO,MAAM,CAAC,SAAS,CAAC,OAAO,IAAI,QAAQ,CAAC,WAAW,IAAI,WAAW,OAAO;QAAC;KAAU;IAC7G,MAAM,SAAS,MAAM,OAAO,MAAM,CAAC,SAAS,CAAC,OAAO,IAAI,QAAQ,CAAC,GAAG,WAAW,IAAI;QAC/E,MAAM,CAAC,IAAI,EAAE,WAAW,GAAG;QAC3B,MAAM;IACV,GAAG,OAAO;QAAC;KAAO;IAClB,MAAM,aAAa,IAAI,WAAW,MAAM,OAAO,MAAM,CAAC,OAAO,CAAC;QAC1D;QACA,MAAM;IACV,GAAG,QAAQ;IACX,MAAM,UAAU,IAAA,4OAAM,EAAC,KAAK,IAAI,YAAY,IAAA,8OAAQ,EAAC,IAAI,MAAM,IAAI;IACnE,MAAM,MAAM,IAAI,WAAW,CAAC,MAAM,OAAO,MAAM,CAAC,IAAI,CAAC,QAAQ,QAAQ,QAAQ,EAAE,KAAK,CAAC,GAAG,WAAW;IACnG,OAAO;QAAE;QAAY;QAAK;IAAG;AACjC;AACA,eAAe,WAAW,GAAG,EAAE,SAAS,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG;IAClD,IAAI;IACJ,IAAI,eAAe,YAAY;QAC3B,SAAS,MAAM,OAAO,MAAM,CAAC,SAAS,CAAC,OAAO,KAAK,WAAW,OAAO;YAAC;SAAU;IACpF,OACK;QACD,IAAA,qPAAiB,EAAC,KAAK,KAAK;QAC5B,SAAS;IACb;IACA,MAAM,YAAY,IAAI,WAAW,MAAM,OAAO,MAAM,CAAC,OAAO,CAAC;QACzD,gBAAgB;QAChB;QACA,MAAM;QACN,WAAW;IACf,GAAG,QAAQ;IACX,MAAM,MAAM,UAAU,KAAK,CAAC,CAAC;IAC7B,MAAM,aAAa,UAAU,KAAK,CAAC,GAAG,CAAC;IACvC,OAAO;QAAE;QAAY;QAAK;IAAG;AACjC;uCACe,OAAO,KAAK,WAAW,KAAK,IAAI;IAC3C,IAAI,CAAC,IAAA,gPAAW,EAAC,QAAQ,CAAC,CAAC,eAAe,UAAU,GAAG;QACnD,MAAM,IAAI,UAAU,IAAA,kPAAe,EAAC,KAAK,aAAa,aAAa,cAAc;IACrF;IACA,IAAI,IAAI;QACJ,IAAA,gPAAa,EAAC,KAAK;IACvB,OACK;QACD,KAAK,IAAA,mOAAU,EAAC;IACpB;IACA,OAAQ;QACJ,KAAK;QACL,KAAK;QACL,KAAK;YACD,IAAI,eAAe,YAAY;gBAC3B,IAAA,iPAAc,EAAC,KAAK,SAAS,IAAI,KAAK,CAAC,CAAC,IAAI;YAChD;YACA,OAAO,WAAW,KAAK,WAAW,KAAK,IAAI;QAC/C,KAAK;QACL,KAAK;QACL,KAAK;YACD,IAAI,eAAe,YAAY;gBAC3B,IAAA,iPAAc,EAAC,KAAK,SAAS,IAAI,KAAK,CAAC,GAAG,IAAI;YAClD;YACA,OAAO,WAAW,KAAK,WAAW,KAAK,IAAI;QAC/C;YACI,MAAM,IAAI,iPAAgB,CAAC;IACnC;AACJ", "ignoreList": [0]}}, {"offset": {"line": 669, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/frontend/node_modules/.pnpm/jose@6.1.0/node_modules/jose/dist/webapi/lib/aeskw.js"], "sourcesContent": ["import { checkEnc<PERSON>rypto<PERSON><PERSON> } from './crypto_key.js';\nfunction checkKeySize(key, alg) {\n    if (key.algorithm.length !== parseInt(alg.slice(1, 4), 10)) {\n        throw new TypeError(`Invalid key size for alg: ${alg}`);\n    }\n}\nfunction getCryptoKey(key, alg, usage) {\n    if (key instanceof Uint8Array) {\n        return crypto.subtle.importKey('raw', key, 'AES-KW', true, [usage]);\n    }\n    checkEncCryptoKey(key, alg, usage);\n    return key;\n}\nexport async function wrap(alg, key, cek) {\n    const cryptoKey = await getCryptoKey(key, alg, 'wrapKey');\n    checkKeySize(cryptoKey, alg);\n    const cryptoKeyCek = await crypto.subtle.importKey('raw', cek, { hash: 'SHA-256', name: 'HMAC' }, true, ['sign']);\n    return new Uint8Array(await crypto.subtle.wrapKey('raw', cryptoKeyCek, cryptoKey, 'AES-KW'));\n}\nexport async function unwrap(alg, key, encryptedKey) {\n    const cryptoKey = await getCryptoKey(key, alg, 'unwrapKey');\n    checkKeySize(cryptoKey, alg);\n    const cryptoKeyCek = await crypto.subtle.unwrapKey('raw', encryptedKey, cryptoKey, 'AES-KW', { hash: 'SHA-256', name: 'HMAC' }, true, ['sign']);\n    return new Uint8Array(await crypto.subtle.exportKey('raw', cryptoKeyCek));\n}\n"], "names": [], "mappings": ";;;;;;AAAA;;AACA,SAAS,aAAa,GAAG,EAAE,GAAG;IAC1B,IAAI,IAAI,SAAS,CAAC,MAAM,KAAK,SAAS,IAAI,KAAK,CAAC,GAAG,IAAI,KAAK;QACxD,MAAM,IAAI,UAAU,CAAC,0BAA0B,EAAE,KAAK;IAC1D;AACJ;AACA,SAAS,aAAa,GAAG,EAAE,GAAG,EAAE,KAAK;IACjC,IAAI,eAAe,YAAY;QAC3B,OAAO,OAAO,MAAM,CAAC,SAAS,CAAC,OAAO,KAAK,UAAU,MAAM;YAAC;SAAM;IACtE;IACA,IAAA,qPAAiB,EAAC,KAAK,KAAK;IAC5B,OAAO;AACX;AACO,eAAe,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG;IACpC,MAAM,YAAY,MAAM,aAAa,KAAK,KAAK;IAC/C,aAAa,WAAW;IACxB,MAAM,eAAe,MAAM,OAAO,MAAM,CAAC,SAAS,CAAC,OAAO,KAAK;QAAE,MAAM;QAAW,MAAM;IAAO,GAAG,MAAM;QAAC;KAAO;IAChH,OAAO,IAAI,WAAW,MAAM,OAAO,MAAM,CAAC,OAAO,CAAC,OAAO,cAAc,WAAW;AACtF;AACO,eAAe,OAAO,GAAG,EAAE,GAAG,EAAE,YAAY;IAC/C,MAAM,YAAY,MAAM,aAAa,KAAK,KAAK;IAC/C,aAAa,WAAW;IACxB,MAAM,eAAe,MAAM,OAAO,MAAM,CAAC,SAAS,CAAC,OAAO,cAAc,WAAW,UAAU;QAAE,MAAM;QAAW,MAAM;IAAO,GAAG,MAAM;QAAC;KAAO;IAC9I,OAAO,IAAI,WAAW,MAAM,OAAO,MAAM,CAAC,SAAS,CAAC,OAAO;AAC/D", "ignoreList": [0]}}, {"offset": {"line": 717, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/frontend/node_modules/.pnpm/jose@6.1.0/node_modules/jose/dist/webapi/lib/digest.js"], "sourcesContent": ["export default async (algorithm, data) => {\n    const subtleDigest = `SHA-${algorithm.slice(-3)}`;\n    return new Uint8Array(await crypto.subtle.digest(subtleDigest, data));\n};\n"], "names": [], "mappings": ";;;;uCAAe,OAAO,WAAW;IAC7B,MAAM,eAAe,CAAC,IAAI,EAAE,UAAU,KAAK,CAAC,CAAC,IAAI;IACjD,OAAO,IAAI,WAAW,MAAM,OAAO,MAAM,CAAC,MAAM,CAAC,cAAc;AACnE", "ignoreList": [0]}}, {"offset": {"line": 729, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/frontend/node_modules/.pnpm/jose@6.1.0/node_modules/jose/dist/webapi/lib/ecdhes.js"], "sourcesContent": ["import { encoder, concat, uint32be } from './buffer_utils.js';\nimport { checkEncCryptoKey } from './crypto_key.js';\nimport digest from './digest.js';\nfunction lengthAndInput(input) {\n    return concat(uint32be(input.length), input);\n}\nasync function concatKdf(Z, L, OtherInfo) {\n    const dkLen = L >> 3;\n    const hashLen = 32;\n    const reps = Math.ceil(dkLen / hashLen);\n    const dk = new Uint8Array(reps * hashLen);\n    for (let i = 1; i <= reps; i++) {\n        const hashInput = new Uint8Array(4 + Z.length + OtherInfo.length);\n        hashInput.set(uint32be(i), 0);\n        hashInput.set(Z, 4);\n        hashInput.set(OtherInfo, 4 + Z.length);\n        const hashResult = await digest('sha256', hashInput);\n        dk.set(hashResult, (i - 1) * hashLen);\n    }\n    return dk.slice(0, dkLen);\n}\nexport async function deriveKey(publicKey, privateKey, algorithm, keyLength, apu = new Uint8Array(0), apv = new Uint8Array(0)) {\n    checkEncCryptoKey(publicKey, 'ECDH');\n    checkEncCryptoKey(privateKey, 'ECDH', 'deriveBits');\n    const algorithmID = lengthAndInput(encoder.encode(algorithm));\n    const partyUInfo = lengthAndInput(apu);\n    const partyVInfo = lengthAndInput(apv);\n    const suppPubInfo = uint32be(keyLength);\n    const suppPrivInfo = new Uint8Array(0);\n    const otherInfo = concat(algorithmID, partyUInfo, partyVInfo, suppPubInfo, suppPrivInfo);\n    const Z = new Uint8Array(await crypto.subtle.deriveBits({\n        name: publicKey.algorithm.name,\n        public: publicKey,\n    }, privateKey, getEcdhBitLength(publicKey)));\n    return concatKdf(Z, keyLength, otherInfo);\n}\nfunction getEcdhBitLength(publicKey) {\n    if (publicKey.algorithm.name === 'X25519') {\n        return 256;\n    }\n    return (Math.ceil(parseInt(publicKey.algorithm.namedCurve.slice(-3), 10) / 8) << 3);\n}\nexport function allowed(key) {\n    switch (key.algorithm.namedCurve) {\n        case 'P-256':\n        case 'P-384':\n        case 'P-521':\n            return true;\n        default:\n            return key.algorithm.name === 'X25519';\n    }\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;;;;AACA,SAAS,eAAe,KAAK;IACzB,OAAO,IAAA,4OAAM,EAAC,IAAA,8OAAQ,EAAC,MAAM,MAAM,GAAG;AAC1C;AACA,eAAe,UAAU,CAAC,EAAE,CAAC,EAAE,SAAS;IACpC,MAAM,QAAQ,KAAK;IACnB,MAAM,UAAU;IAChB,MAAM,OAAO,KAAK,IAAI,CAAC,QAAQ;IAC/B,MAAM,KAAK,IAAI,WAAW,OAAO;IACjC,IAAK,IAAI,IAAI,GAAG,KAAK,MAAM,IAAK;QAC5B,MAAM,YAAY,IAAI,WAAW,IAAI,EAAE,MAAM,GAAG,UAAU,MAAM;QAChE,UAAU,GAAG,CAAC,IAAA,8OAAQ,EAAC,IAAI;QAC3B,UAAU,GAAG,CAAC,GAAG;QACjB,UAAU,GAAG,CAAC,WAAW,IAAI,EAAE,MAAM;QACrC,MAAM,aAAa,MAAM,IAAA,uOAAM,EAAC,UAAU;QAC1C,GAAG,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI;IACjC;IACA,OAAO,GAAG,KAAK,CAAC,GAAG;AACvB;AACO,eAAe,UAAU,SAAS,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,IAAI,WAAW,EAAE,EAAE,MAAM,IAAI,WAAW,EAAE;IACzH,IAAA,qPAAiB,EAAC,WAAW;IAC7B,IAAA,qPAAiB,EAAC,YAAY,QAAQ;IACtC,MAAM,cAAc,eAAe,6OAAO,CAAC,MAAM,CAAC;IAClD,MAAM,aAAa,eAAe;IAClC,MAAM,aAAa,eAAe;IAClC,MAAM,cAAc,IAAA,8OAAQ,EAAC;IAC7B,MAAM,eAAe,IAAI,WAAW;IACpC,MAAM,YAAY,IAAA,4OAAM,EAAC,aAAa,YAAY,YAAY,aAAa;IAC3E,MAAM,IAAI,IAAI,WAAW,MAAM,OAAO,MAAM,CAAC,UAAU,CAAC;QACpD,MAAM,UAAU,SAAS,CAAC,IAAI;QAC9B,QAAQ;IACZ,GAAG,YAAY,iBAAiB;IAChC,OAAO,UAAU,GAAG,WAAW;AACnC;AACA,SAAS,iBAAiB,SAAS;IAC/B,IAAI,UAAU,SAAS,CAAC,IAAI,KAAK,UAAU;QACvC,OAAO;IACX;IACA,OAAQ,KAAK,IAAI,CAAC,SAAS,UAAU,SAAS,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,IAAI,MAAM,MAAM;AACrF;AACO,SAAS,QAAQ,GAAG;IACvB,OAAQ,IAAI,SAAS,CAAC,UAAU;QAC5B,KAAK;QACL,KAAK;QACL,KAAK;YACD,OAAO;QACX;YACI,OAAO,IAAI,SAAS,CAAC,IAAI,KAAK;IACtC;AACJ", "ignoreList": [0]}}, {"offset": {"line": 794, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/frontend/node_modules/.pnpm/jose@6.1.0/node_modules/jose/dist/webapi/lib/pbes2kw.js"], "sourcesContent": ["import { encode as b64u } from '../util/base64url.js';\nimport * as aeskw from './aeskw.js';\nimport { checkEncCryptoKey } from './crypto_key.js';\nimport { concat, encoder } from './buffer_utils.js';\nimport { JWEInvalid } from '../util/errors.js';\nfunction getCryptoKey(key, alg) {\n    if (key instanceof Uint8Array) {\n        return crypto.subtle.importKey('raw', key, 'PBKDF2', false, ['deriveBits']);\n    }\n    checkEncCryptoKey(key, alg, 'deriveBits');\n    return key;\n}\nconst concatSalt = (alg, p2sInput) => concat(encoder.encode(alg), new Uint8Array([0]), p2sInput);\nasync function deriveKey(p2s, alg, p2c, key) {\n    if (!(p2s instanceof Uint8Array) || p2s.length < 8) {\n        throw new JWEInvalid('PBES2 Salt Input must be 8 or more octets');\n    }\n    const salt = concatSalt(alg, p2s);\n    const keylen = parseInt(alg.slice(13, 16), 10);\n    const subtleAlg = {\n        hash: `SHA-${alg.slice(8, 11)}`,\n        iterations: p2c,\n        name: 'PBKDF2',\n        salt,\n    };\n    const cryptoKey = await getCryptoKey(key, alg);\n    return new Uint8Array(await crypto.subtle.deriveBits(subtleAlg, cryptoKey, keylen));\n}\nexport async function wrap(alg, key, cek, p2c = 2048, p2s = crypto.getRandomValues(new Uint8Array(16))) {\n    const derived = await deriveKey(p2s, alg, p2c, key);\n    const encryptedKey = await aeskw.wrap(alg.slice(-6), derived, cek);\n    return { encryptedKey, p2c, p2s: b64u(p2s) };\n}\nexport async function unwrap(alg, key, encryptedKey, p2c, p2s) {\n    const derived = await deriveKey(p2s, alg, p2c, key);\n    return aeskw.unwrap(alg.slice(-6), derived, encryptedKey);\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;;;;;;AACA,SAAS,aAAa,GAAG,EAAE,GAAG;IAC1B,IAAI,eAAe,YAAY;QAC3B,OAAO,OAAO,MAAM,CAAC,SAAS,CAAC,OAAO,KAAK,UAAU,OAAO;YAAC;SAAa;IAC9E;IACA,IAAA,qPAAiB,EAAC,KAAK,KAAK;IAC5B,OAAO;AACX;AACA,MAAM,aAAa,CAAC,KAAK,WAAa,IAAA,4OAAM,EAAC,6OAAO,CAAC,MAAM,CAAC,MAAM,IAAI,WAAW;QAAC;KAAE,GAAG;AACvF,eAAe,UAAU,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;IACvC,IAAI,CAAC,CAAC,eAAe,UAAU,KAAK,IAAI,MAAM,GAAG,GAAG;QAChD,MAAM,IAAI,2OAAU,CAAC;IACzB;IACA,MAAM,OAAO,WAAW,KAAK;IAC7B,MAAM,SAAS,SAAS,IAAI,KAAK,CAAC,IAAI,KAAK;IAC3C,MAAM,YAAY;QACd,MAAM,CAAC,IAAI,EAAE,IAAI,KAAK,CAAC,GAAG,KAAK;QAC/B,YAAY;QACZ,MAAM;QACN;IACJ;IACA,MAAM,YAAY,MAAM,aAAa,KAAK;IAC1C,OAAO,IAAI,WAAW,MAAM,OAAO,MAAM,CAAC,UAAU,CAAC,WAAW,WAAW;AAC/E;AACO,eAAe,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,MAAM,IAAI,EAAE,MAAM,OAAO,eAAe,CAAC,IAAI,WAAW,IAAI;IAClG,MAAM,UAAU,MAAM,UAAU,KAAK,KAAK,KAAK;IAC/C,MAAM,eAAe,MAAM,mOAAU,CAAC,IAAI,KAAK,CAAC,CAAC,IAAI,SAAS;IAC9D,OAAO;QAAE;QAAc;QAAK,KAAK,IAAA,0OAAI,EAAC;IAAK;AAC/C;AACO,eAAe,OAAO,GAAG,EAAE,GAAG,EAAE,YAAY,EAAE,GAAG,EAAE,GAAG;IACzD,MAAM,UAAU,MAAM,UAAU,KAAK,KAAK,KAAK;IAC/C,OAAO,qOAAY,CAAC,IAAI,KAAK,CAAC,CAAC,IAAI,SAAS;AAChD", "ignoreList": [0]}}, {"offset": {"line": 854, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/frontend/node_modules/.pnpm/jose@6.1.0/node_modules/jose/dist/webapi/lib/check_key_length.js"], "sourcesContent": ["export default (alg, key) => {\n    if (alg.startsWith('RS') || alg.startsWith('PS')) {\n        const { modulusLength } = key.algorithm;\n        if (typeof modulusLength !== 'number' || modulusLength < 2048) {\n            throw new TypeError(`${alg} requires key modulusLength to be 2048 bits or larger`);\n        }\n    }\n};\n"], "names": [], "mappings": ";;;;uCAAe,CAAC,KAAK;IACjB,IAAI,IAAI,UAAU,CAAC,SAAS,IAAI,UAAU,CAAC,OAAO;QAC9C,MAAM,EAAE,aAAa,EAAE,GAAG,IAAI,SAAS;QACvC,IAAI,OAAO,kBAAkB,YAAY,gBAAgB,MAAM;YAC3D,MAAM,IAAI,UAAU,GAAG,IAAI,qDAAqD,CAAC;QACrF;IACJ;AACJ", "ignoreList": [0]}}, {"offset": {"line": 870, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/frontend/node_modules/.pnpm/jose@6.1.0/node_modules/jose/dist/webapi/lib/rsaes.js"], "sourcesContent": ["import { checkEnc<PERSON>rypto<PERSON><PERSON> } from './crypto_key.js';\nimport checkKeyLength from './check_key_length.js';\nimport { JOSENotSupported } from '../util/errors.js';\nconst subtleAlgorithm = (alg) => {\n    switch (alg) {\n        case 'RSA-OAEP':\n        case 'RSA-OAEP-256':\n        case 'RSA-OAEP-384':\n        case 'RSA-OAEP-512':\n            return 'RSA-OAEP';\n        default:\n            throw new JOSENotSupported(`alg ${alg} is not supported either by JOSE or your javascript runtime`);\n    }\n};\nexport async function encrypt(alg, key, cek) {\n    checkEncCryptoKey(key, alg, 'encrypt');\n    checkKeyLength(alg, key);\n    return new Uint8Array(await crypto.subtle.encrypt(subtleAlgorithm(alg), key, cek));\n}\nexport async function decrypt(alg, key, encryptedKey) {\n    checkEncCryptoKey(key, alg, 'decrypt');\n    checkKeyLength(alg, key);\n    return new Uint8Array(await crypto.subtle.decrypt(subtleAlgorithm(alg), key, encryptedKey));\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;;;;AACA,MAAM,kBAAkB,CAAC;IACrB,OAAQ;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACD,OAAO;QACX;YACI,MAAM,IAAI,iPAAgB,CAAC,CAAC,IAAI,EAAE,IAAI,2DAA2D,CAAC;IAC1G;AACJ;AACO,eAAe,QAAQ,GAAG,EAAE,GAAG,EAAE,GAAG;IACvC,IAAA,qPAAiB,EAAC,KAAK,KAAK;IAC5B,IAAA,iPAAc,EAAC,KAAK;IACpB,OAAO,IAAI,WAAW,MAAM,OAAO,MAAM,CAAC,OAAO,CAAC,gBAAgB,MAAM,KAAK;AACjF;AACO,eAAe,QAAQ,GAAG,EAAE,GAAG,EAAE,YAAY;IAChD,IAAA,qPAAiB,EAAC,KAAK,KAAK;IAC5B,IAAA,iPAAc,EAAC,KAAK;IACpB,OAAO,IAAI,WAAW,MAAM,OAAO,MAAM,CAAC,OAAO,CAAC,gBAAgB,MAAM,KAAK;AACjF", "ignoreList": [0]}}, {"offset": {"line": 907, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/frontend/node_modules/.pnpm/jose@6.1.0/node_modules/jose/dist/webapi/lib/is_object.js"], "sourcesContent": ["function isObjectLike(value) {\n    return typeof value === 'object' && value !== null;\n}\nexport default (input) => {\n    if (!isObjectLike(input) || Object.prototype.toString.call(input) !== '[object Object]') {\n        return false;\n    }\n    if (Object.getPrototypeOf(input) === null) {\n        return true;\n    }\n    let proto = input;\n    while (Object.getPrototypeOf(proto) !== null) {\n        proto = Object.getPrototypeOf(proto);\n    }\n    return Object.getPrototypeOf(input) === proto;\n};\n"], "names": [], "mappings": ";;;;AAAA,SAAS,aAAa,KAAK;IACvB,OAAO,OAAO,UAAU,YAAY,UAAU;AAClD;uCACe,CAAC;IACZ,IAAI,CAAC,aAAa,UAAU,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,mBAAmB;QACrF,OAAO;IACX;IACA,IAAI,OAAO,cAAc,CAAC,WAAW,MAAM;QACvC,OAAO;IACX;IACA,IAAI,QAAQ;IACZ,MAAO,OAAO,cAAc,CAAC,WAAW,KAAM;QAC1C,QAAQ,OAAO,cAAc,CAAC;IAClC;IACA,OAAO,OAAO,cAAc,CAAC,WAAW;AAC5C", "ignoreList": [0]}}, {"offset": {"line": 931, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/frontend/node_modules/.pnpm/jose@6.1.0/node_modules/jose/dist/webapi/lib/is_jwk.js"], "sourcesContent": ["import isObject from './is_object.js';\nexport function isJWK(key) {\n    return isObject(key) && typeof key.kty === 'string';\n}\nexport function isPrivateJWK(key) {\n    return (key.kty !== 'oct' &&\n        ((key.kty === 'AKP' && typeof key.priv === 'string') || typeof key.d === 'string'));\n}\nexport function isPublicJWK(key) {\n    return key.kty !== 'oct' && typeof key.d === 'undefined' && typeof key.priv === 'undefined';\n}\nexport function isSecretJWK(key) {\n    return key.kty === 'oct' && typeof key.k === 'string';\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;AACO,SAAS,MAAM,GAAG;IACrB,OAAO,IAAA,0OAAQ,EAAC,QAAQ,OAAO,IAAI,GAAG,KAAK;AAC/C;AACO,SAAS,aAAa,GAAG;IAC5B,OAAQ,IAAI,GAAG,KAAK,SAChB,CAAC,AAAC,IAAI,GAAG,KAAK,SAAS,OAAO,IAAI,IAAI,KAAK,YAAa,OAAO,IAAI,CAAC,KAAK,QAAQ;AACzF;AACO,SAAS,YAAY,GAAG;IAC3B,OAAO,IAAI,GAAG,KAAK,SAAS,OAAO,IAAI,CAAC,KAAK,eAAe,OAAO,IAAI,IAAI,KAAK;AACpF;AACO,SAAS,YAAY,GAAG;IAC3B,OAAO,IAAI,GAAG,KAAK,SAAS,OAAO,IAAI,CAAC,KAAK;AACjD", "ignoreList": [0]}}, {"offset": {"line": 959, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/frontend/node_modules/.pnpm/jose@6.1.0/node_modules/jose/dist/webapi/lib/jwk_to_key.js"], "sourcesContent": ["import { JOSENotSupported } from '../util/errors.js';\nfunction subtleMapping(jwk) {\n    let algorithm;\n    let keyUsages;\n    switch (jwk.kty) {\n        case 'AKP': {\n            switch (jwk.alg) {\n                case 'ML-DSA-44':\n                case 'ML-DSA-65':\n                case 'ML-DSA-87':\n                    algorithm = { name: jwk.alg };\n                    keyUsages = jwk.priv ? ['sign'] : ['verify'];\n                    break;\n                default:\n                    throw new JOSENotSupported('Invalid or unsupported JWK \"alg\" (Algorithm) Parameter value');\n            }\n            break;\n        }\n        case 'RSA': {\n            switch (jwk.alg) {\n                case 'PS256':\n                case 'PS384':\n                case 'PS512':\n                    algorithm = { name: 'RSA-PSS', hash: `SHA-${jwk.alg.slice(-3)}` };\n                    keyUsages = jwk.d ? ['sign'] : ['verify'];\n                    break;\n                case 'RS256':\n                case 'RS384':\n                case 'RS512':\n                    algorithm = { name: 'RSASSA-PKCS1-v1_5', hash: `SHA-${jwk.alg.slice(-3)}` };\n                    keyUsages = jwk.d ? ['sign'] : ['verify'];\n                    break;\n                case 'RSA-OAEP':\n                case 'RSA-OAEP-256':\n                case 'RSA-OAEP-384':\n                case 'RSA-OAEP-512':\n                    algorithm = {\n                        name: 'RSA-OAEP',\n                        hash: `SHA-${parseInt(jwk.alg.slice(-3), 10) || 1}`,\n                    };\n                    keyUsages = jwk.d ? ['decrypt', 'unwrapKey'] : ['encrypt', 'wrapKey'];\n                    break;\n                default:\n                    throw new JOSENotSupported('Invalid or unsupported JWK \"alg\" (Algorithm) Parameter value');\n            }\n            break;\n        }\n        case 'EC': {\n            switch (jwk.alg) {\n                case 'ES256':\n                    algorithm = { name: 'ECDSA', namedCurve: 'P-256' };\n                    keyUsages = jwk.d ? ['sign'] : ['verify'];\n                    break;\n                case 'ES384':\n                    algorithm = { name: 'ECDSA', namedCurve: 'P-384' };\n                    keyUsages = jwk.d ? ['sign'] : ['verify'];\n                    break;\n                case 'ES512':\n                    algorithm = { name: 'ECDSA', namedCurve: 'P-521' };\n                    keyUsages = jwk.d ? ['sign'] : ['verify'];\n                    break;\n                case 'ECDH-ES':\n                case 'ECDH-ES+A128KW':\n                case 'ECDH-ES+A192KW':\n                case 'ECDH-ES+A256KW':\n                    algorithm = { name: 'ECDH', namedCurve: jwk.crv };\n                    keyUsages = jwk.d ? ['deriveBits'] : [];\n                    break;\n                default:\n                    throw new JOSENotSupported('Invalid or unsupported JWK \"alg\" (Algorithm) Parameter value');\n            }\n            break;\n        }\n        case 'OKP': {\n            switch (jwk.alg) {\n                case 'Ed25519':\n                case 'EdDSA':\n                    algorithm = { name: 'Ed25519' };\n                    keyUsages = jwk.d ? ['sign'] : ['verify'];\n                    break;\n                case 'ECDH-ES':\n                case 'ECDH-ES+A128KW':\n                case 'ECDH-ES+A192KW':\n                case 'ECDH-ES+A256KW':\n                    algorithm = { name: jwk.crv };\n                    keyUsages = jwk.d ? ['deriveBits'] : [];\n                    break;\n                default:\n                    throw new JOSENotSupported('Invalid or unsupported JWK \"alg\" (Algorithm) Parameter value');\n            }\n            break;\n        }\n        default:\n            throw new JOSENotSupported('Invalid or unsupported JWK \"kty\" (Key Type) Parameter value');\n    }\n    return { algorithm, keyUsages };\n}\nexport default async (jwk) => {\n    if (!jwk.alg) {\n        throw new TypeError('\"alg\" argument is required when \"jwk.alg\" is not present');\n    }\n    const { algorithm, keyUsages } = subtleMapping(jwk);\n    const keyData = { ...jwk };\n    if (keyData.kty !== 'AKP') {\n        delete keyData.alg;\n    }\n    delete keyData.use;\n    return crypto.subtle.importKey('jwk', keyData, algorithm, jwk.ext ?? (jwk.d || jwk.priv ? false : true), jwk.key_ops ?? keyUsages);\n};\n"], "names": [], "mappings": ";;;;AAAA;;AACA,SAAS,cAAc,GAAG;IACtB,IAAI;IACJ,IAAI;IACJ,OAAQ,IAAI,GAAG;QACX,KAAK;YAAO;gBACR,OAAQ,IAAI,GAAG;oBACX,KAAK;oBACL,KAAK;oBACL,KAAK;wBACD,YAAY;4BAAE,MAAM,IAAI,GAAG;wBAAC;wBAC5B,YAAY,IAAI,IAAI,GAAG;4BAAC;yBAAO,GAAG;4BAAC;yBAAS;wBAC5C;oBACJ;wBACI,MAAM,IAAI,iPAAgB,CAAC;gBACnC;gBACA;YACJ;QACA,KAAK;YAAO;gBACR,OAAQ,IAAI,GAAG;oBACX,KAAK;oBACL,KAAK;oBACL,KAAK;wBACD,YAAY;4BAAE,MAAM;4BAAW,MAAM,CAAC,IAAI,EAAE,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,IAAI;wBAAC;wBAChE,YAAY,IAAI,CAAC,GAAG;4BAAC;yBAAO,GAAG;4BAAC;yBAAS;wBACzC;oBACJ,KAAK;oBACL,KAAK;oBACL,KAAK;wBACD,YAAY;4BAAE,MAAM;4BAAqB,MAAM,CAAC,IAAI,EAAE,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,IAAI;wBAAC;wBAC1E,YAAY,IAAI,CAAC,GAAG;4BAAC;yBAAO,GAAG;4BAAC;yBAAS;wBACzC;oBACJ,KAAK;oBACL,KAAK;oBACL,KAAK;oBACL,KAAK;wBACD,YAAY;4BACR,MAAM;4BACN,MAAM,CAAC,IAAI,EAAE,SAAS,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,IAAI,OAAO,GAAG;wBACvD;wBACA,YAAY,IAAI,CAAC,GAAG;4BAAC;4BAAW;yBAAY,GAAG;4BAAC;4BAAW;yBAAU;wBACrE;oBACJ;wBACI,MAAM,IAAI,iPAAgB,CAAC;gBACnC;gBACA;YACJ;QACA,KAAK;YAAM;gBACP,OAAQ,IAAI,GAAG;oBACX,KAAK;wBACD,YAAY;4BAAE,MAAM;4BAAS,YAAY;wBAAQ;wBACjD,YAAY,IAAI,CAAC,GAAG;4BAAC;yBAAO,GAAG;4BAAC;yBAAS;wBACzC;oBACJ,KAAK;wBACD,YAAY;4BAAE,MAAM;4BAAS,YAAY;wBAAQ;wBACjD,YAAY,IAAI,CAAC,GAAG;4BAAC;yBAAO,GAAG;4BAAC;yBAAS;wBACzC;oBACJ,KAAK;wBACD,YAAY;4BAAE,MAAM;4BAAS,YAAY;wBAAQ;wBACjD,YAAY,IAAI,CAAC,GAAG;4BAAC;yBAAO,GAAG;4BAAC;yBAAS;wBACzC;oBACJ,KAAK;oBACL,KAAK;oBACL,KAAK;oBACL,KAAK;wBACD,YAAY;4BAAE,MAAM;4BAAQ,YAAY,IAAI,GAAG;wBAAC;wBAChD,YAAY,IAAI,CAAC,GAAG;4BAAC;yBAAa,GAAG,EAAE;wBACvC;oBACJ;wBACI,MAAM,IAAI,iPAAgB,CAAC;gBACnC;gBACA;YACJ;QACA,KAAK;YAAO;gBACR,OAAQ,IAAI,GAAG;oBACX,KAAK;oBACL,KAAK;wBACD,YAAY;4BAAE,MAAM;wBAAU;wBAC9B,YAAY,IAAI,CAAC,GAAG;4BAAC;yBAAO,GAAG;4BAAC;yBAAS;wBACzC;oBACJ,KAAK;oBACL,KAAK;oBACL,KAAK;oBACL,KAAK;wBACD,YAAY;4BAAE,MAAM,IAAI,GAAG;wBAAC;wBAC5B,YAAY,IAAI,CAAC,GAAG;4BAAC;yBAAa,GAAG,EAAE;wBACvC;oBACJ;wBACI,MAAM,IAAI,iPAAgB,CAAC;gBACnC;gBACA;YACJ;QACA;YACI,MAAM,IAAI,iPAAgB,CAAC;IACnC;IACA,OAAO;QAAE;QAAW;IAAU;AAClC;uCACe,OAAO;IAClB,IAAI,CAAC,IAAI,GAAG,EAAE;QACV,MAAM,IAAI,UAAU;IACxB;IACA,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,GAAG,cAAc;IAC/C,MAAM,UAAU;QAAE,GAAG,GAAG;IAAC;IACzB,IAAI,QAAQ,GAAG,KAAK,OAAO;QACvB,OAAO,QAAQ,GAAG;IACtB;IACA,OAAO,QAAQ,GAAG;IAClB,OAAO,OAAO,MAAM,CAAC,SAAS,CAAC,OAAO,SAAS,WAAW,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,GAAG,QAAQ,IAAI,GAAG,IAAI,OAAO,IAAI;AAC5H", "ignoreList": [0]}}, {"offset": {"line": 1148, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/frontend/node_modules/.pnpm/jose@6.1.0/node_modules/jose/dist/webapi/lib/normalize_key.js"], "sourcesContent": ["import { isJWK } from './is_jwk.js';\nimport { decode } from '../util/base64url.js';\nimport importJWK from './jwk_to_key.js';\nimport { isCryptoKey, isKeyObject } from './is_key_like.js';\nlet cache;\nconst handleJWK = async (key, jwk, alg, freeze = false) => {\n    cache ||= new WeakMap();\n    let cached = cache.get(key);\n    if (cached?.[alg]) {\n        return cached[alg];\n    }\n    const cryptoKey = await importJWK({ ...jwk, alg });\n    if (freeze)\n        Object.freeze(key);\n    if (!cached) {\n        cache.set(key, { [alg]: cryptoKey });\n    }\n    else {\n        cached[alg] = cryptoKey;\n    }\n    return cryptoKey;\n};\nconst handleKeyObject = (keyObject, alg) => {\n    cache ||= new WeakMap();\n    let cached = cache.get(keyObject);\n    if (cached?.[alg]) {\n        return cached[alg];\n    }\n    const isPublic = keyObject.type === 'public';\n    const extractable = isPublic ? true : false;\n    let cryptoKey;\n    if (keyObject.asymmetricKeyType === 'x25519') {\n        switch (alg) {\n            case 'ECDH-ES':\n            case 'ECDH-ES+A128KW':\n            case 'ECDH-ES+A192KW':\n            case 'ECDH-ES+A256KW':\n                break;\n            default:\n                throw new TypeError('given KeyObject instance cannot be used for this algorithm');\n        }\n        cryptoKey = keyObject.toCryptoKey(keyObject.asymmetricKeyType, extractable, isPublic ? [] : ['deriveBits']);\n    }\n    if (keyObject.asymmetricKeyType === 'ed25519') {\n        if (alg !== 'EdDSA' && alg !== 'Ed25519') {\n            throw new TypeError('given KeyObject instance cannot be used for this algorithm');\n        }\n        cryptoKey = keyObject.toCryptoKey(keyObject.asymmetricKeyType, extractable, [\n            isPublic ? 'verify' : 'sign',\n        ]);\n    }\n    switch (keyObject.asymmetricKeyType) {\n        case 'ml-dsa-44':\n        case 'ml-dsa-65':\n        case 'ml-dsa-87': {\n            if (alg !== keyObject.asymmetricKeyType.toUpperCase()) {\n                throw new TypeError('given KeyObject instance cannot be used for this algorithm');\n            }\n            cryptoKey = keyObject.toCryptoKey(keyObject.asymmetricKeyType, extractable, [\n                isPublic ? 'verify' : 'sign',\n            ]);\n        }\n    }\n    if (keyObject.asymmetricKeyType === 'rsa') {\n        let hash;\n        switch (alg) {\n            case 'RSA-OAEP':\n                hash = 'SHA-1';\n                break;\n            case 'RS256':\n            case 'PS256':\n            case 'RSA-OAEP-256':\n                hash = 'SHA-256';\n                break;\n            case 'RS384':\n            case 'PS384':\n            case 'RSA-OAEP-384':\n                hash = 'SHA-384';\n                break;\n            case 'RS512':\n            case 'PS512':\n            case 'RSA-OAEP-512':\n                hash = 'SHA-512';\n                break;\n            default:\n                throw new TypeError('given KeyObject instance cannot be used for this algorithm');\n        }\n        if (alg.startsWith('RSA-OAEP')) {\n            return keyObject.toCryptoKey({\n                name: 'RSA-OAEP',\n                hash,\n            }, extractable, isPublic ? ['encrypt'] : ['decrypt']);\n        }\n        cryptoKey = keyObject.toCryptoKey({\n            name: alg.startsWith('PS') ? 'RSA-PSS' : 'RSASSA-PKCS1-v1_5',\n            hash,\n        }, extractable, [isPublic ? 'verify' : 'sign']);\n    }\n    if (keyObject.asymmetricKeyType === 'ec') {\n        const nist = new Map([\n            ['prime256v1', 'P-256'],\n            ['secp384r1', 'P-384'],\n            ['secp521r1', 'P-521'],\n        ]);\n        const namedCurve = nist.get(keyObject.asymmetricKeyDetails?.namedCurve);\n        if (!namedCurve) {\n            throw new TypeError('given KeyObject instance cannot be used for this algorithm');\n        }\n        if (alg === 'ES256' && namedCurve === 'P-256') {\n            cryptoKey = keyObject.toCryptoKey({\n                name: 'ECDSA',\n                namedCurve,\n            }, extractable, [isPublic ? 'verify' : 'sign']);\n        }\n        if (alg === 'ES384' && namedCurve === 'P-384') {\n            cryptoKey = keyObject.toCryptoKey({\n                name: 'ECDSA',\n                namedCurve,\n            }, extractable, [isPublic ? 'verify' : 'sign']);\n        }\n        if (alg === 'ES512' && namedCurve === 'P-521') {\n            cryptoKey = keyObject.toCryptoKey({\n                name: 'ECDSA',\n                namedCurve,\n            }, extractable, [isPublic ? 'verify' : 'sign']);\n        }\n        if (alg.startsWith('ECDH-ES')) {\n            cryptoKey = keyObject.toCryptoKey({\n                name: 'ECDH',\n                namedCurve,\n            }, extractable, isPublic ? [] : ['deriveBits']);\n        }\n    }\n    if (!cryptoKey) {\n        throw new TypeError('given KeyObject instance cannot be used for this algorithm');\n    }\n    if (!cached) {\n        cache.set(keyObject, { [alg]: cryptoKey });\n    }\n    else {\n        cached[alg] = cryptoKey;\n    }\n    return cryptoKey;\n};\nexport default async (key, alg) => {\n    if (key instanceof Uint8Array) {\n        return key;\n    }\n    if (isCryptoKey(key)) {\n        return key;\n    }\n    if (isKeyObject(key)) {\n        if (key.type === 'secret') {\n            return key.export();\n        }\n        if ('toCryptoKey' in key && typeof key.toCryptoKey === 'function') {\n            try {\n                return handleKeyObject(key, alg);\n            }\n            catch (err) {\n                if (err instanceof TypeError) {\n                    throw err;\n                }\n            }\n        }\n        let jwk = key.export({ format: 'jwk' });\n        return handleJWK(key, jwk, alg);\n    }\n    if (isJWK(key)) {\n        if (key.k) {\n            return decode(key.k);\n        }\n        return handleJWK(key, key, alg, true);\n    }\n    throw new Error('unreachable');\n};\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;AACA,IAAI;AACJ,MAAM,YAAY,OAAO,KAAK,KAAK,KAAK,SAAS,KAAK;IAClD,UAAU,IAAI;IACd,IAAI,SAAS,MAAM,GAAG,CAAC;IACvB,IAAI,QAAQ,CAAC,IAAI,EAAE;QACf,OAAO,MAAM,CAAC,IAAI;IACtB;IACA,MAAM,YAAY,MAAM,IAAA,2OAAS,EAAC;QAAE,GAAG,GAAG;QAAE;IAAI;IAChD,IAAI,QACA,OAAO,MAAM,CAAC;IAClB,IAAI,CAAC,QAAQ;QACT,MAAM,GAAG,CAAC,KAAK;YAAE,CAAC,IAAI,EAAE;QAAU;IACtC,OACK;QACD,MAAM,CAAC,IAAI,GAAG;IAClB;IACA,OAAO;AACX;AACA,MAAM,kBAAkB,CAAC,WAAW;IAChC,UAAU,IAAI;IACd,IAAI,SAAS,MAAM,GAAG,CAAC;IACvB,IAAI,QAAQ,CAAC,IAAI,EAAE;QACf,OAAO,MAAM,CAAC,IAAI;IACtB;IACA,MAAM,WAAW,UAAU,IAAI,KAAK;IACpC,MAAM,cAAc,WAAW,OAAO;IACtC,IAAI;IACJ,IAAI,UAAU,iBAAiB,KAAK,UAAU;QAC1C,OAAQ;YACJ,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACD;YACJ;gBACI,MAAM,IAAI,UAAU;QAC5B;QACA,YAAY,UAAU,WAAW,CAAC,UAAU,iBAAiB,EAAE,aAAa,WAAW,EAAE,GAAG;YAAC;SAAa;IAC9G;IACA,IAAI,UAAU,iBAAiB,KAAK,WAAW;QAC3C,IAAI,QAAQ,WAAW,QAAQ,WAAW;YACtC,MAAM,IAAI,UAAU;QACxB;QACA,YAAY,UAAU,WAAW,CAAC,UAAU,iBAAiB,EAAE,aAAa;YACxE,WAAW,WAAW;SACzB;IACL;IACA,OAAQ,UAAU,iBAAiB;QAC/B,KAAK;QACL,KAAK;QACL,KAAK;YAAa;gBACd,IAAI,QAAQ,UAAU,iBAAiB,CAAC,WAAW,IAAI;oBACnD,MAAM,IAAI,UAAU;gBACxB;gBACA,YAAY,UAAU,WAAW,CAAC,UAAU,iBAAiB,EAAE,aAAa;oBACxE,WAAW,WAAW;iBACzB;YACL;IACJ;IACA,IAAI,UAAU,iBAAiB,KAAK,OAAO;QACvC,IAAI;QACJ,OAAQ;YACJ,KAAK;gBACD,OAAO;gBACP;YACJ,KAAK;YACL,KAAK;YACL,KAAK;gBACD,OAAO;gBACP;YACJ,KAAK;YACL,KAAK;YACL,KAAK;gBACD,OAAO;gBACP;YACJ,KAAK;YACL,KAAK;YACL,KAAK;gBACD,OAAO;gBACP;YACJ;gBACI,MAAM,IAAI,UAAU;QAC5B;QACA,IAAI,IAAI,UAAU,CAAC,aAAa;YAC5B,OAAO,UAAU,WAAW,CAAC;gBACzB,MAAM;gBACN;YACJ,GAAG,aAAa,WAAW;gBAAC;aAAU,GAAG;gBAAC;aAAU;QACxD;QACA,YAAY,UAAU,WAAW,CAAC;YAC9B,MAAM,IAAI,UAAU,CAAC,QAAQ,YAAY;YACzC;QACJ,GAAG,aAAa;YAAC,WAAW,WAAW;SAAO;IAClD;IACA,IAAI,UAAU,iBAAiB,KAAK,MAAM;QACtC,MAAM,OAAO,IAAI,IAAI;YACjB;gBAAC;gBAAc;aAAQ;YACvB;gBAAC;gBAAa;aAAQ;YACtB;gBAAC;gBAAa;aAAQ;SACzB;QACD,MAAM,aAAa,KAAK,GAAG,CAAC,UAAU,oBAAoB,EAAE;QAC5D,IAAI,CAAC,YAAY;YACb,MAAM,IAAI,UAAU;QACxB;QACA,IAAI,QAAQ,WAAW,eAAe,SAAS;YAC3C,YAAY,UAAU,WAAW,CAAC;gBAC9B,MAAM;gBACN;YACJ,GAAG,aAAa;gBAAC,WAAW,WAAW;aAAO;QAClD;QACA,IAAI,QAAQ,WAAW,eAAe,SAAS;YAC3C,YAAY,UAAU,WAAW,CAAC;gBAC9B,MAAM;gBACN;YACJ,GAAG,aAAa;gBAAC,WAAW,WAAW;aAAO;QAClD;QACA,IAAI,QAAQ,WAAW,eAAe,SAAS;YAC3C,YAAY,UAAU,WAAW,CAAC;gBAC9B,MAAM;gBACN;YACJ,GAAG,aAAa;gBAAC,WAAW,WAAW;aAAO;QAClD;QACA,IAAI,IAAI,UAAU,CAAC,YAAY;YAC3B,YAAY,UAAU,WAAW,CAAC;gBAC9B,MAAM;gBACN;YACJ,GAAG,aAAa,WAAW,EAAE,GAAG;gBAAC;aAAa;QAClD;IACJ;IACA,IAAI,CAAC,WAAW;QACZ,MAAM,IAAI,UAAU;IACxB;IACA,IAAI,CAAC,QAAQ;QACT,MAAM,GAAG,CAAC,WAAW;YAAE,CAAC,IAAI,EAAE;QAAU;IAC5C,OACK;QACD,MAAM,CAAC,IAAI,GAAG;IAClB;IACA,OAAO;AACX;uCACe,OAAO,KAAK;IACvB,IAAI,eAAe,YAAY;QAC3B,OAAO;IACX;IACA,IAAI,IAAA,gPAAW,EAAC,MAAM;QAClB,OAAO;IACX;IACA,IAAI,IAAA,gPAAW,EAAC,MAAM;QAClB,IAAI,IAAI,IAAI,KAAK,UAAU;YACvB,OAAO,IAAI,MAAM;QACrB;QACA,IAAI,iBAAiB,OAAO,OAAO,IAAI,WAAW,KAAK,YAAY;YAC/D,IAAI;gBACA,OAAO,gBAAgB,KAAK;YAChC,EACA,OAAO,KAAK;gBACR,IAAI,eAAe,WAAW;oBAC1B,MAAM;gBACV;YACJ;QACJ;QACA,IAAI,MAAM,IAAI,MAAM,CAAC;YAAE,QAAQ;QAAM;QACrC,OAAO,UAAU,KAAK,KAAK;IAC/B;IACA,IAAI,IAAA,qOAAK,EAAC,MAAM;QACZ,IAAI,IAAI,CAAC,EAAE;YACP,OAAO,IAAA,0OAAM,EAAC,IAAI,CAAC;QACvB;QACA,OAAO,UAAU,KAAK,KAAK,KAAK;IACpC;IACA,MAAM,IAAI,MAAM;AACpB", "ignoreList": [0]}}, {"offset": {"line": 1367, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/frontend/node_modules/.pnpm/jose@6.1.0/node_modules/jose/dist/webapi/lib/cek.js"], "sourcesContent": ["import { JOSENotSupported } from '../util/errors.js';\nexport function bitLength(alg) {\n    switch (alg) {\n        case 'A128GCM':\n            return 128;\n        case 'A192GCM':\n            return 192;\n        case 'A256GCM':\n        case 'A128CBC-HS256':\n            return 256;\n        case 'A192CBC-HS384':\n            return 384;\n        case 'A256CBC-HS512':\n            return 512;\n        default:\n            throw new JOSENotSupported(`Unsupported JWE Algorithm: ${alg}`);\n    }\n}\nexport default (alg) => crypto.getRandomValues(new Uint8Array(bitLength(alg) >> 3));\n"], "names": [], "mappings": ";;;;;;AAAA;;AACO,SAAS,UAAU,GAAG;IACzB,OAAQ;QACJ,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX,KAAK;QACL,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX;YACI,MAAM,IAAI,iPAAgB,CAAC,CAAC,2BAA2B,EAAE,KAAK;IACtE;AACJ;uCACe,CAAC,MAAQ,OAAO,eAAe,CAAC,IAAI,WAAW,UAAU,QAAQ", "ignoreList": [0]}}, {"offset": {"line": 1397, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/frontend/node_modules/.pnpm/jose@6.1.0/node_modules/jose/dist/webapi/lib/asn1.js"], "sourcesContent": ["import invalidKeyInput from './invalid_key_input.js';\nimport { encodeBase64, decodeBase64 } from '../lib/base64.js';\nimport { JOSENotSupported } from '../util/errors.js';\nimport { isCryptoKey, isKeyObject } from './is_key_like.js';\nconst formatPEM = (b64, descriptor) => {\n    const newlined = (b64.match(/.{1,64}/g) || []).join('\\n');\n    return `-----BEGIN ${descriptor}-----\\n${newlined}\\n-----END ${descriptor}-----`;\n};\nconst genericExport = async (keyType, keyFormat, key) => {\n    if (isKeyObject(key)) {\n        if (key.type !== keyType) {\n            throw new TypeError(`key is not a ${keyType} key`);\n        }\n        return key.export({ format: 'pem', type: keyFormat });\n    }\n    if (!isCryptoKey(key)) {\n        throw new TypeError(invalidKeyInput(key, 'CryptoKey', 'KeyObject'));\n    }\n    if (!key.extractable) {\n        throw new TypeError('CryptoKey is not extractable');\n    }\n    if (key.type !== keyType) {\n        throw new TypeError(`key is not a ${keyType} key`);\n    }\n    return formatPEM(encodeBase64(new Uint8Array(await crypto.subtle.exportKey(keyFormat, key))), `${keyType.toUpperCase()} KEY`);\n};\nexport const toSPKI = (key) => {\n    return genericExport('public', 'spki', key);\n};\nexport const toPKCS8 = (key) => {\n    return genericExport('private', 'pkcs8', key);\n};\nconst bytesEqual = (a, b) => {\n    if (a.byteLength !== b.length)\n        return false;\n    for (let i = 0; i < a.byteLength; i++) {\n        if (a[i] !== b[i])\n            return false;\n    }\n    return true;\n};\nconst createASN1State = (data) => ({ data, pos: 0 });\nconst parseLength = (state) => {\n    const first = state.data[state.pos++];\n    if (first & 0x80) {\n        const lengthOfLen = first & 0x7f;\n        let length = 0;\n        for (let i = 0; i < lengthOfLen; i++) {\n            length = (length << 8) | state.data[state.pos++];\n        }\n        return length;\n    }\n    return first;\n};\nconst skipElement = (state, count = 1) => {\n    if (count <= 0)\n        return;\n    state.pos++;\n    const length = parseLength(state);\n    state.pos += length;\n    if (count > 1) {\n        skipElement(state, count - 1);\n    }\n};\nconst expectTag = (state, expectedTag, errorMessage) => {\n    if (state.data[state.pos++] !== expectedTag) {\n        throw new Error(errorMessage);\n    }\n};\nconst getSubarray = (state, length) => {\n    const result = state.data.subarray(state.pos, state.pos + length);\n    state.pos += length;\n    return result;\n};\nconst parseAlgorithmOID = (state) => {\n    expectTag(state, 0x06, 'Expected algorithm OID');\n    const oidLen = parseLength(state);\n    return getSubarray(state, oidLen);\n};\nfunction parsePKCS8Header(state) {\n    expectTag(state, 0x30, 'Invalid PKCS#8 structure');\n    parseLength(state);\n    expectTag(state, 0x02, 'Expected version field');\n    const verLen = parseLength(state);\n    state.pos += verLen;\n    expectTag(state, 0x30, 'Expected algorithm identifier');\n    const algIdLen = parseLength(state);\n    const algIdStart = state.pos;\n    return { algIdStart, algIdLength: algIdLen };\n}\nfunction parseSPKIHeader(state) {\n    expectTag(state, 0x30, 'Invalid SPKI structure');\n    parseLength(state);\n    expectTag(state, 0x30, 'Expected algorithm identifier');\n    const algIdLen = parseLength(state);\n    const algIdStart = state.pos;\n    return { algIdStart, algIdLength: algIdLen };\n}\nconst parseECAlgorithmIdentifier = (state) => {\n    const algOid = parseAlgorithmOID(state);\n    if (bytesEqual(algOid, [0x2b, 0x65, 0x6e])) {\n        return 'X25519';\n    }\n    if (!bytesEqual(algOid, [0x2a, 0x86, 0x48, 0xce, 0x3d, 0x02, 0x01])) {\n        throw new Error('Unsupported key algorithm');\n    }\n    expectTag(state, 0x06, 'Expected curve OID');\n    const curveOidLen = parseLength(state);\n    const curveOid = getSubarray(state, curveOidLen);\n    for (const { name, oid } of [\n        { name: 'P-256', oid: [0x2a, 0x86, 0x48, 0xce, 0x3d, 0x03, 0x01, 0x07] },\n        { name: 'P-384', oid: [0x2b, 0x81, 0x04, 0x00, 0x22] },\n        { name: 'P-521', oid: [0x2b, 0x81, 0x04, 0x00, 0x23] },\n    ]) {\n        if (bytesEqual(curveOid, oid)) {\n            return name;\n        }\n    }\n    throw new Error('Unsupported named curve');\n};\nconst genericImport = async (keyFormat, keyData, alg, options) => {\n    let algorithm;\n    let keyUsages;\n    const isPublic = keyFormat === 'spki';\n    const getSigUsages = () => (isPublic ? ['verify'] : ['sign']);\n    const getEncUsages = () => isPublic ? ['encrypt', 'wrapKey'] : ['decrypt', 'unwrapKey'];\n    switch (alg) {\n        case 'PS256':\n        case 'PS384':\n        case 'PS512':\n            algorithm = { name: 'RSA-PSS', hash: `SHA-${alg.slice(-3)}` };\n            keyUsages = getSigUsages();\n            break;\n        case 'RS256':\n        case 'RS384':\n        case 'RS512':\n            algorithm = { name: 'RSASSA-PKCS1-v1_5', hash: `SHA-${alg.slice(-3)}` };\n            keyUsages = getSigUsages();\n            break;\n        case 'RSA-OAEP':\n        case 'RSA-OAEP-256':\n        case 'RSA-OAEP-384':\n        case 'RSA-OAEP-512':\n            algorithm = {\n                name: 'RSA-OAEP',\n                hash: `SHA-${parseInt(alg.slice(-3), 10) || 1}`,\n            };\n            keyUsages = getEncUsages();\n            break;\n        case 'ES256':\n        case 'ES384':\n        case 'ES512': {\n            const curveMap = { ES256: 'P-256', ES384: 'P-384', ES512: 'P-521' };\n            algorithm = { name: 'ECDSA', namedCurve: curveMap[alg] };\n            keyUsages = getSigUsages();\n            break;\n        }\n        case 'ECDH-ES':\n        case 'ECDH-ES+A128KW':\n        case 'ECDH-ES+A192KW':\n        case 'ECDH-ES+A256KW': {\n            try {\n                const namedCurve = options.getNamedCurve(keyData);\n                algorithm = namedCurve === 'X25519' ? { name: 'X25519' } : { name: 'ECDH', namedCurve };\n            }\n            catch (cause) {\n                throw new JOSENotSupported('Invalid or unsupported key format');\n            }\n            keyUsages = isPublic ? [] : ['deriveBits'];\n            break;\n        }\n        case 'Ed25519':\n        case 'EdDSA':\n            algorithm = { name: 'Ed25519' };\n            keyUsages = getSigUsages();\n            break;\n        case 'ML-DSA-44':\n        case 'ML-DSA-65':\n        case 'ML-DSA-87':\n            algorithm = { name: alg };\n            keyUsages = getSigUsages();\n            break;\n        default:\n            throw new JOSENotSupported('Invalid or unsupported \"alg\" (Algorithm) value');\n    }\n    return crypto.subtle.importKey(keyFormat, keyData, algorithm, options?.extractable ?? (isPublic ? true : false), keyUsages);\n};\nconst processPEMData = (pem, pattern) => {\n    return decodeBase64(pem.replace(pattern, ''));\n};\nexport const fromPKCS8 = (pem, alg, options) => {\n    const keyData = processPEMData(pem, /(?:-----(?:BEGIN|END) PRIVATE KEY-----|\\s)/g);\n    let opts = options;\n    if (alg?.startsWith?.('ECDH-ES')) {\n        opts ||= {};\n        opts.getNamedCurve = (keyData) => {\n            const state = createASN1State(keyData);\n            parsePKCS8Header(state);\n            return parseECAlgorithmIdentifier(state);\n        };\n    }\n    return genericImport('pkcs8', keyData, alg, opts);\n};\nexport const fromSPKI = (pem, alg, options) => {\n    const keyData = processPEMData(pem, /(?:-----(?:BEGIN|END) PUBLIC KEY-----|\\s)/g);\n    let opts = options;\n    if (alg?.startsWith?.('ECDH-ES')) {\n        opts ||= {};\n        opts.getNamedCurve = (keyData) => {\n            const state = createASN1State(keyData);\n            parseSPKIHeader(state);\n            return parseECAlgorithmIdentifier(state);\n        };\n    }\n    return genericImport('spki', keyData, alg, opts);\n};\nfunction spkiFromX509(buf) {\n    const state = createASN1State(buf);\n    expectTag(state, 0x30, 'Invalid certificate structure');\n    parseLength(state);\n    expectTag(state, 0x30, 'Invalid tbsCertificate structure');\n    parseLength(state);\n    if (buf[state.pos] === 0xa0) {\n        skipElement(state, 6);\n    }\n    else {\n        skipElement(state, 5);\n    }\n    const spkiStart = state.pos;\n    expectTag(state, 0x30, 'Invalid SPKI structure');\n    const spkiContentLen = parseLength(state);\n    return buf.subarray(spkiStart, spkiStart + spkiContentLen + (state.pos - spkiStart));\n}\nfunction extractX509SPKI(x509) {\n    const derBytes = processPEMData(x509, /(?:-----(?:BEGIN|END) CERTIFICATE-----|\\s)/g);\n    return spkiFromX509(derBytes);\n}\nexport const fromX509 = (pem, alg, options) => {\n    let spki;\n    try {\n        spki = extractX509SPKI(pem);\n    }\n    catch (cause) {\n        throw new TypeError('Failed to parse the X.509 certificate', { cause });\n    }\n    return fromSPKI(formatPEM(encodeBase64(spki), 'PUBLIC KEY'), alg, options);\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AACA;AACA;AACA;;;;;AACA,MAAM,YAAY,CAAC,KAAK;IACpB,MAAM,WAAW,CAAC,IAAI,KAAK,CAAC,eAAe,EAAE,EAAE,IAAI,CAAC;IACpD,OAAO,CAAC,WAAW,EAAE,WAAW,OAAO,EAAE,SAAS,WAAW,EAAE,WAAW,KAAK,CAAC;AACpF;AACA,MAAM,gBAAgB,OAAO,SAAS,WAAW;IAC7C,IAAI,IAAA,gPAAW,EAAC,MAAM;QAClB,IAAI,IAAI,IAAI,KAAK,SAAS;YACtB,MAAM,IAAI,UAAU,CAAC,aAAa,EAAE,QAAQ,IAAI,CAAC;QACrD;QACA,OAAO,IAAI,MAAM,CAAC;YAAE,QAAQ;YAAO,MAAM;QAAU;IACvD;IACA,IAAI,CAAC,IAAA,gPAAW,EAAC,MAAM;QACnB,MAAM,IAAI,UAAU,IAAA,kPAAe,EAAC,KAAK,aAAa;IAC1D;IACA,IAAI,CAAC,IAAI,WAAW,EAAE;QAClB,MAAM,IAAI,UAAU;IACxB;IACA,IAAI,IAAI,IAAI,KAAK,SAAS;QACtB,MAAM,IAAI,UAAU,CAAC,aAAa,EAAE,QAAQ,IAAI,CAAC;IACrD;IACA,OAAO,UAAU,IAAA,4OAAY,EAAC,IAAI,WAAW,MAAM,OAAO,MAAM,CAAC,SAAS,CAAC,WAAW,QAAQ,GAAG,QAAQ,WAAW,GAAG,IAAI,CAAC;AAChI;AACO,MAAM,SAAS,CAAC;IACnB,OAAO,cAAc,UAAU,QAAQ;AAC3C;AACO,MAAM,UAAU,CAAC;IACpB,OAAO,cAAc,WAAW,SAAS;AAC7C;AACA,MAAM,aAAa,CAAC,GAAG;IACnB,IAAI,EAAE,UAAU,KAAK,EAAE,MAAM,EACzB,OAAO;IACX,IAAK,IAAI,IAAI,GAAG,IAAI,EAAE,UAAU,EAAE,IAAK;QACnC,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,EACb,OAAO;IACf;IACA,OAAO;AACX;AACA,MAAM,kBAAkB,CAAC,OAAS,CAAC;QAAE;QAAM,KAAK;IAAE,CAAC;AACnD,MAAM,cAAc,CAAC;IACjB,MAAM,QAAQ,MAAM,IAAI,CAAC,MAAM,GAAG,GAAG;IACrC,IAAI,QAAQ,MAAM;QACd,MAAM,cAAc,QAAQ;QAC5B,IAAI,SAAS;QACb,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,IAAK;YAClC,SAAS,AAAC,UAAU,IAAK,MAAM,IAAI,CAAC,MAAM,GAAG,GAAG;QACpD;QACA,OAAO;IACX;IACA,OAAO;AACX;AACA,MAAM,cAAc,CAAC,OAAO,QAAQ,CAAC;IACjC,IAAI,SAAS,GACT;IACJ,MAAM,GAAG;IACT,MAAM,SAAS,YAAY;IAC3B,MAAM,GAAG,IAAI;IACb,IAAI,QAAQ,GAAG;QACX,YAAY,OAAO,QAAQ;IAC/B;AACJ;AACA,MAAM,YAAY,CAAC,OAAO,aAAa;IACnC,IAAI,MAAM,IAAI,CAAC,MAAM,GAAG,GAAG,KAAK,aAAa;QACzC,MAAM,IAAI,MAAM;IACpB;AACJ;AACA,MAAM,cAAc,CAAC,OAAO;IACxB,MAAM,SAAS,MAAM,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,EAAE,MAAM,GAAG,GAAG;IAC1D,MAAM,GAAG,IAAI;IACb,OAAO;AACX;AACA,MAAM,oBAAoB,CAAC;IACvB,UAAU,OAAO,MAAM;IACvB,MAAM,SAAS,YAAY;IAC3B,OAAO,YAAY,OAAO;AAC9B;AACA,SAAS,iBAAiB,KAAK;IAC3B,UAAU,OAAO,MAAM;IACvB,YAAY;IACZ,UAAU,OAAO,MAAM;IACvB,MAAM,SAAS,YAAY;IAC3B,MAAM,GAAG,IAAI;IACb,UAAU,OAAO,MAAM;IACvB,MAAM,WAAW,YAAY;IAC7B,MAAM,aAAa,MAAM,GAAG;IAC5B,OAAO;QAAE;QAAY,aAAa;IAAS;AAC/C;AACA,SAAS,gBAAgB,KAAK;IAC1B,UAAU,OAAO,MAAM;IACvB,YAAY;IACZ,UAAU,OAAO,MAAM;IACvB,MAAM,WAAW,YAAY;IAC7B,MAAM,aAAa,MAAM,GAAG;IAC5B,OAAO;QAAE;QAAY,aAAa;IAAS;AAC/C;AACA,MAAM,6BAA6B,CAAC;IAChC,MAAM,SAAS,kBAAkB;IACjC,IAAI,WAAW,QAAQ;QAAC;QAAM;QAAM;KAAK,GAAG;QACxC,OAAO;IACX;IACA,IAAI,CAAC,WAAW,QAAQ;QAAC;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;KAAK,GAAG;QACjE,MAAM,IAAI,MAAM;IACpB;IACA,UAAU,OAAO,MAAM;IACvB,MAAM,cAAc,YAAY;IAChC,MAAM,WAAW,YAAY,OAAO;IACpC,KAAK,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI;QACxB;YAAE,MAAM;YAAS,KAAK;gBAAC;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;gBAAM;aAAK;QAAC;QACvE;YAAE,MAAM;YAAS,KAAK;gBAAC;gBAAM;gBAAM;gBAAM;gBAAM;aAAK;QAAC;QACrD;YAAE,MAAM;YAAS,KAAK;gBAAC;gBAAM;gBAAM;gBAAM;gBAAM;aAAK;QAAC;KACxD,CAAE;QACC,IAAI,WAAW,UAAU,MAAM;YAC3B,OAAO;QACX;IACJ;IACA,MAAM,IAAI,MAAM;AACpB;AACA,MAAM,gBAAgB,OAAO,WAAW,SAAS,KAAK;IAClD,IAAI;IACJ,IAAI;IACJ,MAAM,WAAW,cAAc;IAC/B,MAAM,eAAe,IAAO,WAAW;YAAC;SAAS,GAAG;YAAC;SAAO;IAC5D,MAAM,eAAe,IAAM,WAAW;YAAC;YAAW;SAAU,GAAG;YAAC;YAAW;SAAY;IACvF,OAAQ;QACJ,KAAK;QACL,KAAK;QACL,KAAK;YACD,YAAY;gBAAE,MAAM;gBAAW,MAAM,CAAC,IAAI,EAAE,IAAI,KAAK,CAAC,CAAC,IAAI;YAAC;YAC5D,YAAY;YACZ;QACJ,KAAK;QACL,KAAK;QACL,KAAK;YACD,YAAY;gBAAE,MAAM;gBAAqB,MAAM,CAAC,IAAI,EAAE,IAAI,KAAK,CAAC,CAAC,IAAI;YAAC;YACtE,YAAY;YACZ;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACD,YAAY;gBACR,MAAM;gBACN,MAAM,CAAC,IAAI,EAAE,SAAS,IAAI,KAAK,CAAC,CAAC,IAAI,OAAO,GAAG;YACnD;YACA,YAAY;YACZ;QACJ,KAAK;QACL,KAAK;QACL,KAAK;YAAS;gBACV,MAAM,WAAW;oBAAE,OAAO;oBAAS,OAAO;oBAAS,OAAO;gBAAQ;gBAClE,YAAY;oBAAE,MAAM;oBAAS,YAAY,QAAQ,CAAC,IAAI;gBAAC;gBACvD,YAAY;gBACZ;YACJ;QACA,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YAAkB;gBACnB,IAAI;oBACA,MAAM,aAAa,QAAQ,aAAa,CAAC;oBACzC,YAAY,eAAe,WAAW;wBAAE,MAAM;oBAAS,IAAI;wBAAE,MAAM;wBAAQ;oBAAW;gBAC1F,EACA,OAAO,OAAO;oBACV,MAAM,IAAI,iPAAgB,CAAC;gBAC/B;gBACA,YAAY,WAAW,EAAE,GAAG;oBAAC;iBAAa;gBAC1C;YACJ;QACA,KAAK;QACL,KAAK;YACD,YAAY;gBAAE,MAAM;YAAU;YAC9B,YAAY;YACZ;QACJ,KAAK;QACL,KAAK;QACL,KAAK;YACD,YAAY;gBAAE,MAAM;YAAI;YACxB,YAAY;YACZ;QACJ;YACI,MAAM,IAAI,iPAAgB,CAAC;IACnC;IACA,OAAO,OAAO,MAAM,CAAC,SAAS,CAAC,WAAW,SAAS,WAAW,SAAS,eAAe,CAAC,WAAW,OAAO,KAAK,GAAG;AACrH;AACA,MAAM,iBAAiB,CAAC,KAAK;IACzB,OAAO,IAAA,4OAAY,EAAC,IAAI,OAAO,CAAC,SAAS;AAC7C;AACO,MAAM,YAAY,CAAC,KAAK,KAAK;IAChC,MAAM,UAAU,eAAe,KAAK;IACpC,IAAI,OAAO;IACX,IAAI,KAAK,aAAa,YAAY;QAC9B,SAAS,CAAC;QACV,KAAK,aAAa,GAAG,CAAC;YAClB,MAAM,QAAQ,gBAAgB;YAC9B,iBAAiB;YACjB,OAAO,2BAA2B;QACtC;IACJ;IACA,OAAO,cAAc,SAAS,SAAS,KAAK;AAChD;AACO,MAAM,WAAW,CAAC,KAAK,KAAK;IAC/B,MAAM,UAAU,eAAe,KAAK;IACpC,IAAI,OAAO;IACX,IAAI,KAAK,aAAa,YAAY;QAC9B,SAAS,CAAC;QACV,KAAK,aAAa,GAAG,CAAC;YAClB,MAAM,QAAQ,gBAAgB;YAC9B,gBAAgB;YAChB,OAAO,2BAA2B;QACtC;IACJ;IACA,OAAO,cAAc,QAAQ,SAAS,KAAK;AAC/C;AACA,SAAS,aAAa,GAAG;IACrB,MAAM,QAAQ,gBAAgB;IAC9B,UAAU,OAAO,MAAM;IACvB,YAAY;IACZ,UAAU,OAAO,MAAM;IACvB,YAAY;IACZ,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,KAAK,MAAM;QACzB,YAAY,OAAO;IACvB,OACK;QACD,YAAY,OAAO;IACvB;IACA,MAAM,YAAY,MAAM,GAAG;IAC3B,UAAU,OAAO,MAAM;IACvB,MAAM,iBAAiB,YAAY;IACnC,OAAO,IAAI,QAAQ,CAAC,WAAW,YAAY,iBAAiB,CAAC,MAAM,GAAG,GAAG,SAAS;AACtF;AACA,SAAS,gBAAgB,IAAI;IACzB,MAAM,WAAW,eAAe,MAAM;IACtC,OAAO,aAAa;AACxB;AACO,MAAM,WAAW,CAAC,KAAK,KAAK;IAC/B,IAAI;IACJ,IAAI;QACA,OAAO,gBAAgB;IAC3B,EACA,OAAO,OAAO;QACV,MAAM,IAAI,UAAU,yCAAyC;YAAE;QAAM;IACzE;IACA,OAAO,SAAS,UAAU,IAAA,4OAAY,EAAC,OAAO,eAAe,KAAK;AACtE", "ignoreList": [0]}}, {"offset": {"line": 1750, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/frontend/node_modules/.pnpm/jose@6.1.0/node_modules/jose/dist/webapi/lib/key_to_jwk.js"], "sourcesContent": ["import invalidKeyInput from './invalid_key_input.js';\nimport { encode as b64u } from '../util/base64url.js';\nimport { isCryptoKey, isKeyObject } from './is_key_like.js';\nexport default async function keyToJWK(key) {\n    if (isKeyObject(key)) {\n        if (key.type === 'secret') {\n            key = key.export();\n        }\n        else {\n            return key.export({ format: 'jwk' });\n        }\n    }\n    if (key instanceof Uint8Array) {\n        return {\n            kty: 'oct',\n            k: b64u(key),\n        };\n    }\n    if (!isCryptoKey(key)) {\n        throw new TypeError(invalidKeyInput(key, 'CryptoKey', 'KeyObject', 'Uint8Array'));\n    }\n    if (!key.extractable) {\n        throw new TypeError('non-extractable CryptoKey cannot be exported as a JWK');\n    }\n    const { ext, key_ops, alg, use, ...jwk } = await crypto.subtle.exportKey('jwk', key);\n    if (jwk.kty === 'AKP') {\n        ;\n        jwk.alg = alg;\n    }\n    return jwk;\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AACe,eAAe,SAAS,GAAG;IACtC,IAAI,IAAA,gPAAW,EAAC,MAAM;QAClB,IAAI,IAAI,IAAI,KAAK,UAAU;YACvB,MAAM,IAAI,MAAM;QACpB,OACK;YACD,OAAO,IAAI,MAAM,CAAC;gBAAE,QAAQ;YAAM;QACtC;IACJ;IACA,IAAI,eAAe,YAAY;QAC3B,OAAO;YACH,KAAK;YACL,GAAG,IAAA,0OAAI,EAAC;QACZ;IACJ;IACA,IAAI,CAAC,IAAA,gPAAW,EAAC,MAAM;QACnB,MAAM,IAAI,UAAU,IAAA,kPAAe,EAAC,KAAK,aAAa,aAAa;IACvE;IACA,IAAI,CAAC,IAAI,WAAW,EAAE;QAClB,MAAM,IAAI,UAAU;IACxB;IACA,MAAM,EAAE,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,KAAK,GAAG,MAAM,OAAO,MAAM,CAAC,SAAS,CAAC,OAAO;IAChF,IAAI,IAAI,GAAG,KAAK,OAAO;;QAEnB,IAAI,GAAG,GAAG;IACd;IACA,OAAO;AACX", "ignoreList": [0]}}, {"offset": {"line": 1793, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/frontend/node_modules/.pnpm/jose@6.1.0/node_modules/jose/dist/webapi/key/export.js"], "sourcesContent": ["import { toSPKI as exportPublic, toPKCS8 as exportPrivate } from '../lib/asn1.js';\nimport keyToJWK from '../lib/key_to_jwk.js';\nexport async function exportSPKI(key) {\n    return exportPublic(key);\n}\nexport async function exportPKCS8(key) {\n    return exportPrivate(key);\n}\nexport async function exportJWK(key) {\n    return keyToJWK(key);\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AACO,eAAe,WAAW,GAAG;IAChC,OAAO,IAAA,oOAAY,EAAC;AACxB;AACO,eAAe,YAAY,GAAG;IACjC,OAAO,IAAA,qOAAa,EAAC;AACzB;AACO,eAAe,UAAU,GAAG;IAC/B,OAAO,IAAA,2OAAQ,EAAC;AACpB", "ignoreList": [0]}}, {"offset": {"line": 1818, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/frontend/node_modules/.pnpm/jose@6.1.0/node_modules/jose/dist/webapi/lib/decrypt.js"], "sourcesContent": ["import { concat, uint64be } from './buffer_utils.js';\nimport checkIvLength from './check_iv_length.js';\nimport checkCekLength from './check_cek_length.js';\nimport { JOSENotSupported, JWEDecryptionFailed, JWEInvalid } from '../util/errors.js';\nimport { checkEncCryptoKey } from './crypto_key.js';\nimport invalidKeyInput from './invalid_key_input.js';\nimport { isCryptoKey } from './is_key_like.js';\nasync function timingSafeEqual(a, b) {\n    if (!(a instanceof Uint8Array)) {\n        throw new TypeError('First argument must be a buffer');\n    }\n    if (!(b instanceof Uint8Array)) {\n        throw new TypeError('Second argument must be a buffer');\n    }\n    const algorithm = { name: 'HMAC', hash: 'SHA-256' };\n    const key = (await crypto.subtle.generateKey(algorithm, false, ['sign']));\n    const aHmac = new Uint8Array(await crypto.subtle.sign(algorithm, key, a));\n    const bHmac = new Uint8Array(await crypto.subtle.sign(algorithm, key, b));\n    let out = 0;\n    let i = -1;\n    while (++i < 32) {\n        out |= aHmac[i] ^ bHmac[i];\n    }\n    return out === 0;\n}\nasync function cbcDecrypt(enc, cek, ciphertext, iv, tag, aad) {\n    if (!(cek instanceof Uint8Array)) {\n        throw new TypeError(invalidKeyInput(cek, 'Uint8Array'));\n    }\n    const keySize = parseInt(enc.slice(1, 4), 10);\n    const encKey = await crypto.subtle.importKey('raw', cek.subarray(keySize >> 3), 'AES-CBC', false, ['decrypt']);\n    const macKey = await crypto.subtle.importKey('raw', cek.subarray(0, keySize >> 3), {\n        hash: `SHA-${keySize << 1}`,\n        name: 'HMAC',\n    }, false, ['sign']);\n    const macData = concat(aad, iv, ciphertext, uint64be(aad.length << 3));\n    const expectedTag = new Uint8Array((await crypto.subtle.sign('HMAC', macKey, macData)).slice(0, keySize >> 3));\n    let macCheckPassed;\n    try {\n        macCheckPassed = await timingSafeEqual(tag, expectedTag);\n    }\n    catch {\n    }\n    if (!macCheckPassed) {\n        throw new JWEDecryptionFailed();\n    }\n    let plaintext;\n    try {\n        plaintext = new Uint8Array(await crypto.subtle.decrypt({ iv, name: 'AES-CBC' }, encKey, ciphertext));\n    }\n    catch {\n    }\n    if (!plaintext) {\n        throw new JWEDecryptionFailed();\n    }\n    return plaintext;\n}\nasync function gcmDecrypt(enc, cek, ciphertext, iv, tag, aad) {\n    let encKey;\n    if (cek instanceof Uint8Array) {\n        encKey = await crypto.subtle.importKey('raw', cek, 'AES-GCM', false, ['decrypt']);\n    }\n    else {\n        checkEncCryptoKey(cek, enc, 'decrypt');\n        encKey = cek;\n    }\n    try {\n        return new Uint8Array(await crypto.subtle.decrypt({\n            additionalData: aad,\n            iv,\n            name: 'AES-GCM',\n            tagLength: 128,\n        }, encKey, concat(ciphertext, tag)));\n    }\n    catch {\n        throw new JWEDecryptionFailed();\n    }\n}\nexport default async (enc, cek, ciphertext, iv, tag, aad) => {\n    if (!isCryptoKey(cek) && !(cek instanceof Uint8Array)) {\n        throw new TypeError(invalidKeyInput(cek, 'CryptoKey', 'KeyObject', 'Uint8Array', 'JSON Web Key'));\n    }\n    if (!iv) {\n        throw new JWEInvalid('JWE Initialization Vector missing');\n    }\n    if (!tag) {\n        throw new JWEInvalid('JWE Authentication Tag missing');\n    }\n    checkIvLength(enc, iv);\n    switch (enc) {\n        case 'A128CBC-HS256':\n        case 'A192CBC-HS384':\n        case 'A256CBC-HS512':\n            if (cek instanceof Uint8Array)\n                checkCekLength(cek, parseInt(enc.slice(-3), 10));\n            return cbcDecrypt(enc, cek, ciphertext, iv, tag, aad);\n        case 'A128GCM':\n        case 'A192GCM':\n        case 'A256GCM':\n            if (cek instanceof Uint8Array)\n                checkCekLength(cek, parseInt(enc.slice(1, 4), 10));\n            return gcmDecrypt(enc, cek, ciphertext, iv, tag, aad);\n        default:\n            throw new JOSENotSupported('Unsupported JWE Content Encryption Algorithm');\n    }\n};\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AACA,eAAe,gBAAgB,CAAC,EAAE,CAAC;IAC/B,IAAI,CAAC,CAAC,aAAa,UAAU,GAAG;QAC5B,MAAM,IAAI,UAAU;IACxB;IACA,IAAI,CAAC,CAAC,aAAa,UAAU,GAAG;QAC5B,MAAM,IAAI,UAAU;IACxB;IACA,MAAM,YAAY;QAAE,MAAM;QAAQ,MAAM;IAAU;IAClD,MAAM,MAAO,MAAM,OAAO,MAAM,CAAC,WAAW,CAAC,WAAW,OAAO;QAAC;KAAO;IACvE,MAAM,QAAQ,IAAI,WAAW,MAAM,OAAO,MAAM,CAAC,IAAI,CAAC,WAAW,KAAK;IACtE,MAAM,QAAQ,IAAI,WAAW,MAAM,OAAO,MAAM,CAAC,IAAI,CAAC,WAAW,KAAK;IACtE,IAAI,MAAM;IACV,IAAI,IAAI,CAAC;IACT,MAAO,EAAE,IAAI,GAAI;QACb,OAAO,KAAK,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE;IAC9B;IACA,OAAO,QAAQ;AACnB;AACA,eAAe,WAAW,GAAG,EAAE,GAAG,EAAE,UAAU,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG;IACxD,IAAI,CAAC,CAAC,eAAe,UAAU,GAAG;QAC9B,MAAM,IAAI,UAAU,IAAA,kPAAe,EAAC,KAAK;IAC7C;IACA,MAAM,UAAU,SAAS,IAAI,KAAK,CAAC,GAAG,IAAI;IAC1C,MAAM,SAAS,MAAM,OAAO,MAAM,CAAC,SAAS,CAAC,OAAO,IAAI,QAAQ,CAAC,WAAW,IAAI,WAAW,OAAO;QAAC;KAAU;IAC7G,MAAM,SAAS,MAAM,OAAO,MAAM,CAAC,SAAS,CAAC,OAAO,IAAI,QAAQ,CAAC,GAAG,WAAW,IAAI;QAC/E,MAAM,CAAC,IAAI,EAAE,WAAW,GAAG;QAC3B,MAAM;IACV,GAAG,OAAO;QAAC;KAAO;IAClB,MAAM,UAAU,IAAA,4OAAM,EAAC,KAAK,IAAI,YAAY,IAAA,8OAAQ,EAAC,IAAI,MAAM,IAAI;IACnE,MAAM,cAAc,IAAI,WAAW,CAAC,MAAM,OAAO,MAAM,CAAC,IAAI,CAAC,QAAQ,QAAQ,QAAQ,EAAE,KAAK,CAAC,GAAG,WAAW;IAC3G,IAAI;IACJ,IAAI;QACA,iBAAiB,MAAM,gBAAgB,KAAK;IAChD,EACA,OAAM,CACN;IACA,IAAI,CAAC,gBAAgB;QACjB,MAAM,IAAI,oPAAmB;IACjC;IACA,IAAI;IACJ,IAAI;QACA,YAAY,IAAI,WAAW,MAAM,OAAO,MAAM,CAAC,OAAO,CAAC;YAAE;YAAI,MAAM;QAAU,GAAG,QAAQ;IAC5F,EACA,OAAM,CACN;IACA,IAAI,CAAC,WAAW;QACZ,MAAM,IAAI,oPAAmB;IACjC;IACA,OAAO;AACX;AACA,eAAe,WAAW,GAAG,EAAE,GAAG,EAAE,UAAU,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG;IACxD,IAAI;IACJ,IAAI,eAAe,YAAY;QAC3B,SAAS,MAAM,OAAO,MAAM,CAAC,SAAS,CAAC,OAAO,KAAK,WAAW,OAAO;YAAC;SAAU;IACpF,OACK;QACD,IAAA,qPAAiB,EAAC,KAAK,KAAK;QAC5B,SAAS;IACb;IACA,IAAI;QACA,OAAO,IAAI,WAAW,MAAM,OAAO,MAAM,CAAC,OAAO,CAAC;YAC9C,gBAAgB;YAChB;YACA,MAAM;YACN,WAAW;QACf,GAAG,QAAQ,IAAA,4OAAM,EAAC,YAAY;IAClC,EACA,OAAM;QACF,MAAM,IAAI,oPAAmB;IACjC;AACJ;uCACe,OAAO,KAAK,KAAK,YAAY,IAAI,KAAK;IACjD,IAAI,CAAC,IAAA,gPAAW,EAAC,QAAQ,CAAC,CAAC,eAAe,UAAU,GAAG;QACnD,MAAM,IAAI,UAAU,IAAA,kPAAe,EAAC,KAAK,aAAa,aAAa,cAAc;IACrF;IACA,IAAI,CAAC,IAAI;QACL,MAAM,IAAI,2OAAU,CAAC;IACzB;IACA,IAAI,CAAC,KAAK;QACN,MAAM,IAAI,2OAAU,CAAC;IACzB;IACA,IAAA,gPAAa,EAAC,KAAK;IACnB,OAAQ;QACJ,KAAK;QACL,KAAK;QACL,KAAK;YACD,IAAI,eAAe,YACf,IAAA,iPAAc,EAAC,KAAK,SAAS,IAAI,KAAK,CAAC,CAAC,IAAI;YAChD,OAAO,WAAW,KAAK,KAAK,YAAY,IAAI,KAAK;QACrD,KAAK;QACL,KAAK;QACL,KAAK;YACD,IAAI,eAAe,YACf,IAAA,iPAAc,EAAC,KAAK,SAAS,IAAI,KAAK,CAAC,GAAG,IAAI;YAClD,OAAO,WAAW,KAAK,KAAK,YAAY,IAAI,KAAK;QACrD;YACI,MAAM,IAAI,iPAAgB,CAAC;IACnC;AACJ", "ignoreList": [0]}}, {"offset": {"line": 1945, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/frontend/node_modules/.pnpm/jose@6.1.0/node_modules/jose/dist/webapi/lib/aesgcmkw.js"], "sourcesContent": ["import encrypt from './encrypt.js';\nimport decrypt from './decrypt.js';\nimport { encode as b64u } from '../util/base64url.js';\nexport async function wrap(alg, key, cek, iv) {\n    const jweAlgorithm = alg.slice(0, 7);\n    const wrapped = await encrypt(jweAlgorithm, cek, key, iv, new Uint8Array(0));\n    return {\n        encryptedKey: wrapped.ciphertext,\n        iv: b64u(wrapped.iv),\n        tag: b64u(wrapped.tag),\n    };\n}\nexport async function unwrap(alg, key, encryptedKey, iv, tag) {\n    const jweAlgorithm = alg.slice(0, 7);\n    return decrypt(jweAlgorithm, key, encryptedKey, iv, tag, new Uint8Array(0));\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;;;;AACO,eAAe,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;IACxC,MAAM,eAAe,IAAI,KAAK,CAAC,GAAG;IAClC,MAAM,UAAU,MAAM,IAAA,wOAAO,EAAC,cAAc,KAAK,KAAK,IAAI,IAAI,WAAW;IACzE,OAAO;QACH,cAAc,QAAQ,UAAU;QAChC,IAAI,IAAA,0OAAI,EAAC,QAAQ,EAAE;QACnB,KAAK,IAAA,0OAAI,EAAC,QAAQ,GAAG;IACzB;AACJ;AACO,eAAe,OAAO,GAAG,EAAE,GAAG,EAAE,YAAY,EAAE,EAAE,EAAE,GAAG;IACxD,MAAM,eAAe,IAAI,KAAK,CAAC,GAAG;IAClC,OAAO,IAAA,wOAAO,EAAC,cAAc,KAAK,cAAc,IAAI,KAAK,IAAI,WAAW;AAC5E", "ignoreList": [0]}}, {"offset": {"line": 1974, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/frontend/node_modules/.pnpm/jose@6.1.0/node_modules/jose/dist/webapi/lib/encrypt_key_management.js"], "sourcesContent": ["import * as aeskw from './aeskw.js';\nimport * as ecdhes from './ecdhes.js';\nimport * as pbes2kw from './pbes2kw.js';\nimport * as rsaes from './rsaes.js';\nimport { encode as b64u } from '../util/base64url.js';\nimport normalizeKey from './normalize_key.js';\nimport generateCek, { bitLength as cekLength } from '../lib/cek.js';\nimport { JOSENotSupported } from '../util/errors.js';\nimport { exportJWK } from '../key/export.js';\nimport { wrap as aesGcmKw } from './aesgcmkw.js';\nimport { assertCryptoKey } from './is_key_like.js';\nexport default async (alg, enc, key, providedCek, providedParameters = {}) => {\n    let encryptedKey;\n    let parameters;\n    let cek;\n    switch (alg) {\n        case 'dir': {\n            cek = key;\n            break;\n        }\n        case 'ECDH-ES':\n        case 'ECDH-ES+A128KW':\n        case 'ECDH-ES+A192KW':\n        case 'ECDH-ES+A256KW': {\n            assertCryptoKey(key);\n            if (!ecdhes.allowed(key)) {\n                throw new JOSENotSupported('ECDH with the provided key is not allowed or not supported by your javascript runtime');\n            }\n            const { apu, apv } = providedParameters;\n            let ephemeralKey;\n            if (providedParameters.epk) {\n                ephemeralKey = (await normalizeKey(providedParameters.epk, alg));\n            }\n            else {\n                ephemeralKey = (await crypto.subtle.generateKey(key.algorithm, true, ['deriveBits'])).privateKey;\n            }\n            const { x, y, crv, kty } = await exportJWK(ephemeralKey);\n            const sharedSecret = await ecdhes.deriveKey(key, ephemeralKey, alg === 'ECDH-ES' ? enc : alg, alg === 'ECDH-ES' ? cekLength(enc) : parseInt(alg.slice(-5, -2), 10), apu, apv);\n            parameters = { epk: { x, crv, kty } };\n            if (kty === 'EC')\n                parameters.epk.y = y;\n            if (apu)\n                parameters.apu = b64u(apu);\n            if (apv)\n                parameters.apv = b64u(apv);\n            if (alg === 'ECDH-ES') {\n                cek = sharedSecret;\n                break;\n            }\n            cek = providedCek || generateCek(enc);\n            const kwAlg = alg.slice(-6);\n            encryptedKey = await aeskw.wrap(kwAlg, sharedSecret, cek);\n            break;\n        }\n        case 'RSA-OAEP':\n        case 'RSA-OAEP-256':\n        case 'RSA-OAEP-384':\n        case 'RSA-OAEP-512': {\n            cek = providedCek || generateCek(enc);\n            assertCryptoKey(key);\n            encryptedKey = await rsaes.encrypt(alg, key, cek);\n            break;\n        }\n        case 'PBES2-HS256+A128KW':\n        case 'PBES2-HS384+A192KW':\n        case 'PBES2-HS512+A256KW': {\n            cek = providedCek || generateCek(enc);\n            const { p2c, p2s } = providedParameters;\n            ({ encryptedKey, ...parameters } = await pbes2kw.wrap(alg, key, cek, p2c, p2s));\n            break;\n        }\n        case 'A128KW':\n        case 'A192KW':\n        case 'A256KW': {\n            cek = providedCek || generateCek(enc);\n            encryptedKey = await aeskw.wrap(alg, key, cek);\n            break;\n        }\n        case 'A128GCMKW':\n        case 'A192GCMKW':\n        case 'A256GCMKW': {\n            cek = providedCek || generateCek(enc);\n            const { iv } = providedParameters;\n            ({ encryptedKey, ...parameters } = await aesGcmKw(alg, key, cek, iv));\n            break;\n        }\n        default: {\n            throw new JOSENotSupported('Invalid or unsupported \"alg\" (JWE Algorithm) header value');\n        }\n    }\n    return { cek, encryptedKey, parameters };\n};\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;uCACe,OAAO,KAAK,KAAK,KAAK,aAAa,qBAAqB,CAAC,CAAC;IACrE,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,OAAQ;QACJ,KAAK;YAAO;gBACR,MAAM;gBACN;YACJ;QACA,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YAAkB;gBACnB,IAAA,oPAAe,EAAC;gBAChB,IAAI,CAAC,uOAAc,CAAC,MAAM;oBACtB,MAAM,IAAI,iPAAgB,CAAC;gBAC/B;gBACA,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;gBACrB,IAAI;gBACJ,IAAI,mBAAmB,GAAG,EAAE;oBACxB,eAAgB,MAAM,IAAA,8OAAY,EAAC,mBAAmB,GAAG,EAAE;gBAC/D,OACK;oBACD,eAAe,CAAC,MAAM,OAAO,MAAM,CAAC,WAAW,CAAC,IAAI,SAAS,EAAE,MAAM;wBAAC;qBAAa,CAAC,EAAE,UAAU;gBACpG;gBACA,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,MAAM,IAAA,yOAAS,EAAC;gBAC3C,MAAM,eAAe,MAAM,yOAAgB,CAAC,KAAK,cAAc,QAAQ,YAAY,MAAM,KAAK,QAAQ,YAAY,IAAA,sOAAS,EAAC,OAAO,SAAS,IAAI,KAAK,CAAC,CAAC,GAAG,CAAC,IAAI,KAAK,KAAK;gBACzK,aAAa;oBAAE,KAAK;wBAAE;wBAAG;wBAAK;oBAAI;gBAAE;gBACpC,IAAI,QAAQ,MACR,WAAW,GAAG,CAAC,CAAC,GAAG;gBACvB,IAAI,KACA,WAAW,GAAG,GAAG,IAAA,0OAAI,EAAC;gBAC1B,IAAI,KACA,WAAW,GAAG,GAAG,IAAA,0OAAI,EAAC;gBAC1B,IAAI,QAAQ,WAAW;oBACnB,MAAM;oBACN;gBACJ;gBACA,MAAM,eAAe,IAAA,oOAAW,EAAC;gBACjC,MAAM,QAAQ,IAAI,KAAK,CAAC,CAAC;gBACzB,eAAe,MAAM,mOAAU,CAAC,OAAO,cAAc;gBACrD;YACJ;QACA,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YAAgB;gBACjB,MAAM,eAAe,IAAA,oOAAW,EAAC;gBACjC,IAAA,oPAAe,EAAC;gBAChB,eAAe,MAAM,sOAAa,CAAC,KAAK,KAAK;gBAC7C;YACJ;QACA,KAAK;QACL,KAAK;QACL,KAAK;YAAsB;gBACvB,MAAM,eAAe,IAAA,oOAAW,EAAC;gBACjC,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;gBACrB,CAAC,EAAE,YAAY,EAAE,GAAG,YAAY,GAAG,MAAM,qOAAY,CAAC,KAAK,KAAK,KAAK,KAAK,IAAI;gBAC9E;YACJ;QACA,KAAK;QACL,KAAK;QACL,KAAK;YAAU;gBACX,MAAM,eAAe,IAAA,oOAAW,EAAC;gBACjC,eAAe,MAAM,mOAAU,CAAC,KAAK,KAAK;gBAC1C;YACJ;QACA,KAAK;QACL,KAAK;QACL,KAAK;YAAa;gBACd,MAAM,eAAe,IAAA,oOAAW,EAAC;gBACjC,MAAM,EAAE,EAAE,EAAE,GAAG;gBACf,CAAC,EAAE,YAAY,EAAE,GAAG,YAAY,GAAG,MAAM,IAAA,sOAAQ,EAAC,KAAK,KAAK,KAAK,GAAG;gBACpE;YACJ;QACA;YAAS;gBACL,MAAM,IAAI,iPAAgB,CAAC;YAC/B;IACJ;IACA,OAAO;QAAE;QAAK;QAAc;IAAW;AAC3C", "ignoreList": [0]}}, {"offset": {"line": 2100, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/frontend/node_modules/.pnpm/jose@6.1.0/node_modules/jose/dist/webapi/lib/is_disjoint.js"], "sourcesContent": ["export default (...headers) => {\n    const sources = headers.filter(Boolean);\n    if (sources.length === 0 || sources.length === 1) {\n        return true;\n    }\n    let acc;\n    for (const header of sources) {\n        const parameters = Object.keys(header);\n        if (!acc || acc.size === 0) {\n            acc = new Set(parameters);\n            continue;\n        }\n        for (const parameter of parameters) {\n            if (acc.has(parameter)) {\n                return false;\n            }\n            acc.add(parameter);\n        }\n    }\n    return true;\n};\n"], "names": [], "mappings": ";;;;uCAAe,CAAC,GAAG;IACf,MAAM,UAAU,QAAQ,MAAM,CAAC;IAC/B,IAAI,QAAQ,MAAM,KAAK,KAAK,QAAQ,MAAM,KAAK,GAAG;QAC9C,OAAO;IACX;IACA,IAAI;IACJ,KAAK,MAAM,UAAU,QAAS;QAC1B,MAAM,aAAa,OAAO,IAAI,CAAC;QAC/B,IAAI,CAAC,OAAO,IAAI,IAAI,KAAK,GAAG;YACxB,MAAM,IAAI,IAAI;YACd;QACJ;QACA,KAAK,MAAM,aAAa,WAAY;YAChC,IAAI,IAAI,GAAG,CAAC,YAAY;gBACpB,OAAO;YACX;YACA,IAAI,GAAG,CAAC;QACZ;IACJ;IACA,OAAO;AACX", "ignoreList": [0]}}, {"offset": {"line": 2129, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/frontend/node_modules/.pnpm/jose@6.1.0/node_modules/jose/dist/webapi/lib/validate_crit.js"], "sourcesContent": ["import { JOSENotSupported, JWEInvalid, JWSInvalid } from '../util/errors.js';\nexport default (Err, recognizedDefault, recognizedOption, protectedHeader, joseHeader) => {\n    if (joseHeader.crit !== undefined && protectedHeader?.crit === undefined) {\n        throw new Err('\"crit\" (Critical) Header Parameter MUST be integrity protected');\n    }\n    if (!protectedHeader || protectedHeader.crit === undefined) {\n        return new Set();\n    }\n    if (!Array.isArray(protectedHeader.crit) ||\n        protectedHeader.crit.length === 0 ||\n        protectedHeader.crit.some((input) => typeof input !== 'string' || input.length === 0)) {\n        throw new Err('\"crit\" (Critical) Header Parameter MUST be an array of non-empty strings when present');\n    }\n    let recognized;\n    if (recognizedOption !== undefined) {\n        recognized = new Map([...Object.entries(recognizedOption), ...recognizedDefault.entries()]);\n    }\n    else {\n        recognized = recognizedDefault;\n    }\n    for (const parameter of protectedHeader.crit) {\n        if (!recognized.has(parameter)) {\n            throw new JOSENotSupported(`Extension Header Parameter \"${parameter}\" is not recognized`);\n        }\n        if (joseHeader[parameter] === undefined) {\n            throw new Err(`Extension Header Parameter \"${parameter}\" is missing`);\n        }\n        if (recognized.get(parameter) && protectedHeader[parameter] === undefined) {\n            throw new Err(`Extension Header Parameter \"${parameter}\" MUST be integrity protected`);\n        }\n    }\n    return new Set(protectedHeader.crit);\n};\n"], "names": [], "mappings": ";;;;AAAA;;uCACe,CAAC,KAAK,mBAAmB,kBAAkB,iBAAiB;IACvE,IAAI,WAAW,IAAI,KAAK,aAAa,iBAAiB,SAAS,WAAW;QACtE,MAAM,IAAI,IAAI;IAClB;IACA,IAAI,CAAC,mBAAmB,gBAAgB,IAAI,KAAK,WAAW;QACxD,OAAO,IAAI;IACf;IACA,IAAI,CAAC,MAAM,OAAO,CAAC,gBAAgB,IAAI,KACnC,gBAAgB,IAAI,CAAC,MAAM,KAAK,KAChC,gBAAgB,IAAI,CAAC,IAAI,CAAC,CAAC,QAAU,OAAO,UAAU,YAAY,MAAM,MAAM,KAAK,IAAI;QACvF,MAAM,IAAI,IAAI;IAClB;IACA,IAAI;IACJ,IAAI,qBAAqB,WAAW;QAChC,aAAa,IAAI,IAAI;eAAI,OAAO,OAAO,CAAC;eAAsB,kBAAkB,OAAO;SAAG;IAC9F,OACK;QACD,aAAa;IACjB;IACA,KAAK,MAAM,aAAa,gBAAgB,IAAI,CAAE;QAC1C,IAAI,CAAC,WAAW,GAAG,CAAC,YAAY;YAC5B,MAAM,IAAI,iPAAgB,CAAC,CAAC,4BAA4B,EAAE,UAAU,mBAAmB,CAAC;QAC5F;QACA,IAAI,UAAU,CAAC,UAAU,KAAK,WAAW;YACrC,MAAM,IAAI,IAAI,CAAC,4BAA4B,EAAE,UAAU,YAAY,CAAC;QACxE;QACA,IAAI,WAAW,GAAG,CAAC,cAAc,eAAe,CAAC,UAAU,KAAK,WAAW;YACvE,MAAM,IAAI,IAAI,CAAC,4BAA4B,EAAE,UAAU,6BAA6B,CAAC;QACzF;IACJ;IACA,OAAO,IAAI,IAAI,gBAAgB,IAAI;AACvC", "ignoreList": [0]}}, {"offset": {"line": 2171, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/frontend/node_modules/.pnpm/jose@6.1.0/node_modules/jose/dist/webapi/lib/check_key_type.js"], "sourcesContent": ["import { withAlg as invalidKeyInput } from './invalid_key_input.js';\nimport isKey<PERSON>ike from './is_key_like.js';\nimport * as jwk from './is_jwk.js';\nconst tag = (key) => key?.[Symbol.toStringTag];\nconst jwkMatchesOp = (alg, key, usage) => {\n    if (key.use !== undefined) {\n        let expected;\n        switch (usage) {\n            case 'sign':\n            case 'verify':\n                expected = 'sig';\n                break;\n            case 'encrypt':\n            case 'decrypt':\n                expected = 'enc';\n                break;\n        }\n        if (key.use !== expected) {\n            throw new TypeError(`Invalid key for this operation, its \"use\" must be \"${expected}\" when present`);\n        }\n    }\n    if (key.alg !== undefined && key.alg !== alg) {\n        throw new TypeError(`Invalid key for this operation, its \"alg\" must be \"${alg}\" when present`);\n    }\n    if (Array.isArray(key.key_ops)) {\n        let expectedKeyOp;\n        switch (true) {\n            case usage === 'sign' || usage === 'verify':\n            case alg === 'dir':\n            case alg.includes('CBC-HS'):\n                expectedKeyOp = usage;\n                break;\n            case alg.startsWith('PBES2'):\n                expectedKeyOp = 'deriveBits';\n                break;\n            case /^A\\d{3}(?:GCM)?(?:KW)?$/.test(alg):\n                if (!alg.includes('GCM') && alg.endsWith('KW')) {\n                    expectedKeyOp = usage === 'encrypt' ? 'wrapKey' : 'unwrapKey';\n                }\n                else {\n                    expectedKeyOp = usage;\n                }\n                break;\n            case usage === 'encrypt' && alg.startsWith('RSA'):\n                expectedKeyOp = 'wrapKey';\n                break;\n            case usage === 'decrypt':\n                expectedKeyOp = alg.startsWith('RSA') ? 'unwrapKey' : 'deriveBits';\n                break;\n        }\n        if (expectedKeyOp && key.key_ops?.includes?.(expectedKeyOp) === false) {\n            throw new TypeError(`Invalid key for this operation, its \"key_ops\" must include \"${expectedKeyOp}\" when present`);\n        }\n    }\n    return true;\n};\nconst symmetricTypeCheck = (alg, key, usage) => {\n    if (key instanceof Uint8Array)\n        return;\n    if (jwk.isJWK(key)) {\n        if (jwk.isSecretJWK(key) && jwkMatchesOp(alg, key, usage))\n            return;\n        throw new TypeError(`JSON Web Key for symmetric algorithms must have JWK \"kty\" (Key Type) equal to \"oct\" and the JWK \"k\" (Key Value) present`);\n    }\n    if (!isKeyLike(key)) {\n        throw new TypeError(invalidKeyInput(alg, key, 'CryptoKey', 'KeyObject', 'JSON Web Key', 'Uint8Array'));\n    }\n    if (key.type !== 'secret') {\n        throw new TypeError(`${tag(key)} instances for symmetric algorithms must be of type \"secret\"`);\n    }\n};\nconst asymmetricTypeCheck = (alg, key, usage) => {\n    if (jwk.isJWK(key)) {\n        switch (usage) {\n            case 'decrypt':\n            case 'sign':\n                if (jwk.isPrivateJWK(key) && jwkMatchesOp(alg, key, usage))\n                    return;\n                throw new TypeError(`JSON Web Key for this operation be a private JWK`);\n            case 'encrypt':\n            case 'verify':\n                if (jwk.isPublicJWK(key) && jwkMatchesOp(alg, key, usage))\n                    return;\n                throw new TypeError(`JSON Web Key for this operation be a public JWK`);\n        }\n    }\n    if (!isKeyLike(key)) {\n        throw new TypeError(invalidKeyInput(alg, key, 'CryptoKey', 'KeyObject', 'JSON Web Key'));\n    }\n    if (key.type === 'secret') {\n        throw new TypeError(`${tag(key)} instances for asymmetric algorithms must not be of type \"secret\"`);\n    }\n    if (key.type === 'public') {\n        switch (usage) {\n            case 'sign':\n                throw new TypeError(`${tag(key)} instances for asymmetric algorithm signing must be of type \"private\"`);\n            case 'decrypt':\n                throw new TypeError(`${tag(key)} instances for asymmetric algorithm decryption must be of type \"private\"`);\n            default:\n                break;\n        }\n    }\n    if (key.type === 'private') {\n        switch (usage) {\n            case 'verify':\n                throw new TypeError(`${tag(key)} instances for asymmetric algorithm verifying must be of type \"public\"`);\n            case 'encrypt':\n                throw new TypeError(`${tag(key)} instances for asymmetric algorithm encryption must be of type \"public\"`);\n            default:\n                break;\n        }\n    }\n};\nexport default (alg, key, usage) => {\n    const symmetric = alg.startsWith('HS') ||\n        alg === 'dir' ||\n        alg.startsWith('PBES2') ||\n        /^A(?:128|192|256)(?:GCM)?(?:KW)?$/.test(alg) ||\n        /^A(?:128|192|256)CBC-HS(?:256|384|512)$/.test(alg);\n    if (symmetric) {\n        symmetricTypeCheck(alg, key, usage);\n    }\n    else {\n        asymmetricTypeCheck(alg, key, usage);\n    }\n};\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AACA,MAAM,MAAM,CAAC,MAAQ,KAAK,CAAC,OAAO,WAAW,CAAC;AAC9C,MAAM,eAAe,CAAC,KAAK,KAAK;IAC5B,IAAI,IAAI,GAAG,KAAK,WAAW;QACvB,IAAI;QACJ,OAAQ;YACJ,KAAK;YACL,KAAK;gBACD,WAAW;gBACX;YACJ,KAAK;YACL,KAAK;gBACD,WAAW;gBACX;QACR;QACA,IAAI,IAAI,GAAG,KAAK,UAAU;YACtB,MAAM,IAAI,UAAU,CAAC,mDAAmD,EAAE,SAAS,cAAc,CAAC;QACtG;IACJ;IACA,IAAI,IAAI,GAAG,KAAK,aAAa,IAAI,GAAG,KAAK,KAAK;QAC1C,MAAM,IAAI,UAAU,CAAC,mDAAmD,EAAE,IAAI,cAAc,CAAC;IACjG;IACA,IAAI,MAAM,OAAO,CAAC,IAAI,OAAO,GAAG;QAC5B,IAAI;QACJ,OAAQ;YACJ,KAAK,UAAU,UAAU,UAAU;YACnC,KAAK,QAAQ;YACb,KAAK,IAAI,QAAQ,CAAC;gBACd,gBAAgB;gBAChB;YACJ,KAAK,IAAI,UAAU,CAAC;gBAChB,gBAAgB;gBAChB;YACJ,KAAK,0BAA0B,IAAI,CAAC;gBAChC,IAAI,CAAC,IAAI,QAAQ,CAAC,UAAU,IAAI,QAAQ,CAAC,OAAO;oBAC5C,gBAAgB,UAAU,YAAY,YAAY;gBACtD,OACK;oBACD,gBAAgB;gBACpB;gBACA;YACJ,KAAK,UAAU,aAAa,IAAI,UAAU,CAAC;gBACvC,gBAAgB;gBAChB;YACJ,KAAK,UAAU;gBACX,gBAAgB,IAAI,UAAU,CAAC,SAAS,cAAc;gBACtD;QACR;QACA,IAAI,iBAAiB,IAAI,OAAO,EAAE,WAAW,mBAAmB,OAAO;YACnE,MAAM,IAAI,UAAU,CAAC,4DAA4D,EAAE,cAAc,cAAc,CAAC;QACpH;IACJ;IACA,OAAO;AACX;AACA,MAAM,qBAAqB,CAAC,KAAK,KAAK;IAClC,IAAI,eAAe,YACf;IACJ,IAAI,qOAAS,CAAC,MAAM;QAChB,IAAI,2OAAe,CAAC,QAAQ,aAAa,KAAK,KAAK,QAC/C;QACJ,MAAM,IAAI,UAAU,CAAC,uHAAuH,CAAC;IACjJ;IACA,IAAI,CAAC,IAAA,4OAAS,EAAC,MAAM;QACjB,MAAM,IAAI,UAAU,IAAA,kPAAe,EAAC,KAAK,KAAK,aAAa,aAAa,gBAAgB;IAC5F;IACA,IAAI,IAAI,IAAI,KAAK,UAAU;QACvB,MAAM,IAAI,UAAU,GAAG,IAAI,KAAK,4DAA4D,CAAC;IACjG;AACJ;AACA,MAAM,sBAAsB,CAAC,KAAK,KAAK;IACnC,IAAI,qOAAS,CAAC,MAAM;QAChB,OAAQ;YACJ,KAAK;YACL,KAAK;gBACD,IAAI,4OAAgB,CAAC,QAAQ,aAAa,KAAK,KAAK,QAChD;gBACJ,MAAM,IAAI,UAAU,CAAC,gDAAgD,CAAC;YAC1E,KAAK;YACL,KAAK;gBACD,IAAI,2OAAe,CAAC,QAAQ,aAAa,KAAK,KAAK,QAC/C;gBACJ,MAAM,IAAI,UAAU,CAAC,+CAA+C,CAAC;QAC7E;IACJ;IACA,IAAI,CAAC,IAAA,4OAAS,EAAC,MAAM;QACjB,MAAM,IAAI,UAAU,IAAA,kPAAe,EAAC,KAAK,KAAK,aAAa,aAAa;IAC5E;IACA,IAAI,IAAI,IAAI,KAAK,UAAU;QACvB,MAAM,IAAI,UAAU,GAAG,IAAI,KAAK,iEAAiE,CAAC;IACtG;IACA,IAAI,IAAI,IAAI,KAAK,UAAU;QACvB,OAAQ;YACJ,KAAK;gBACD,MAAM,IAAI,UAAU,GAAG,IAAI,KAAK,qEAAqE,CAAC;YAC1G,KAAK;gBACD,MAAM,IAAI,UAAU,GAAG,IAAI,KAAK,wEAAwE,CAAC;YAC7G;gBACI;QACR;IACJ;IACA,IAAI,IAAI,IAAI,KAAK,WAAW;QACxB,OAAQ;YACJ,KAAK;gBACD,MAAM,IAAI,UAAU,GAAG,IAAI,KAAK,sEAAsE,CAAC;YAC3G,KAAK;gBACD,MAAM,IAAI,UAAU,GAAG,IAAI,KAAK,uEAAuE,CAAC;YAC5G;gBACI;QACR;IACJ;AACJ;uCACe,CAAC,KAAK,KAAK;IACtB,MAAM,YAAY,IAAI,UAAU,CAAC,SAC7B,QAAQ,SACR,IAAI,UAAU,CAAC,YACf,oCAAoC,IAAI,CAAC,QACzC,0CAA0C,IAAI,CAAC;IACnD,IAAI,WAAW;QACX,mBAAmB,KAAK,KAAK;IACjC,OACK;QACD,oBAAoB,KAAK,KAAK;IAClC;AACJ", "ignoreList": [0]}}, {"offset": {"line": 2298, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/frontend/node_modules/.pnpm/jose@6.1.0/node_modules/jose/dist/webapi/jwe/flattened/encrypt.js"], "sourcesContent": ["import { encode as b64u } from '../../util/base64url.js';\nimport { unprotected } from '../../lib/private_symbols.js';\nimport encrypt from '../../lib/encrypt.js';\nimport encryptKeyManagement from '../../lib/encrypt_key_management.js';\nimport { JOSENotSupported, JWEInvalid } from '../../util/errors.js';\nimport isDisjoint from '../../lib/is_disjoint.js';\nimport { encoder, decoder, concat } from '../../lib/buffer_utils.js';\nimport validateCrit from '../../lib/validate_crit.js';\nimport normalizeKey from '../../lib/normalize_key.js';\nimport checkKeyType from '../../lib/check_key_type.js';\nexport class FlattenedEncrypt {\n    #plaintext;\n    #protectedHeader;\n    #sharedUnprotectedHeader;\n    #unprotectedHeader;\n    #aad;\n    #cek;\n    #iv;\n    #keyManagementParameters;\n    constructor(plaintext) {\n        if (!(plaintext instanceof Uint8Array)) {\n            throw new TypeError('plaintext must be an instance of Uint8Array');\n        }\n        this.#plaintext = plaintext;\n    }\n    setKeyManagementParameters(parameters) {\n        if (this.#keyManagementParameters) {\n            throw new TypeError('setKeyManagementParameters can only be called once');\n        }\n        this.#keyManagementParameters = parameters;\n        return this;\n    }\n    setProtectedHeader(protectedHeader) {\n        if (this.#protectedHeader) {\n            throw new TypeError('setProtectedHeader can only be called once');\n        }\n        this.#protectedHeader = protectedHeader;\n        return this;\n    }\n    setSharedUnprotectedHeader(sharedUnprotectedHeader) {\n        if (this.#sharedUnprotectedHeader) {\n            throw new TypeError('setSharedUnprotectedHeader can only be called once');\n        }\n        this.#sharedUnprotectedHeader = sharedUnprotectedHeader;\n        return this;\n    }\n    setUnprotectedHeader(unprotectedHeader) {\n        if (this.#unprotectedHeader) {\n            throw new TypeError('setUnprotectedHeader can only be called once');\n        }\n        this.#unprotectedHeader = unprotectedHeader;\n        return this;\n    }\n    setAdditionalAuthenticatedData(aad) {\n        this.#aad = aad;\n        return this;\n    }\n    setContentEncryptionKey(cek) {\n        if (this.#cek) {\n            throw new TypeError('setContentEncryptionKey can only be called once');\n        }\n        this.#cek = cek;\n        return this;\n    }\n    setInitializationVector(iv) {\n        if (this.#iv) {\n            throw new TypeError('setInitializationVector can only be called once');\n        }\n        this.#iv = iv;\n        return this;\n    }\n    async encrypt(key, options) {\n        if (!this.#protectedHeader && !this.#unprotectedHeader && !this.#sharedUnprotectedHeader) {\n            throw new JWEInvalid('either setProtectedHeader, setUnprotectedHeader, or sharedUnprotectedHeader must be called before #encrypt()');\n        }\n        if (!isDisjoint(this.#protectedHeader, this.#unprotectedHeader, this.#sharedUnprotectedHeader)) {\n            throw new JWEInvalid('JWE Protected, JWE Shared Unprotected and JWE Per-Recipient Header Parameter names must be disjoint');\n        }\n        const joseHeader = {\n            ...this.#protectedHeader,\n            ...this.#unprotectedHeader,\n            ...this.#sharedUnprotectedHeader,\n        };\n        validateCrit(JWEInvalid, new Map(), options?.crit, this.#protectedHeader, joseHeader);\n        if (joseHeader.zip !== undefined) {\n            throw new JOSENotSupported('JWE \"zip\" (Compression Algorithm) Header Parameter is not supported.');\n        }\n        const { alg, enc } = joseHeader;\n        if (typeof alg !== 'string' || !alg) {\n            throw new JWEInvalid('JWE \"alg\" (Algorithm) Header Parameter missing or invalid');\n        }\n        if (typeof enc !== 'string' || !enc) {\n            throw new JWEInvalid('JWE \"enc\" (Encryption Algorithm) Header Parameter missing or invalid');\n        }\n        let encryptedKey;\n        if (this.#cek && (alg === 'dir' || alg === 'ECDH-ES')) {\n            throw new TypeError(`setContentEncryptionKey cannot be called with JWE \"alg\" (Algorithm) Header ${alg}`);\n        }\n        checkKeyType(alg === 'dir' ? enc : alg, key, 'encrypt');\n        let cek;\n        {\n            let parameters;\n            const k = await normalizeKey(key, alg);\n            ({ cek, encryptedKey, parameters } = await encryptKeyManagement(alg, enc, k, this.#cek, this.#keyManagementParameters));\n            if (parameters) {\n                if (options && unprotected in options) {\n                    if (!this.#unprotectedHeader) {\n                        this.setUnprotectedHeader(parameters);\n                    }\n                    else {\n                        this.#unprotectedHeader = { ...this.#unprotectedHeader, ...parameters };\n                    }\n                }\n                else if (!this.#protectedHeader) {\n                    this.setProtectedHeader(parameters);\n                }\n                else {\n                    this.#protectedHeader = { ...this.#protectedHeader, ...parameters };\n                }\n            }\n        }\n        let additionalData;\n        let protectedHeader;\n        let aadMember;\n        if (this.#protectedHeader) {\n            protectedHeader = encoder.encode(b64u(JSON.stringify(this.#protectedHeader)));\n        }\n        else {\n            protectedHeader = encoder.encode('');\n        }\n        if (this.#aad) {\n            aadMember = b64u(this.#aad);\n            additionalData = concat(protectedHeader, encoder.encode('.'), encoder.encode(aadMember));\n        }\n        else {\n            additionalData = protectedHeader;\n        }\n        const { ciphertext, tag, iv } = await encrypt(enc, this.#plaintext, cek, this.#iv, additionalData);\n        const jwe = {\n            ciphertext: b64u(ciphertext),\n        };\n        if (iv) {\n            jwe.iv = b64u(iv);\n        }\n        if (tag) {\n            jwe.tag = b64u(tag);\n        }\n        if (encryptedKey) {\n            jwe.encrypted_key = b64u(encryptedKey);\n        }\n        if (aadMember) {\n            jwe.aad = aadMember;\n        }\n        if (this.#protectedHeader) {\n            jwe.protected = decoder.decode(protectedHeader);\n        }\n        if (this.#sharedUnprotectedHeader) {\n            jwe.unprotected = this.#sharedUnprotectedHeader;\n        }\n        if (this.#unprotectedHeader) {\n            jwe.header = this.#unprotectedHeader;\n        }\n        return jwe;\n    }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;AACO,MAAM;IACT,CAAA,SAAU,CAAC;IACX,CAAA,eAAgB,CAAC;IACjB,CAAA,uBAAwB,CAAC;IACzB,CAAA,iBAAkB,CAAC;IACnB,CAAA,GAAI,CAAC;IACL,CAAA,GAAI,CAAC;IACL,CAAA,EAAG,CAAC;IACJ,CAAA,uBAAwB,CAAC;IACzB,YAAY,SAAS,CAAE;QACnB,IAAI,CAAC,CAAC,qBAAqB,UAAU,GAAG;YACpC,MAAM,IAAI,UAAU;QACxB;QACA,IAAI,CAAC,CAAA,SAAU,GAAG;IACtB;IACA,2BAA2B,UAAU,EAAE;QACnC,IAAI,IAAI,CAAC,CAAA,uBAAwB,EAAE;YAC/B,MAAM,IAAI,UAAU;QACxB;QACA,IAAI,CAAC,CAAA,uBAAwB,GAAG;QAChC,OAAO,IAAI;IACf;IACA,mBAAmB,eAAe,EAAE;QAChC,IAAI,IAAI,CAAC,CAAA,eAAgB,EAAE;YACvB,MAAM,IAAI,UAAU;QACxB;QACA,IAAI,CAAC,CAAA,eAAgB,GAAG;QACxB,OAAO,IAAI;IACf;IACA,2BAA2B,uBAAuB,EAAE;QAChD,IAAI,IAAI,CAAC,CAAA,uBAAwB,EAAE;YAC/B,MAAM,IAAI,UAAU;QACxB;QACA,IAAI,CAAC,CAAA,uBAAwB,GAAG;QAChC,OAAO,IAAI;IACf;IACA,qBAAqB,iBAAiB,EAAE;QACpC,IAAI,IAAI,CAAC,CAAA,iBAAkB,EAAE;YACzB,MAAM,IAAI,UAAU;QACxB;QACA,IAAI,CAAC,CAAA,iBAAkB,GAAG;QAC1B,OAAO,IAAI;IACf;IACA,+BAA+B,GAAG,EAAE;QAChC,IAAI,CAAC,CAAA,GAAI,GAAG;QACZ,OAAO,IAAI;IACf;IACA,wBAAwB,GAAG,EAAE;QACzB,IAAI,IAAI,CAAC,CAAA,GAAI,EAAE;YACX,MAAM,IAAI,UAAU;QACxB;QACA,IAAI,CAAC,CAAA,GAAI,GAAG;QACZ,OAAO,IAAI;IACf;IACA,wBAAwB,EAAE,EAAE;QACxB,IAAI,IAAI,CAAC,CAAA,EAAG,EAAE;YACV,MAAM,IAAI,UAAU;QACxB;QACA,IAAI,CAAC,CAAA,EAAG,GAAG;QACX,OAAO,IAAI;IACf;IACA,MAAM,QAAQ,GAAG,EAAE,OAAO,EAAE;QACxB,IAAI,CAAC,IAAI,CAAC,CAAA,eAAgB,IAAI,CAAC,IAAI,CAAC,CAAA,iBAAkB,IAAI,CAAC,IAAI,CAAC,CAAA,uBAAwB,EAAE;YACtF,MAAM,IAAI,2OAAU,CAAC;QACzB;QACA,IAAI,CAAC,IAAA,4OAAU,EAAC,IAAI,CAAC,CAAA,eAAgB,EAAE,IAAI,CAAC,CAAA,iBAAkB,EAAE,IAAI,CAAC,CAAA,uBAAwB,GAAG;YAC5F,MAAM,IAAI,2OAAU,CAAC;QACzB;QACA,MAAM,aAAa;YACf,GAAG,IAAI,CAAC,CAAA,eAAgB;YACxB,GAAG,IAAI,CAAC,CAAA,iBAAkB;YAC1B,GAAG,IAAI,CAAC,CAAA,uBAAwB;QACpC;QACA,IAAA,8OAAY,EAAC,2OAAU,EAAE,IAAI,OAAO,SAAS,MAAM,IAAI,CAAC,CAAA,eAAgB,EAAE;QAC1E,IAAI,WAAW,GAAG,KAAK,WAAW;YAC9B,MAAM,IAAI,iPAAgB,CAAC;QAC/B;QACA,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;QACrB,IAAI,OAAO,QAAQ,YAAY,CAAC,KAAK;YACjC,MAAM,IAAI,2OAAU,CAAC;QACzB;QACA,IAAI,OAAO,QAAQ,YAAY,CAAC,KAAK;YACjC,MAAM,IAAI,2OAAU,CAAC;QACzB;QACA,IAAI;QACJ,IAAI,IAAI,CAAC,CAAA,GAAI,IAAI,CAAC,QAAQ,SAAS,QAAQ,SAAS,GAAG;YACnD,MAAM,IAAI,UAAU,CAAC,2EAA2E,EAAE,KAAK;QAC3G;QACA,IAAA,+OAAY,EAAC,QAAQ,QAAQ,MAAM,KAAK,KAAK;QAC7C,IAAI;QACJ;YACI,IAAI;YACJ,MAAM,IAAI,MAAM,IAAA,8OAAY,EAAC,KAAK;YAClC,CAAC,EAAE,GAAG,EAAE,YAAY,EAAE,UAAU,EAAE,GAAG,MAAM,IAAA,uPAAoB,EAAC,KAAK,KAAK,GAAG,IAAI,CAAC,CAAA,GAAI,EAAE,IAAI,CAAC,CAAA,uBAAwB,CAAC;YACtH,IAAI,YAAY;gBACZ,IAAI,WAAW,oPAAW,IAAI,SAAS;oBACnC,IAAI,CAAC,IAAI,CAAC,CAAA,iBAAkB,EAAE;wBAC1B,IAAI,CAAC,oBAAoB,CAAC;oBAC9B,OACK;wBACD,IAAI,CAAC,CAAA,iBAAkB,GAAG;4BAAE,GAAG,IAAI,CAAC,CAAA,iBAAkB;4BAAE,GAAG,UAAU;wBAAC;oBAC1E;gBACJ,OACK,IAAI,CAAC,IAAI,CAAC,CAAA,eAAgB,EAAE;oBAC7B,IAAI,CAAC,kBAAkB,CAAC;gBAC5B,OACK;oBACD,IAAI,CAAC,CAAA,eAAgB,GAAG;wBAAE,GAAG,IAAI,CAAC,CAAA,eAAgB;wBAAE,GAAG,UAAU;oBAAC;gBACtE;YACJ;QACJ;QACA,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI,IAAI,CAAC,CAAA,eAAgB,EAAE;YACvB,kBAAkB,6OAAO,CAAC,MAAM,CAAC,IAAA,0OAAI,EAAC,KAAK,SAAS,CAAC,IAAI,CAAC,CAAA,eAAgB;QAC9E,OACK;YACD,kBAAkB,6OAAO,CAAC,MAAM,CAAC;QACrC;QACA,IAAI,IAAI,CAAC,CAAA,GAAI,EAAE;YACX,YAAY,IAAA,0OAAI,EAAC,IAAI,CAAC,CAAA,GAAI;YAC1B,iBAAiB,IAAA,4OAAM,EAAC,iBAAiB,6OAAO,CAAC,MAAM,CAAC,MAAM,6OAAO,CAAC,MAAM,CAAC;QACjF,OACK;YACD,iBAAiB;QACrB;QACA,MAAM,EAAE,UAAU,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,MAAM,IAAA,wOAAO,EAAC,KAAK,IAAI,CAAC,CAAA,SAAU,EAAE,KAAK,IAAI,CAAC,CAAA,EAAG,EAAE;QACnF,MAAM,MAAM;YACR,YAAY,IAAA,0OAAI,EAAC;QACrB;QACA,IAAI,IAAI;YACJ,IAAI,EAAE,GAAG,IAAA,0OAAI,EAAC;QAClB;QACA,IAAI,KAAK;YACL,IAAI,GAAG,GAAG,IAAA,0OAAI,EAAC;QACnB;QACA,IAAI,cAAc;YACd,IAAI,aAAa,GAAG,IAAA,0OAAI,EAAC;QAC7B;QACA,IAAI,WAAW;YACX,IAAI,GAAG,GAAG;QACd;QACA,IAAI,IAAI,CAAC,CAAA,eAAgB,EAAE;YACvB,IAAI,SAAS,GAAG,6OAAO,CAAC,MAAM,CAAC;QACnC;QACA,IAAI,IAAI,CAAC,CAAA,uBAAwB,EAAE;YAC/B,IAAI,WAAW,GAAG,IAAI,CAAC,CAAA,uBAAwB;QACnD;QACA,IAAI,IAAI,CAAC,CAAA,iBAAkB,EAAE;YACzB,IAAI,MAAM,GAAG,IAAI,CAAC,CAAA,iBAAkB;QACxC;QACA,OAAO;IACX;AACJ", "ignoreList": [0]}}, {"offset": {"line": 2482, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/frontend/node_modules/.pnpm/jose@6.1.0/node_modules/jose/dist/webapi/jwe/compact/encrypt.js"], "sourcesContent": ["import { FlattenedEncrypt } from '../flattened/encrypt.js';\nexport class CompactEncrypt {\n    #flattened;\n    constructor(plaintext) {\n        this.#flattened = new FlattenedEncrypt(plaintext);\n    }\n    setContentEncryptionKey(cek) {\n        this.#flattened.setContentEncryptionKey(cek);\n        return this;\n    }\n    setInitializationVector(iv) {\n        this.#flattened.setInitializationVector(iv);\n        return this;\n    }\n    setProtectedHeader(protectedHeader) {\n        this.#flattened.setProtectedHeader(protectedHeader);\n        return this;\n    }\n    setKeyManagementParameters(parameters) {\n        this.#flattened.setKeyManagementParameters(parameters);\n        return this;\n    }\n    async encrypt(key, options) {\n        const jwe = await this.#flattened.encrypt(key, options);\n        return [jwe.protected, jwe.encrypted_key, jwe.iv, jwe.ciphertext, jwe.tag].join('.');\n    }\n}\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM;IACT,CAAA,SAAU,CAAC;IACX,YAAY,SAAS,CAAE;QACnB,IAAI,CAAC,CAAA,SAAU,GAAG,IAAI,8PAAgB,CAAC;IAC3C;IACA,wBAAwB,GAAG,EAAE;QACzB,IAAI,CAAC,CAAA,SAAU,CAAC,uBAAuB,CAAC;QACxC,OAAO,IAAI;IACf;IACA,wBAAwB,EAAE,EAAE;QACxB,IAAI,CAAC,CAAA,SAAU,CAAC,uBAAuB,CAAC;QACxC,OAAO,IAAI;IACf;IACA,mBAAmB,eAAe,EAAE;QAChC,IAAI,CAAC,CAAA,SAAU,CAAC,kBAAkB,CAAC;QACnC,OAAO,IAAI;IACf;IACA,2BAA2B,UAAU,EAAE;QACnC,IAAI,CAAC,CAAA,SAAU,CAAC,0BAA0B,CAAC;QAC3C,OAAO,IAAI;IACf;IACA,MAAM,QAAQ,GAAG,EAAE,OAAO,EAAE;QACxB,MAAM,MAAM,MAAM,IAAI,CAAC,CAAA,SAAU,CAAC,OAAO,CAAC,KAAK;QAC/C,OAAO;YAAC,IAAI,SAAS;YAAE,IAAI,aAAa;YAAE,IAAI,EAAE;YAAE,IAAI,UAAU;YAAE,IAAI,GAAG;SAAC,CAAC,IAAI,CAAC;IACpF;AACJ", "ignoreList": [0]}}, {"offset": {"line": 2524, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/frontend/node_modules/.pnpm/jose@6.1.0/node_modules/jose/dist/webapi/lib/epoch.js"], "sourcesContent": ["export default (date) => Math.floor(date.getTime() / 1000);\n"], "names": [], "mappings": ";;;;uCAAe,CAAC,OAAS,KAAK,KAAK,CAAC,KAAK,OAAO,KAAK", "ignoreList": [0]}}, {"offset": {"line": 2533, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/frontend/node_modules/.pnpm/jose@6.1.0/node_modules/jose/dist/webapi/lib/secs.js"], "sourcesContent": ["const minute = 60;\nconst hour = minute * 60;\nconst day = hour * 24;\nconst week = day * 7;\nconst year = day * 365.25;\nconst REGEX = /^(\\+|\\-)? ?(\\d+|\\d+\\.\\d+) ?(seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)(?: (ago|from now))?$/i;\nexport default (str) => {\n    const matched = REGEX.exec(str);\n    if (!matched || (matched[4] && matched[1])) {\n        throw new TypeError('Invalid time period format');\n    }\n    const value = parseFloat(matched[2]);\n    const unit = matched[3].toLowerCase();\n    let numericDate;\n    switch (unit) {\n        case 'sec':\n        case 'secs':\n        case 'second':\n        case 'seconds':\n        case 's':\n            numericDate = Math.round(value);\n            break;\n        case 'minute':\n        case 'minutes':\n        case 'min':\n        case 'mins':\n        case 'm':\n            numericDate = Math.round(value * minute);\n            break;\n        case 'hour':\n        case 'hours':\n        case 'hr':\n        case 'hrs':\n        case 'h':\n            numericDate = Math.round(value * hour);\n            break;\n        case 'day':\n        case 'days':\n        case 'd':\n            numericDate = Math.round(value * day);\n            break;\n        case 'week':\n        case 'weeks':\n        case 'w':\n            numericDate = Math.round(value * week);\n            break;\n        default:\n            numericDate = Math.round(value * year);\n            break;\n    }\n    if (matched[1] === '-' || matched[4] === 'ago') {\n        return -numericDate;\n    }\n    return numericDate;\n};\n"], "names": [], "mappings": ";;;;AAAA,MAAM,SAAS;AACf,MAAM,OAAO,SAAS;AACtB,MAAM,MAAM,OAAO;AACnB,MAAM,OAAO,MAAM;AACnB,MAAM,OAAO,MAAM;AACnB,MAAM,QAAQ;uCACC,CAAC;IACZ,MAAM,UAAU,MAAM,IAAI,CAAC;IAC3B,IAAI,CAAC,WAAY,OAAO,CAAC,EAAE,IAAI,OAAO,CAAC,EAAE,EAAG;QACxC,MAAM,IAAI,UAAU;IACxB;IACA,MAAM,QAAQ,WAAW,OAAO,CAAC,EAAE;IACnC,MAAM,OAAO,OAAO,CAAC,EAAE,CAAC,WAAW;IACnC,IAAI;IACJ,OAAQ;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACD,cAAc,KAAK,KAAK,CAAC;YACzB;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACD,cAAc,KAAK,KAAK,CAAC,QAAQ;YACjC;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACD,cAAc,KAAK,KAAK,CAAC,QAAQ;YACjC;QACJ,KAAK;QACL,KAAK;QACL,KAAK;YACD,cAAc,KAAK,KAAK,CAAC,QAAQ;YACjC;QACJ,KAAK;QACL,KAAK;QACL,KAAK;YACD,cAAc,KAAK,KAAK,CAAC,QAAQ;YACjC;QACJ;YACI,cAAc,KAAK,KAAK,CAAC,QAAQ;YACjC;IACR;IACA,IAAI,OAAO,CAAC,EAAE,KAAK,OAAO,OAAO,CAAC,EAAE,KAAK,OAAO;QAC5C,OAAO,CAAC;IACZ;IACA,OAAO;AACX", "ignoreList": [0]}}, {"offset": {"line": 2596, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/frontend/node_modules/.pnpm/jose@6.1.0/node_modules/jose/dist/webapi/lib/jwt_claims_set.js"], "sourcesContent": ["import { JWTClaimValidationFailed, JW<PERSON>xpired, JWTInvalid } from '../util/errors.js';\nimport { decoder } from './buffer_utils.js';\nimport epoch from './epoch.js';\nimport secs from './secs.js';\nimport isObject from './is_object.js';\nimport { encoder } from './buffer_utils.js';\nfunction validateInput(label, input) {\n    if (!Number.isFinite(input)) {\n        throw new TypeError(`Invalid ${label} input`);\n    }\n    return input;\n}\nconst normalizeTyp = (value) => {\n    if (value.includes('/')) {\n        return value.toLowerCase();\n    }\n    return `application/${value.toLowerCase()}`;\n};\nconst checkAudiencePresence = (audPayload, audOption) => {\n    if (typeof audPayload === 'string') {\n        return audOption.includes(audPayload);\n    }\n    if (Array.isArray(audPayload)) {\n        return audOption.some(Set.prototype.has.bind(new Set(audPayload)));\n    }\n    return false;\n};\nexport function validateClaimsSet(protectedHeader, encodedPayload, options = {}) {\n    let payload;\n    try {\n        payload = JSON.parse(decoder.decode(encodedPayload));\n    }\n    catch {\n    }\n    if (!isObject(payload)) {\n        throw new JWTInvalid('JWT Claims Set must be a top-level JSON object');\n    }\n    const { typ } = options;\n    if (typ &&\n        (typeof protectedHeader.typ !== 'string' ||\n            normalizeTyp(protectedHeader.typ) !== normalizeTyp(typ))) {\n        throw new JWTClaimValidationFailed('unexpected \"typ\" JWT header value', payload, 'typ', 'check_failed');\n    }\n    const { requiredClaims = [], issuer, subject, audience, maxTokenAge } = options;\n    const presenceCheck = [...requiredClaims];\n    if (maxTokenAge !== undefined)\n        presenceCheck.push('iat');\n    if (audience !== undefined)\n        presenceCheck.push('aud');\n    if (subject !== undefined)\n        presenceCheck.push('sub');\n    if (issuer !== undefined)\n        presenceCheck.push('iss');\n    for (const claim of new Set(presenceCheck.reverse())) {\n        if (!(claim in payload)) {\n            throw new JWTClaimValidationFailed(`missing required \"${claim}\" claim`, payload, claim, 'missing');\n        }\n    }\n    if (issuer &&\n        !(Array.isArray(issuer) ? issuer : [issuer]).includes(payload.iss)) {\n        throw new JWTClaimValidationFailed('unexpected \"iss\" claim value', payload, 'iss', 'check_failed');\n    }\n    if (subject && payload.sub !== subject) {\n        throw new JWTClaimValidationFailed('unexpected \"sub\" claim value', payload, 'sub', 'check_failed');\n    }\n    if (audience &&\n        !checkAudiencePresence(payload.aud, typeof audience === 'string' ? [audience] : audience)) {\n        throw new JWTClaimValidationFailed('unexpected \"aud\" claim value', payload, 'aud', 'check_failed');\n    }\n    let tolerance;\n    switch (typeof options.clockTolerance) {\n        case 'string':\n            tolerance = secs(options.clockTolerance);\n            break;\n        case 'number':\n            tolerance = options.clockTolerance;\n            break;\n        case 'undefined':\n            tolerance = 0;\n            break;\n        default:\n            throw new TypeError('Invalid clockTolerance option type');\n    }\n    const { currentDate } = options;\n    const now = epoch(currentDate || new Date());\n    if ((payload.iat !== undefined || maxTokenAge) && typeof payload.iat !== 'number') {\n        throw new JWTClaimValidationFailed('\"iat\" claim must be a number', payload, 'iat', 'invalid');\n    }\n    if (payload.nbf !== undefined) {\n        if (typeof payload.nbf !== 'number') {\n            throw new JWTClaimValidationFailed('\"nbf\" claim must be a number', payload, 'nbf', 'invalid');\n        }\n        if (payload.nbf > now + tolerance) {\n            throw new JWTClaimValidationFailed('\"nbf\" claim timestamp check failed', payload, 'nbf', 'check_failed');\n        }\n    }\n    if (payload.exp !== undefined) {\n        if (typeof payload.exp !== 'number') {\n            throw new JWTClaimValidationFailed('\"exp\" claim must be a number', payload, 'exp', 'invalid');\n        }\n        if (payload.exp <= now - tolerance) {\n            throw new JWTExpired('\"exp\" claim timestamp check failed', payload, 'exp', 'check_failed');\n        }\n    }\n    if (maxTokenAge) {\n        const age = now - payload.iat;\n        const max = typeof maxTokenAge === 'number' ? maxTokenAge : secs(maxTokenAge);\n        if (age - tolerance > max) {\n            throw new JWTExpired('\"iat\" claim timestamp check failed (too far in the past)', payload, 'iat', 'check_failed');\n        }\n        if (age < 0 - tolerance) {\n            throw new JWTClaimValidationFailed('\"iat\" claim timestamp check failed (it should be in the past)', payload, 'iat', 'check_failed');\n        }\n    }\n    return payload;\n}\nexport class JWTClaimsBuilder {\n    #payload;\n    constructor(payload) {\n        if (!isObject(payload)) {\n            throw new TypeError('JWT Claims Set MUST be an object');\n        }\n        this.#payload = structuredClone(payload);\n    }\n    data() {\n        return encoder.encode(JSON.stringify(this.#payload));\n    }\n    get iss() {\n        return this.#payload.iss;\n    }\n    set iss(value) {\n        this.#payload.iss = value;\n    }\n    get sub() {\n        return this.#payload.sub;\n    }\n    set sub(value) {\n        this.#payload.sub = value;\n    }\n    get aud() {\n        return this.#payload.aud;\n    }\n    set aud(value) {\n        this.#payload.aud = value;\n    }\n    set jti(value) {\n        this.#payload.jti = value;\n    }\n    set nbf(value) {\n        if (typeof value === 'number') {\n            this.#payload.nbf = validateInput('setNotBefore', value);\n        }\n        else if (value instanceof Date) {\n            this.#payload.nbf = validateInput('setNotBefore', epoch(value));\n        }\n        else {\n            this.#payload.nbf = epoch(new Date()) + secs(value);\n        }\n    }\n    set exp(value) {\n        if (typeof value === 'number') {\n            this.#payload.exp = validateInput('setExpirationTime', value);\n        }\n        else if (value instanceof Date) {\n            this.#payload.exp = validateInput('setExpirationTime', epoch(value));\n        }\n        else {\n            this.#payload.exp = epoch(new Date()) + secs(value);\n        }\n    }\n    set iat(value) {\n        if (typeof value === 'undefined') {\n            this.#payload.iat = epoch(new Date());\n        }\n        else if (value instanceof Date) {\n            this.#payload.iat = validateInput('setIssuedAt', epoch(value));\n        }\n        else if (typeof value === 'string') {\n            this.#payload.iat = validateInput('setIssuedAt', epoch(new Date()) + secs(value));\n        }\n        else {\n            this.#payload.iat = validateInput('setIssuedAt', value);\n        }\n    }\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;;;;;;;AAEA,SAAS,cAAc,KAAK,EAAE,KAAK;IAC/B,IAAI,CAAC,OAAO,QAAQ,CAAC,QAAQ;QACzB,MAAM,IAAI,UAAU,CAAC,QAAQ,EAAE,MAAM,MAAM,CAAC;IAChD;IACA,OAAO;AACX;AACA,MAAM,eAAe,CAAC;IAClB,IAAI,MAAM,QAAQ,CAAC,MAAM;QACrB,OAAO,MAAM,WAAW;IAC5B;IACA,OAAO,CAAC,YAAY,EAAE,MAAM,WAAW,IAAI;AAC/C;AACA,MAAM,wBAAwB,CAAC,YAAY;IACvC,IAAI,OAAO,eAAe,UAAU;QAChC,OAAO,UAAU,QAAQ,CAAC;IAC9B;IACA,IAAI,MAAM,OAAO,CAAC,aAAa;QAC3B,OAAO,UAAU,IAAI,CAAC,IAAI,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,IAAI;IACzD;IACA,OAAO;AACX;AACO,SAAS,kBAAkB,eAAe,EAAE,cAAc,EAAE,UAAU,CAAC,CAAC;IAC3E,IAAI;IACJ,IAAI;QACA,UAAU,KAAK,KAAK,CAAC,6OAAO,CAAC,MAAM,CAAC;IACxC,EACA,OAAM,CACN;IACA,IAAI,CAAC,IAAA,0OAAQ,EAAC,UAAU;QACpB,MAAM,IAAI,2OAAU,CAAC;IACzB;IACA,MAAM,EAAE,GAAG,EAAE,GAAG;IAChB,IAAI,OACA,CAAC,OAAO,gBAAgB,GAAG,KAAK,YAC5B,aAAa,gBAAgB,GAAG,MAAM,aAAa,IAAI,GAAG;QAC9D,MAAM,IAAI,yPAAwB,CAAC,qCAAqC,SAAS,OAAO;IAC5F;IACA,MAAM,EAAE,iBAAiB,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,GAAG;IACxE,MAAM,gBAAgB;WAAI;KAAe;IACzC,IAAI,gBAAgB,WAChB,cAAc,IAAI,CAAC;IACvB,IAAI,aAAa,WACb,cAAc,IAAI,CAAC;IACvB,IAAI,YAAY,WACZ,cAAc,IAAI,CAAC;IACvB,IAAI,WAAW,WACX,cAAc,IAAI,CAAC;IACvB,KAAK,MAAM,SAAS,IAAI,IAAI,cAAc,OAAO,IAAK;QAClD,IAAI,CAAC,CAAC,SAAS,OAAO,GAAG;YACrB,MAAM,IAAI,yPAAwB,CAAC,CAAC,kBAAkB,EAAE,MAAM,OAAO,CAAC,EAAE,SAAS,OAAO;QAC5F;IACJ;IACA,IAAI,UACA,CAAC,CAAC,MAAM,OAAO,CAAC,UAAU,SAAS;QAAC;KAAO,EAAE,QAAQ,CAAC,QAAQ,GAAG,GAAG;QACpE,MAAM,IAAI,yPAAwB,CAAC,gCAAgC,SAAS,OAAO;IACvF;IACA,IAAI,WAAW,QAAQ,GAAG,KAAK,SAAS;QACpC,MAAM,IAAI,yPAAwB,CAAC,gCAAgC,SAAS,OAAO;IACvF;IACA,IAAI,YACA,CAAC,sBAAsB,QAAQ,GAAG,EAAE,OAAO,aAAa,WAAW;QAAC;KAAS,GAAG,WAAW;QAC3F,MAAM,IAAI,yPAAwB,CAAC,gCAAgC,SAAS,OAAO;IACvF;IACA,IAAI;IACJ,OAAQ,OAAO,QAAQ,cAAc;QACjC,KAAK;YACD,YAAY,IAAA,qOAAI,EAAC,QAAQ,cAAc;YACvC;QACJ,KAAK;YACD,YAAY,QAAQ,cAAc;YAClC;QACJ,KAAK;YACD,YAAY;YACZ;QACJ;YACI,MAAM,IAAI,UAAU;IAC5B;IACA,MAAM,EAAE,WAAW,EAAE,GAAG;IACxB,MAAM,MAAM,IAAA,sOAAK,EAAC,eAAe,IAAI;IACrC,IAAI,CAAC,QAAQ,GAAG,KAAK,aAAa,WAAW,KAAK,OAAO,QAAQ,GAAG,KAAK,UAAU;QAC/E,MAAM,IAAI,yPAAwB,CAAC,gCAAgC,SAAS,OAAO;IACvF;IACA,IAAI,QAAQ,GAAG,KAAK,WAAW;QAC3B,IAAI,OAAO,QAAQ,GAAG,KAAK,UAAU;YACjC,MAAM,IAAI,yPAAwB,CAAC,gCAAgC,SAAS,OAAO;QACvF;QACA,IAAI,QAAQ,GAAG,GAAG,MAAM,WAAW;YAC/B,MAAM,IAAI,yPAAwB,CAAC,sCAAsC,SAAS,OAAO;QAC7F;IACJ;IACA,IAAI,QAAQ,GAAG,KAAK,WAAW;QAC3B,IAAI,OAAO,QAAQ,GAAG,KAAK,UAAU;YACjC,MAAM,IAAI,yPAAwB,CAAC,gCAAgC,SAAS,OAAO;QACvF;QACA,IAAI,QAAQ,GAAG,IAAI,MAAM,WAAW;YAChC,MAAM,IAAI,2OAAU,CAAC,sCAAsC,SAAS,OAAO;QAC/E;IACJ;IACA,IAAI,aAAa;QACb,MAAM,MAAM,MAAM,QAAQ,GAAG;QAC7B,MAAM,MAAM,OAAO,gBAAgB,WAAW,cAAc,IAAA,qOAAI,EAAC;QACjE,IAAI,MAAM,YAAY,KAAK;YACvB,MAAM,IAAI,2OAAU,CAAC,4DAA4D,SAAS,OAAO;QACrG;QACA,IAAI,MAAM,IAAI,WAAW;YACrB,MAAM,IAAI,yPAAwB,CAAC,iEAAiE,SAAS,OAAO;QACxH;IACJ;IACA,OAAO;AACX;AACO,MAAM;IACT,CAAA,OAAQ,CAAC;IACT,YAAY,OAAO,CAAE;QACjB,IAAI,CAAC,IAAA,0OAAQ,EAAC,UAAU;YACpB,MAAM,IAAI,UAAU;QACxB;QACA,IAAI,CAAC,CAAA,OAAQ,GAAG,gBAAgB;IACpC;IACA,OAAO;QACH,OAAO,6OAAO,CAAC,MAAM,CAAC,KAAK,SAAS,CAAC,IAAI,CAAC,CAAA,OAAQ;IACtD;IACA,IAAI,MAAM;QACN,OAAO,IAAI,CAAC,CAAA,OAAQ,CAAC,GAAG;IAC5B;IACA,IAAI,IAAI,KAAK,EAAE;QACX,IAAI,CAAC,CAAA,OAAQ,CAAC,GAAG,GAAG;IACxB;IACA,IAAI,MAAM;QACN,OAAO,IAAI,CAAC,CAAA,OAAQ,CAAC,GAAG;IAC5B;IACA,IAAI,IAAI,KAAK,EAAE;QACX,IAAI,CAAC,CAAA,OAAQ,CAAC,GAAG,GAAG;IACxB;IACA,IAAI,MAAM;QACN,OAAO,IAAI,CAAC,CAAA,OAAQ,CAAC,GAAG;IAC5B;IACA,IAAI,IAAI,KAAK,EAAE;QACX,IAAI,CAAC,CAAA,OAAQ,CAAC,GAAG,GAAG;IACxB;IACA,IAAI,IAAI,KAAK,EAAE;QACX,IAAI,CAAC,CAAA,OAAQ,CAAC,GAAG,GAAG;IACxB;IACA,IAAI,IAAI,KAAK,EAAE;QACX,IAAI,OAAO,UAAU,UAAU;YAC3B,IAAI,CAAC,CAAA,OAAQ,CAAC,GAAG,GAAG,cAAc,gBAAgB;QACtD,OACK,IAAI,iBAAiB,MAAM;YAC5B,IAAI,CAAC,CAAA,OAAQ,CAAC,GAAG,GAAG,cAAc,gBAAgB,IAAA,sOAAK,EAAC;QAC5D,OACK;YACD,IAAI,CAAC,CAAA,OAAQ,CAAC,GAAG,GAAG,IAAA,sOAAK,EAAC,IAAI,UAAU,IAAA,qOAAI,EAAC;QACjD;IACJ;IACA,IAAI,IAAI,KAAK,EAAE;QACX,IAAI,OAAO,UAAU,UAAU;YAC3B,IAAI,CAAC,CAAA,OAAQ,CAAC,GAAG,GAAG,cAAc,qBAAqB;QAC3D,OACK,IAAI,iBAAiB,MAAM;YAC5B,IAAI,CAAC,CAAA,OAAQ,CAAC,GAAG,GAAG,cAAc,qBAAqB,IAAA,sOAAK,EAAC;QACjE,OACK;YACD,IAAI,CAAC,CAAA,OAAQ,CAAC,GAAG,GAAG,IAAA,sOAAK,EAAC,IAAI,UAAU,IAAA,qOAAI,EAAC;QACjD;IACJ;IACA,IAAI,IAAI,KAAK,EAAE;QACX,IAAI,OAAO,UAAU,aAAa;YAC9B,IAAI,CAAC,CAAA,OAAQ,CAAC,GAAG,GAAG,IAAA,sOAAK,EAAC,IAAI;QAClC,OACK,IAAI,iBAAiB,MAAM;YAC5B,IAAI,CAAC,CAAA,OAAQ,CAAC,GAAG,GAAG,cAAc,eAAe,IAAA,sOAAK,EAAC;QAC3D,OACK,IAAI,OAAO,UAAU,UAAU;YAChC,IAAI,CAAC,CAAA,OAAQ,CAAC,GAAG,GAAG,cAAc,eAAe,IAAA,sOAAK,EAAC,IAAI,UAAU,IAAA,qOAAI,EAAC;QAC9E,OACK;YACD,IAAI,CAAC,CAAA,OAAQ,CAAC,GAAG,GAAG,cAAc,eAAe;QACrD;IACJ;AACJ", "ignoreList": [0]}}, {"offset": {"line": 2785, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/frontend/node_modules/.pnpm/jose@6.1.0/node_modules/jose/dist/webapi/jwt/encrypt.js"], "sourcesContent": ["import { CompactEncrypt } from '../jwe/compact/encrypt.js';\nimport { JWTClaimsBuilder } from '../lib/jwt_claims_set.js';\nexport class EncryptJWT {\n    #cek;\n    #iv;\n    #keyManagementParameters;\n    #protectedHeader;\n    #replicateIssuerAsHeader;\n    #replicateSubjectAsHeader;\n    #replicateAudienceAsHeader;\n    #jwt;\n    constructor(payload = {}) {\n        this.#jwt = new JWTClaimsBuilder(payload);\n    }\n    setIssuer(issuer) {\n        this.#jwt.iss = issuer;\n        return this;\n    }\n    setSubject(subject) {\n        this.#jwt.sub = subject;\n        return this;\n    }\n    setAudience(audience) {\n        this.#jwt.aud = audience;\n        return this;\n    }\n    setJti(jwtId) {\n        this.#jwt.jti = jwtId;\n        return this;\n    }\n    setNotBefore(input) {\n        this.#jwt.nbf = input;\n        return this;\n    }\n    setExpirationTime(input) {\n        this.#jwt.exp = input;\n        return this;\n    }\n    setIssuedAt(input) {\n        this.#jwt.iat = input;\n        return this;\n    }\n    setProtectedHeader(protectedHeader) {\n        if (this.#protectedHeader) {\n            throw new TypeError('setProtectedHeader can only be called once');\n        }\n        this.#protectedHeader = protectedHeader;\n        return this;\n    }\n    setKeyManagementParameters(parameters) {\n        if (this.#keyManagementParameters) {\n            throw new TypeError('setKeyManagementParameters can only be called once');\n        }\n        this.#keyManagementParameters = parameters;\n        return this;\n    }\n    setContentEncryptionKey(cek) {\n        if (this.#cek) {\n            throw new TypeError('setContentEncryptionKey can only be called once');\n        }\n        this.#cek = cek;\n        return this;\n    }\n    setInitializationVector(iv) {\n        if (this.#iv) {\n            throw new TypeError('setInitializationVector can only be called once');\n        }\n        this.#iv = iv;\n        return this;\n    }\n    replicateIssuerAsHeader() {\n        this.#replicateIssuerAsHeader = true;\n        return this;\n    }\n    replicateSubjectAsHeader() {\n        this.#replicateSubjectAsHeader = true;\n        return this;\n    }\n    replicateAudienceAsHeader() {\n        this.#replicateAudienceAsHeader = true;\n        return this;\n    }\n    async encrypt(key, options) {\n        const enc = new CompactEncrypt(this.#jwt.data());\n        if (this.#protectedHeader &&\n            (this.#replicateIssuerAsHeader ||\n                this.#replicateSubjectAsHeader ||\n                this.#replicateAudienceAsHeader)) {\n            this.#protectedHeader = {\n                ...this.#protectedHeader,\n                iss: this.#replicateIssuerAsHeader ? this.#jwt.iss : undefined,\n                sub: this.#replicateSubjectAsHeader ? this.#jwt.sub : undefined,\n                aud: this.#replicateAudienceAsHeader ? this.#jwt.aud : undefined,\n            };\n        }\n        enc.setProtectedHeader(this.#protectedHeader);\n        if (this.#iv) {\n            enc.setInitializationVector(this.#iv);\n        }\n        if (this.#cek) {\n            enc.setContentEncryptionKey(this.#cek);\n        }\n        if (this.#keyManagementParameters) {\n            enc.setKeyManagementParameters(this.#keyManagementParameters);\n        }\n        return enc.encrypt(key, options);\n    }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AACO,MAAM;IACT,CAAA,GAAI,CAAC;IACL,CAAA,EAAG,CAAC;IACJ,CAAA,uBAAwB,CAAC;IACzB,CAAA,eAAgB,CAAC;IACjB,CAAA,uBAAwB,CAAC;IACzB,CAAA,wBAAyB,CAAC;IAC1B,CAAA,yBAA0B,CAAC;IAC3B,CAAA,GAAI,CAAC;IACL,YAAY,UAAU,CAAC,CAAC,CAAE;QACtB,IAAI,CAAC,CAAA,GAAI,GAAG,IAAI,wPAAgB,CAAC;IACrC;IACA,UAAU,MAAM,EAAE;QACd,IAAI,CAAC,CAAA,GAAI,CAAC,GAAG,GAAG;QAChB,OAAO,IAAI;IACf;IACA,WAAW,OAAO,EAAE;QAChB,IAAI,CAAC,CAAA,GAAI,CAAC,GAAG,GAAG;QAChB,OAAO,IAAI;IACf;IACA,YAAY,QAAQ,EAAE;QAClB,IAAI,CAAC,CAAA,GAAI,CAAC,GAAG,GAAG;QAChB,OAAO,IAAI;IACf;IACA,OAAO,KAAK,EAAE;QACV,IAAI,CAAC,CAAA,GAAI,CAAC,GAAG,GAAG;QAChB,OAAO,IAAI;IACf;IACA,aAAa,KAAK,EAAE;QAChB,IAAI,CAAC,CAAA,GAAI,CAAC,GAAG,GAAG;QAChB,OAAO,IAAI;IACf;IACA,kBAAkB,KAAK,EAAE;QACrB,IAAI,CAAC,CAAA,GAAI,CAAC,GAAG,GAAG;QAChB,OAAO,IAAI;IACf;IACA,YAAY,KAAK,EAAE;QACf,IAAI,CAAC,CAAA,GAAI,CAAC,GAAG,GAAG;QAChB,OAAO,IAAI;IACf;IACA,mBAAmB,eAAe,EAAE;QAChC,IAAI,IAAI,CAAC,CAAA,eAAgB,EAAE;YACvB,MAAM,IAAI,UAAU;QACxB;QACA,IAAI,CAAC,CAAA,eAAgB,GAAG;QACxB,OAAO,IAAI;IACf;IACA,2BAA2B,UAAU,EAAE;QACnC,IAAI,IAAI,CAAC,CAAA,uBAAwB,EAAE;YAC/B,MAAM,IAAI,UAAU;QACxB;QACA,IAAI,CAAC,CAAA,uBAAwB,GAAG;QAChC,OAAO,IAAI;IACf;IACA,wBAAwB,GAAG,EAAE;QACzB,IAAI,IAAI,CAAC,CAAA,GAAI,EAAE;YACX,MAAM,IAAI,UAAU;QACxB;QACA,IAAI,CAAC,CAAA,GAAI,GAAG;QACZ,OAAO,IAAI;IACf;IACA,wBAAwB,EAAE,EAAE;QACxB,IAAI,IAAI,CAAC,CAAA,EAAG,EAAE;YACV,MAAM,IAAI,UAAU;QACxB;QACA,IAAI,CAAC,CAAA,EAAG,GAAG;QACX,OAAO,IAAI;IACf;IACA,0BAA0B;QACtB,IAAI,CAAC,CAAA,uBAAwB,GAAG;QAChC,OAAO,IAAI;IACf;IACA,2BAA2B;QACvB,IAAI,CAAC,CAAA,wBAAyB,GAAG;QACjC,OAAO,IAAI;IACf;IACA,4BAA4B;QACxB,IAAI,CAAC,CAAA,yBAA0B,GAAG;QAClC,OAAO,IAAI;IACf;IACA,MAAM,QAAQ,GAAG,EAAE,OAAO,EAAE;QACxB,MAAM,MAAM,IAAI,0PAAc,CAAC,IAAI,CAAC,CAAA,GAAI,CAAC,IAAI;QAC7C,IAAI,IAAI,CAAC,CAAA,eAAgB,IACrB,CAAC,IAAI,CAAC,CAAA,uBAAwB,IAC1B,IAAI,CAAC,CAAA,wBAAyB,IAC9B,IAAI,CAAC,CAAA,yBAA0B,GAAG;YACtC,IAAI,CAAC,CAAA,eAAgB,GAAG;gBACpB,GAAG,IAAI,CAAC,CAAA,eAAgB;gBACxB,KAAK,IAAI,CAAC,CAAA,uBAAwB,GAAG,IAAI,CAAC,CAAA,GAAI,CAAC,GAAG,GAAG;gBACrD,KAAK,IAAI,CAAC,CAAA,wBAAyB,GAAG,IAAI,CAAC,CAAA,GAAI,CAAC,GAAG,GAAG;gBACtD,KAAK,IAAI,CAAC,CAAA,yBAA0B,GAAG,IAAI,CAAC,CAAA,GAAI,CAAC,GAAG,GAAG;YAC3D;QACJ;QACA,IAAI,kBAAkB,CAAC,IAAI,CAAC,CAAA,eAAgB;QAC5C,IAAI,IAAI,CAAC,CAAA,EAAG,EAAE;YACV,IAAI,uBAAuB,CAAC,IAAI,CAAC,CAAA,EAAG;QACxC;QACA,IAAI,IAAI,CAAC,CAAA,GAAI,EAAE;YACX,IAAI,uBAAuB,CAAC,IAAI,CAAC,CAAA,GAAI;QACzC;QACA,IAAI,IAAI,CAAC,CAAA,uBAAwB,EAAE;YAC/B,IAAI,0BAA0B,CAAC,IAAI,CAAC,CAAA,uBAAwB;QAChE;QACA,OAAO,IAAI,OAAO,CAAC,KAAK;IAC5B;AACJ", "ignoreList": [0]}}, {"offset": {"line": 2909, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/frontend/node_modules/.pnpm/jose@6.1.0/node_modules/jose/dist/webapi/jwk/thumbprint.js"], "sourcesContent": ["import digest from '../lib/digest.js';\nimport { encode as b64u } from '../util/base64url.js';\nimport { JOSENotSupported, JWKInvalid } from '../util/errors.js';\nimport { encoder } from '../lib/buffer_utils.js';\nimport isKey<PERSON>ike from '../lib/is_key_like.js';\nimport { isJWK } from '../lib/is_jwk.js';\nimport { exportJWK } from '../key/export.js';\nimport invalidKeyInput from '../lib/invalid_key_input.js';\nconst check = (value, description) => {\n    if (typeof value !== 'string' || !value) {\n        throw new JWKInvalid(`${description} missing or invalid`);\n    }\n};\nexport async function calculateJwkThumbprint(key, digestAlgorithm) {\n    let jwk;\n    if (isJWK(key)) {\n        jwk = key;\n    }\n    else if (isKey<PERSON>ike(key)) {\n        jwk = await exportJWK(key);\n    }\n    else {\n        throw new TypeError(invalidKeyInput(key, 'CryptoKey', 'KeyObject', 'JSON Web Key'));\n    }\n    digestAlgorithm ??= 'sha256';\n    if (digestAlgorithm !== 'sha256' &&\n        digestAlgorithm !== 'sha384' &&\n        digestAlgorithm !== 'sha512') {\n        throw new TypeError('digestAlgorithm must one of \"sha256\", \"sha384\", or \"sha512\"');\n    }\n    let components;\n    switch (jwk.kty) {\n        case 'AKP':\n            check(jwk.alg, '\"alg\" (Algorithm) Parameter');\n            check(jwk.pub, '\"pub\" (Public key) Parameter');\n            components = { alg: jwk.alg, kty: jwk.kty, pub: jwk.pub };\n            break;\n        case 'EC':\n            check(jwk.crv, '\"crv\" (Curve) Parameter');\n            check(jwk.x, '\"x\" (X Coordinate) Parameter');\n            check(jwk.y, '\"y\" (Y Coordinate) Parameter');\n            components = { crv: jwk.crv, kty: jwk.kty, x: jwk.x, y: jwk.y };\n            break;\n        case 'OKP':\n            check(jwk.crv, '\"crv\" (Subtype of Key Pair) Parameter');\n            check(jwk.x, '\"x\" (Public Key) Parameter');\n            components = { crv: jwk.crv, kty: jwk.kty, x: jwk.x };\n            break;\n        case 'RSA':\n            check(jwk.e, '\"e\" (Exponent) Parameter');\n            check(jwk.n, '\"n\" (Modulus) Parameter');\n            components = { e: jwk.e, kty: jwk.kty, n: jwk.n };\n            break;\n        case 'oct':\n            check(jwk.k, '\"k\" (Key Value) Parameter');\n            components = { k: jwk.k, kty: jwk.kty };\n            break;\n        default:\n            throw new JOSENotSupported('\"kty\" (Key Type) Parameter missing or unsupported');\n    }\n    const data = encoder.encode(JSON.stringify(components));\n    return b64u(await digest(digestAlgorithm, data));\n}\nexport async function calculateJwkThumbprintUri(key, digestAlgorithm) {\n    digestAlgorithm ??= 'sha256';\n    const thumbprint = await calculateJwkThumbprint(key, digestAlgorithm);\n    return `urn:ietf:params:oauth:jwk-thumbprint:sha-${digestAlgorithm.slice(-3)}:${thumbprint}`;\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AACA,MAAM,QAAQ,CAAC,OAAO;IAClB,IAAI,OAAO,UAAU,YAAY,CAAC,OAAO;QACrC,MAAM,IAAI,2OAAU,CAAC,GAAG,YAAY,mBAAmB,CAAC;IAC5D;AACJ;AACO,eAAe,uBAAuB,GAAG,EAAE,eAAe;IAC7D,IAAI;IACJ,IAAI,IAAA,qOAAK,EAAC,MAAM;QACZ,MAAM;IACV,OACK,IAAI,IAAA,4OAAS,EAAC,MAAM;QACrB,MAAM,MAAM,IAAA,yOAAS,EAAC;IAC1B,OACK;QACD,MAAM,IAAI,UAAU,IAAA,kPAAe,EAAC,KAAK,aAAa,aAAa;IACvE;IACA,oBAAoB;IACpB,IAAI,oBAAoB,YACpB,oBAAoB,YACpB,oBAAoB,UAAU;QAC9B,MAAM,IAAI,UAAU;IACxB;IACA,IAAI;IACJ,OAAQ,IAAI,GAAG;QACX,KAAK;YACD,MAAM,IAAI,GAAG,EAAE;YACf,MAAM,IAAI,GAAG,EAAE;YACf,aAAa;gBAAE,KAAK,IAAI,GAAG;gBAAE,KAAK,IAAI,GAAG;gBAAE,KAAK,IAAI,GAAG;YAAC;YACxD;QACJ,KAAK;YACD,MAAM,IAAI,GAAG,EAAE;YACf,MAAM,IAAI,CAAC,EAAE;YACb,MAAM,IAAI,CAAC,EAAE;YACb,aAAa;gBAAE,KAAK,IAAI,GAAG;gBAAE,KAAK,IAAI,GAAG;gBAAE,GAAG,IAAI,CAAC;gBAAE,GAAG,IAAI,CAAC;YAAC;YAC9D;QACJ,KAAK;YACD,MAAM,IAAI,GAAG,EAAE;YACf,MAAM,IAAI,CAAC,EAAE;YACb,aAAa;gBAAE,KAAK,IAAI,GAAG;gBAAE,KAAK,IAAI,GAAG;gBAAE,GAAG,IAAI,CAAC;YAAC;YACpD;QACJ,KAAK;YACD,MAAM,IAAI,CAAC,EAAE;YACb,MAAM,IAAI,CAAC,EAAE;YACb,aAAa;gBAAE,GAAG,IAAI,CAAC;gBAAE,KAAK,IAAI,GAAG;gBAAE,GAAG,IAAI,CAAC;YAAC;YAChD;QACJ,KAAK;YACD,MAAM,IAAI,CAAC,EAAE;YACb,aAAa;gBAAE,GAAG,IAAI,CAAC;gBAAE,KAAK,IAAI,GAAG;YAAC;YACtC;QACJ;YACI,MAAM,IAAI,iPAAgB,CAAC;IACnC;IACA,MAAM,OAAO,6OAAO,CAAC,MAAM,CAAC,KAAK,SAAS,CAAC;IAC3C,OAAO,IAAA,0OAAI,EAAC,MAAM,IAAA,uOAAM,EAAC,iBAAiB;AAC9C;AACO,eAAe,0BAA0B,GAAG,EAAE,eAAe;IAChE,oBAAoB;IACpB,MAAM,aAAa,MAAM,uBAAuB,KAAK;IACrD,OAAO,CAAC,yCAAyC,EAAE,gBAAgB,KAAK,CAAC,CAAC,GAAG,CAAC,EAAE,YAAY;AAChG", "ignoreList": [0]}}, {"offset": {"line": 3011, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/frontend/node_modules/.pnpm/jose@6.1.0/node_modules/jose/dist/webapi/key/import.js"], "sourcesContent": ["import { decode as decodeBase64URL } from '../util/base64url.js';\nimport { fromSPKI, fromPKCS8, fromX509 } from '../lib/asn1.js';\nimport toCryptoKey from '../lib/jwk_to_key.js';\nimport { JOSENotSupported } from '../util/errors.js';\nimport isObject from '../lib/is_object.js';\nexport async function importSPKI(spki, alg, options) {\n    if (typeof spki !== 'string' || spki.indexOf('-----BEGIN PUBLIC KEY-----') !== 0) {\n        throw new TypeError('\"spki\" must be SPKI formatted string');\n    }\n    return fromSPKI(spki, alg, options);\n}\nexport async function importX509(x509, alg, options) {\n    if (typeof x509 !== 'string' || x509.indexOf('-----BEGIN CERTIFICATE-----') !== 0) {\n        throw new TypeError('\"x509\" must be X.509 formatted string');\n    }\n    return fromX509(x509, alg, options);\n}\nexport async function importPKCS8(pkcs8, alg, options) {\n    if (typeof pkcs8 !== 'string' || pkcs8.indexOf('-----BEGIN PRIVATE KEY-----') !== 0) {\n        throw new TypeError('\"pkcs8\" must be PKCS#8 formatted string');\n    }\n    return fromPKCS8(pkcs8, alg, options);\n}\nexport async function importJWK(jwk, alg, options) {\n    if (!isObject(jwk)) {\n        throw new TypeError('JWK must be an object');\n    }\n    let ext;\n    alg ??= jwk.alg;\n    ext ??= options?.extractable ?? jwk.ext;\n    switch (jwk.kty) {\n        case 'oct':\n            if (typeof jwk.k !== 'string' || !jwk.k) {\n                throw new TypeError('missing \"k\" (Key Value) Parameter value');\n            }\n            return decodeBase64URL(jwk.k);\n        case 'RSA':\n            if ('oth' in jwk && jwk.oth !== undefined) {\n                throw new JOSENotSupported('RSA JWK \"oth\" (Other Primes Info) Parameter value is not supported');\n            }\n            return toCryptoKey({ ...jwk, alg, ext });\n        case 'AKP': {\n            if (typeof jwk.alg !== 'string' || !jwk.alg) {\n                throw new TypeError('missing \"alg\" (Algorithm) Parameter value');\n            }\n            if (alg !== undefined && alg !== jwk.alg) {\n                throw new TypeError('JWK alg and alg option value mismatch');\n            }\n            return toCryptoKey({ ...jwk, ext });\n        }\n        case 'EC':\n        case 'OKP':\n            return toCryptoKey({ ...jwk, alg, ext });\n        default:\n            throw new JOSENotSupported('Unsupported \"kty\" (Key Type) Parameter value');\n    }\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;;;;;;AACO,eAAe,WAAW,IAAI,EAAE,GAAG,EAAE,OAAO;IAC/C,IAAI,OAAO,SAAS,YAAY,KAAK,OAAO,CAAC,kCAAkC,GAAG;QAC9E,MAAM,IAAI,UAAU;IACxB;IACA,OAAO,IAAA,sOAAQ,EAAC,MAAM,KAAK;AAC/B;AACO,eAAe,WAAW,IAAI,EAAE,GAAG,EAAE,OAAO;IAC/C,IAAI,OAAO,SAAS,YAAY,KAAK,OAAO,CAAC,mCAAmC,GAAG;QAC/E,MAAM,IAAI,UAAU;IACxB;IACA,OAAO,IAAA,sOAAQ,EAAC,MAAM,KAAK;AAC/B;AACO,eAAe,YAAY,KAAK,EAAE,GAAG,EAAE,OAAO;IACjD,IAAI,OAAO,UAAU,YAAY,MAAM,OAAO,CAAC,mCAAmC,GAAG;QACjF,MAAM,IAAI,UAAU;IACxB;IACA,OAAO,IAAA,uOAAS,EAAC,OAAO,KAAK;AACjC;AACO,eAAe,UAAU,GAAG,EAAE,GAAG,EAAE,OAAO;IAC7C,IAAI,CAAC,IAAA,0OAAQ,EAAC,MAAM;QAChB,MAAM,IAAI,UAAU;IACxB;IACA,IAAI;IACJ,QAAQ,IAAI,GAAG;IACf,QAAQ,SAAS,eAAe,IAAI,GAAG;IACvC,OAAQ,IAAI,GAAG;QACX,KAAK;YACD,IAAI,OAAO,IAAI,CAAC,KAAK,YAAY,CAAC,IAAI,CAAC,EAAE;gBACrC,MAAM,IAAI,UAAU;YACxB;YACA,OAAO,IAAA,0OAAe,EAAC,IAAI,CAAC;QAChC,KAAK;YACD,IAAI,SAAS,OAAO,IAAI,GAAG,KAAK,WAAW;gBACvC,MAAM,IAAI,iPAAgB,CAAC;YAC/B;YACA,OAAO,IAAA,2OAAW,EAAC;gBAAE,GAAG,GAAG;gBAAE;gBAAK;YAAI;QAC1C,KAAK;YAAO;gBACR,IAAI,OAAO,IAAI,GAAG,KAAK,YAAY,CAAC,IAAI,GAAG,EAAE;oBACzC,MAAM,IAAI,UAAU;gBACxB;gBACA,IAAI,QAAQ,aAAa,QAAQ,IAAI,GAAG,EAAE;oBACtC,MAAM,IAAI,UAAU;gBACxB;gBACA,OAAO,IAAA,2OAAW,EAAC;oBAAE,GAAG,GAAG;oBAAE;gBAAI;YACrC;QACA,KAAK;QACL,KAAK;YACD,OAAO,IAAA,2OAAW,EAAC;gBAAE,GAAG,GAAG;gBAAE;gBAAK;YAAI;QAC1C;YACI,MAAM,IAAI,iPAAgB,CAAC;IACnC;AACJ", "ignoreList": [0]}}, {"offset": {"line": 3099, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/frontend/node_modules/.pnpm/jose@6.1.0/node_modules/jose/dist/webapi/lib/decrypt_key_management.js"], "sourcesContent": ["import * as aeskw from './aeskw.js';\nimport * as ecdhes from './ecdhes.js';\nimport * as pbes2kw from './pbes2kw.js';\nimport * as rsaes from './rsaes.js';\nimport { decode as b64u } from '../util/base64url.js';\nimport { JOSENotSupported, JWEInvalid } from '../util/errors.js';\nimport { bitLength as cekLength } from '../lib/cek.js';\nimport { importJWK } from '../key/import.js';\nimport isObject from './is_object.js';\nimport { unwrap as aesGcmKw } from './aesgcmkw.js';\nimport { assertCryptoKey } from './is_key_like.js';\nexport default async (alg, key, encryptedKey, joseHeader, options) => {\n    switch (alg) {\n        case 'dir': {\n            if (encryptedKey !== undefined)\n                throw new JWEInvalid('Encountered unexpected JWE Encrypted Key');\n            return key;\n        }\n        case 'ECDH-ES':\n            if (encryptedKey !== undefined)\n                throw new JWEInvalid('Encountered unexpected JWE Encrypted Key');\n        case 'ECDH-ES+A128KW':\n        case 'ECDH-ES+A192KW':\n        case 'ECDH-ES+A256KW': {\n            if (!isObject(joseHeader.epk))\n                throw new JWEInvalid(`JOSE Header \"epk\" (Ephemeral Public Key) missing or invalid`);\n            assertCryptoKey(key);\n            if (!ecdhes.allowed(key))\n                throw new JOSENotSupported('ECDH with the provided key is not allowed or not supported by your javascript runtime');\n            const epk = await importJWK(joseHeader.epk, alg);\n            assertCryptoKey(epk);\n            let partyUInfo;\n            let partyVInfo;\n            if (joseHeader.apu !== undefined) {\n                if (typeof joseHeader.apu !== 'string')\n                    throw new JWEInvalid(`JOSE Header \"apu\" (Agreement PartyUInfo) invalid`);\n                try {\n                    partyUInfo = b64u(joseHeader.apu);\n                }\n                catch {\n                    throw new JWEInvalid('Failed to base64url decode the apu');\n                }\n            }\n            if (joseHeader.apv !== undefined) {\n                if (typeof joseHeader.apv !== 'string')\n                    throw new JWEInvalid(`JOSE Header \"apv\" (Agreement PartyVInfo) invalid`);\n                try {\n                    partyVInfo = b64u(joseHeader.apv);\n                }\n                catch {\n                    throw new JWEInvalid('Failed to base64url decode the apv');\n                }\n            }\n            const sharedSecret = await ecdhes.deriveKey(epk, key, alg === 'ECDH-ES' ? joseHeader.enc : alg, alg === 'ECDH-ES' ? cekLength(joseHeader.enc) : parseInt(alg.slice(-5, -2), 10), partyUInfo, partyVInfo);\n            if (alg === 'ECDH-ES')\n                return sharedSecret;\n            if (encryptedKey === undefined)\n                throw new JWEInvalid('JWE Encrypted Key missing');\n            return aeskw.unwrap(alg.slice(-6), sharedSecret, encryptedKey);\n        }\n        case 'RSA-OAEP':\n        case 'RSA-OAEP-256':\n        case 'RSA-OAEP-384':\n        case 'RSA-OAEP-512': {\n            if (encryptedKey === undefined)\n                throw new JWEInvalid('JWE Encrypted Key missing');\n            assertCryptoKey(key);\n            return rsaes.decrypt(alg, key, encryptedKey);\n        }\n        case 'PBES2-HS256+A128KW':\n        case 'PBES2-HS384+A192KW':\n        case 'PBES2-HS512+A256KW': {\n            if (encryptedKey === undefined)\n                throw new JWEInvalid('JWE Encrypted Key missing');\n            if (typeof joseHeader.p2c !== 'number')\n                throw new JWEInvalid(`JOSE Header \"p2c\" (PBES2 Count) missing or invalid`);\n            const p2cLimit = options?.maxPBES2Count || 10_000;\n            if (joseHeader.p2c > p2cLimit)\n                throw new JWEInvalid(`JOSE Header \"p2c\" (PBES2 Count) out is of acceptable bounds`);\n            if (typeof joseHeader.p2s !== 'string')\n                throw new JWEInvalid(`JOSE Header \"p2s\" (PBES2 Salt) missing or invalid`);\n            let p2s;\n            try {\n                p2s = b64u(joseHeader.p2s);\n            }\n            catch {\n                throw new JWEInvalid('Failed to base64url decode the p2s');\n            }\n            return pbes2kw.unwrap(alg, key, encryptedKey, joseHeader.p2c, p2s);\n        }\n        case 'A128KW':\n        case 'A192KW':\n        case 'A256KW': {\n            if (encryptedKey === undefined)\n                throw new JWEInvalid('JWE Encrypted Key missing');\n            return aeskw.unwrap(alg, key, encryptedKey);\n        }\n        case 'A128GCMKW':\n        case 'A192GCMKW':\n        case 'A256GCMKW': {\n            if (encryptedKey === undefined)\n                throw new JWEInvalid('JWE Encrypted Key missing');\n            if (typeof joseHeader.iv !== 'string')\n                throw new JWEInvalid(`JOSE Header \"iv\" (Initialization Vector) missing or invalid`);\n            if (typeof joseHeader.tag !== 'string')\n                throw new JWEInvalid(`JOSE Header \"tag\" (Authentication Tag) missing or invalid`);\n            let iv;\n            try {\n                iv = b64u(joseHeader.iv);\n            }\n            catch {\n                throw new JWEInvalid('Failed to base64url decode the iv');\n            }\n            let tag;\n            try {\n                tag = b64u(joseHeader.tag);\n            }\n            catch {\n                throw new JWEInvalid('Failed to base64url decode the tag');\n            }\n            return aesGcmKw(alg, key, encryptedKey, iv, tag);\n        }\n        default: {\n            throw new JOSENotSupported('Invalid or unsupported \"alg\" (JWE Algorithm) header value');\n        }\n    }\n};\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;uCACe,OAAO,KAAK,KAAK,cAAc,YAAY;IACtD,OAAQ;QACJ,KAAK;YAAO;gBACR,IAAI,iBAAiB,WACjB,MAAM,IAAI,2OAAU,CAAC;gBACzB,OAAO;YACX;QACA,KAAK;YACD,IAAI,iBAAiB,WACjB,MAAM,IAAI,2OAAU,CAAC;QAC7B,KAAK;QACL,KAAK;QACL,KAAK;YAAkB;gBACnB,IAAI,CAAC,IAAA,0OAAQ,EAAC,WAAW,GAAG,GACxB,MAAM,IAAI,2OAAU,CAAC,CAAC,2DAA2D,CAAC;gBACtF,IAAA,oPAAe,EAAC;gBAChB,IAAI,CAAC,uOAAc,CAAC,MAChB,MAAM,IAAI,iPAAgB,CAAC;gBAC/B,MAAM,MAAM,MAAM,IAAA,yOAAS,EAAC,WAAW,GAAG,EAAE;gBAC5C,IAAA,oPAAe,EAAC;gBAChB,IAAI;gBACJ,IAAI;gBACJ,IAAI,WAAW,GAAG,KAAK,WAAW;oBAC9B,IAAI,OAAO,WAAW,GAAG,KAAK,UAC1B,MAAM,IAAI,2OAAU,CAAC,CAAC,gDAAgD,CAAC;oBAC3E,IAAI;wBACA,aAAa,IAAA,0OAAI,EAAC,WAAW,GAAG;oBACpC,EACA,OAAM;wBACF,MAAM,IAAI,2OAAU,CAAC;oBACzB;gBACJ;gBACA,IAAI,WAAW,GAAG,KAAK,WAAW;oBAC9B,IAAI,OAAO,WAAW,GAAG,KAAK,UAC1B,MAAM,IAAI,2OAAU,CAAC,CAAC,gDAAgD,CAAC;oBAC3E,IAAI;wBACA,aAAa,IAAA,0OAAI,EAAC,WAAW,GAAG;oBACpC,EACA,OAAM;wBACF,MAAM,IAAI,2OAAU,CAAC;oBACzB;gBACJ;gBACA,MAAM,eAAe,MAAM,yOAAgB,CAAC,KAAK,KAAK,QAAQ,YAAY,WAAW,GAAG,GAAG,KAAK,QAAQ,YAAY,IAAA,sOAAS,EAAC,WAAW,GAAG,IAAI,SAAS,IAAI,KAAK,CAAC,CAAC,GAAG,CAAC,IAAI,KAAK,YAAY;gBAC7L,IAAI,QAAQ,WACR,OAAO;gBACX,IAAI,iBAAiB,WACjB,MAAM,IAAI,2OAAU,CAAC;gBACzB,OAAO,qOAAY,CAAC,IAAI,KAAK,CAAC,CAAC,IAAI,cAAc;YACrD;QACA,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YAAgB;gBACjB,IAAI,iBAAiB,WACjB,MAAM,IAAI,2OAAU,CAAC;gBACzB,IAAA,oPAAe,EAAC;gBAChB,OAAO,sOAAa,CAAC,KAAK,KAAK;YACnC;QACA,KAAK;QACL,KAAK;QACL,KAAK;YAAsB;gBACvB,IAAI,iBAAiB,WACjB,MAAM,IAAI,2OAAU,CAAC;gBACzB,IAAI,OAAO,WAAW,GAAG,KAAK,UAC1B,MAAM,IAAI,2OAAU,CAAC,CAAC,kDAAkD,CAAC;gBAC7E,MAAM,WAAW,SAAS,iBAAiB;gBAC3C,IAAI,WAAW,GAAG,GAAG,UACjB,MAAM,IAAI,2OAAU,CAAC,CAAC,2DAA2D,CAAC;gBACtF,IAAI,OAAO,WAAW,GAAG,KAAK,UAC1B,MAAM,IAAI,2OAAU,CAAC,CAAC,iDAAiD,CAAC;gBAC5E,IAAI;gBACJ,IAAI;oBACA,MAAM,IAAA,0OAAI,EAAC,WAAW,GAAG;gBAC7B,EACA,OAAM;oBACF,MAAM,IAAI,2OAAU,CAAC;gBACzB;gBACA,OAAO,uOAAc,CAAC,KAAK,KAAK,cAAc,WAAW,GAAG,EAAE;YAClE;QACA,KAAK;QACL,KAAK;QACL,KAAK;YAAU;gBACX,IAAI,iBAAiB,WACjB,MAAM,IAAI,2OAAU,CAAC;gBACzB,OAAO,qOAAY,CAAC,KAAK,KAAK;YAClC;QACA,KAAK;QACL,KAAK;QACL,KAAK;YAAa;gBACd,IAAI,iBAAiB,WACjB,MAAM,IAAI,2OAAU,CAAC;gBACzB,IAAI,OAAO,WAAW,EAAE,KAAK,UACzB,MAAM,IAAI,2OAAU,CAAC,CAAC,2DAA2D,CAAC;gBACtF,IAAI,OAAO,WAAW,GAAG,KAAK,UAC1B,MAAM,IAAI,2OAAU,CAAC,CAAC,yDAAyD,CAAC;gBACpF,IAAI;gBACJ,IAAI;oBACA,KAAK,IAAA,0OAAI,EAAC,WAAW,EAAE;gBAC3B,EACA,OAAM;oBACF,MAAM,IAAI,2OAAU,CAAC;gBACzB;gBACA,IAAI;gBACJ,IAAI;oBACA,MAAM,IAAA,0OAAI,EAAC,WAAW,GAAG;gBAC7B,EACA,OAAM;oBACF,MAAM,IAAI,2OAAU,CAAC;gBACzB;gBACA,OAAO,IAAA,wOAAQ,EAAC,KAAK,KAAK,cAAc,IAAI;YAChD;QACA;YAAS;gBACL,MAAM,IAAI,iPAAgB,CAAC;YAC/B;IACJ;AACJ", "ignoreList": [0]}}, {"offset": {"line": 3230, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/frontend/node_modules/.pnpm/jose@6.1.0/node_modules/jose/dist/webapi/lib/validate_algorithms.js"], "sourcesContent": ["export default (option, algorithms) => {\n    if (algorithms !== undefined &&\n        (!Array.isArray(algorithms) || algorithms.some((s) => typeof s !== 'string'))) {\n        throw new TypeError(`\"${option}\" option must be an array of strings`);\n    }\n    if (!algorithms) {\n        return undefined;\n    }\n    return new Set(algorithms);\n};\n"], "names": [], "mappings": ";;;;uCAAe,CAAC,QAAQ;IACpB,IAAI,eAAe,aACf,CAAC,CAAC,MAAM,OAAO,CAAC,eAAe,WAAW,IAAI,CAAC,CAAC,IAAM,OAAO,MAAM,SAAS,GAAG;QAC/E,MAAM,IAAI,UAAU,CAAC,CAAC,EAAE,OAAO,oCAAoC,CAAC;IACxE;IACA,IAAI,CAAC,YAAY;QACb,OAAO;IACX;IACA,OAAO,IAAI,IAAI;AACnB", "ignoreList": [0]}}, {"offset": {"line": 3247, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/frontend/node_modules/.pnpm/jose@6.1.0/node_modules/jose/dist/webapi/jwe/flattened/decrypt.js"], "sourcesContent": ["import { decode as b64u } from '../../util/base64url.js';\nimport decrypt from '../../lib/decrypt.js';\nimport { JOSEAlgNotAllowed, JOSENotSupported, JWEInvalid } from '../../util/errors.js';\nimport isDisjoint from '../../lib/is_disjoint.js';\nimport isObject from '../../lib/is_object.js';\nimport decryptKeyManagement from '../../lib/decrypt_key_management.js';\nimport { encoder, decoder, concat } from '../../lib/buffer_utils.js';\nimport generateCek from '../../lib/cek.js';\nimport validateCrit from '../../lib/validate_crit.js';\nimport validateAlgorithms from '../../lib/validate_algorithms.js';\nimport normalizeKey from '../../lib/normalize_key.js';\nimport checkKeyType from '../../lib/check_key_type.js';\nexport async function flattenedDecrypt(jwe, key, options) {\n    if (!isObject(jwe)) {\n        throw new JWEInvalid('Flattened JWE must be an object');\n    }\n    if (jwe.protected === undefined && jwe.header === undefined && jwe.unprotected === undefined) {\n        throw new JWEInvalid('JOSE Header missing');\n    }\n    if (jwe.iv !== undefined && typeof jwe.iv !== 'string') {\n        throw new JWEInvalid('JWE Initialization Vector incorrect type');\n    }\n    if (typeof jwe.ciphertext !== 'string') {\n        throw new JWEInvalid('JWE Ciphertext missing or incorrect type');\n    }\n    if (jwe.tag !== undefined && typeof jwe.tag !== 'string') {\n        throw new JWEInvalid('JWE Authentication Tag incorrect type');\n    }\n    if (jwe.protected !== undefined && typeof jwe.protected !== 'string') {\n        throw new JWEInvalid('JWE Protected Header incorrect type');\n    }\n    if (jwe.encrypted_key !== undefined && typeof jwe.encrypted_key !== 'string') {\n        throw new JWEInvalid('JWE Encrypted Key incorrect type');\n    }\n    if (jwe.aad !== undefined && typeof jwe.aad !== 'string') {\n        throw new JWEInvalid('JWE AAD incorrect type');\n    }\n    if (jwe.header !== undefined && !isObject(jwe.header)) {\n        throw new JWEInvalid('JWE Shared Unprotected Header incorrect type');\n    }\n    if (jwe.unprotected !== undefined && !isObject(jwe.unprotected)) {\n        throw new JWEInvalid('JWE Per-Recipient Unprotected Header incorrect type');\n    }\n    let parsedProt;\n    if (jwe.protected) {\n        try {\n            const protectedHeader = b64u(jwe.protected);\n            parsedProt = JSON.parse(decoder.decode(protectedHeader));\n        }\n        catch {\n            throw new JWEInvalid('JWE Protected Header is invalid');\n        }\n    }\n    if (!isDisjoint(parsedProt, jwe.header, jwe.unprotected)) {\n        throw new JWEInvalid('JWE Protected, JWE Unprotected Header, and JWE Per-Recipient Unprotected Header Parameter names must be disjoint');\n    }\n    const joseHeader = {\n        ...parsedProt,\n        ...jwe.header,\n        ...jwe.unprotected,\n    };\n    validateCrit(JWEInvalid, new Map(), options?.crit, parsedProt, joseHeader);\n    if (joseHeader.zip !== undefined) {\n        throw new JOSENotSupported('JWE \"zip\" (Compression Algorithm) Header Parameter is not supported.');\n    }\n    const { alg, enc } = joseHeader;\n    if (typeof alg !== 'string' || !alg) {\n        throw new JWEInvalid('missing JWE Algorithm (alg) in JWE Header');\n    }\n    if (typeof enc !== 'string' || !enc) {\n        throw new JWEInvalid('missing JWE Encryption Algorithm (enc) in JWE Header');\n    }\n    const keyManagementAlgorithms = options && validateAlgorithms('keyManagementAlgorithms', options.keyManagementAlgorithms);\n    const contentEncryptionAlgorithms = options &&\n        validateAlgorithms('contentEncryptionAlgorithms', options.contentEncryptionAlgorithms);\n    if ((keyManagementAlgorithms && !keyManagementAlgorithms.has(alg)) ||\n        (!keyManagementAlgorithms && alg.startsWith('PBES2'))) {\n        throw new JOSEAlgNotAllowed('\"alg\" (Algorithm) Header Parameter value not allowed');\n    }\n    if (contentEncryptionAlgorithms && !contentEncryptionAlgorithms.has(enc)) {\n        throw new JOSEAlgNotAllowed('\"enc\" (Encryption Algorithm) Header Parameter value not allowed');\n    }\n    let encryptedKey;\n    if (jwe.encrypted_key !== undefined) {\n        try {\n            encryptedKey = b64u(jwe.encrypted_key);\n        }\n        catch {\n            throw new JWEInvalid('Failed to base64url decode the encrypted_key');\n        }\n    }\n    let resolvedKey = false;\n    if (typeof key === 'function') {\n        key = await key(parsedProt, jwe);\n        resolvedKey = true;\n    }\n    checkKeyType(alg === 'dir' ? enc : alg, key, 'decrypt');\n    const k = await normalizeKey(key, alg);\n    let cek;\n    try {\n        cek = await decryptKeyManagement(alg, k, encryptedKey, joseHeader, options);\n    }\n    catch (err) {\n        if (err instanceof TypeError || err instanceof JWEInvalid || err instanceof JOSENotSupported) {\n            throw err;\n        }\n        cek = generateCek(enc);\n    }\n    let iv;\n    let tag;\n    if (jwe.iv !== undefined) {\n        try {\n            iv = b64u(jwe.iv);\n        }\n        catch {\n            throw new JWEInvalid('Failed to base64url decode the iv');\n        }\n    }\n    if (jwe.tag !== undefined) {\n        try {\n            tag = b64u(jwe.tag);\n        }\n        catch {\n            throw new JWEInvalid('Failed to base64url decode the tag');\n        }\n    }\n    const protectedHeader = encoder.encode(jwe.protected ?? '');\n    let additionalData;\n    if (jwe.aad !== undefined) {\n        additionalData = concat(protectedHeader, encoder.encode('.'), encoder.encode(jwe.aad));\n    }\n    else {\n        additionalData = protectedHeader;\n    }\n    let ciphertext;\n    try {\n        ciphertext = b64u(jwe.ciphertext);\n    }\n    catch {\n        throw new JWEInvalid('Failed to base64url decode the ciphertext');\n    }\n    const plaintext = await decrypt(enc, cek, ciphertext, iv, tag, additionalData);\n    const result = { plaintext };\n    if (jwe.protected !== undefined) {\n        result.protectedHeader = parsedProt;\n    }\n    if (jwe.aad !== undefined) {\n        try {\n            result.additionalAuthenticatedData = b64u(jwe.aad);\n        }\n        catch {\n            throw new JWEInvalid('Failed to base64url decode the aad');\n        }\n    }\n    if (jwe.unprotected !== undefined) {\n        result.sharedUnprotectedHeader = jwe.unprotected;\n    }\n    if (jwe.header !== undefined) {\n        result.unprotectedHeader = jwe.header;\n    }\n    if (resolvedKey) {\n        return { ...result, key: k };\n    }\n    return result;\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;AACO,eAAe,iBAAiB,GAAG,EAAE,GAAG,EAAE,OAAO;IACpD,IAAI,CAAC,IAAA,0OAAQ,EAAC,MAAM;QAChB,MAAM,IAAI,2OAAU,CAAC;IACzB;IACA,IAAI,IAAI,SAAS,KAAK,aAAa,IAAI,MAAM,KAAK,aAAa,IAAI,WAAW,KAAK,WAAW;QAC1F,MAAM,IAAI,2OAAU,CAAC;IACzB;IACA,IAAI,IAAI,EAAE,KAAK,aAAa,OAAO,IAAI,EAAE,KAAK,UAAU;QACpD,MAAM,IAAI,2OAAU,CAAC;IACzB;IACA,IAAI,OAAO,IAAI,UAAU,KAAK,UAAU;QACpC,MAAM,IAAI,2OAAU,CAAC;IACzB;IACA,IAAI,IAAI,GAAG,KAAK,aAAa,OAAO,IAAI,GAAG,KAAK,UAAU;QACtD,MAAM,IAAI,2OAAU,CAAC;IACzB;IACA,IAAI,IAAI,SAAS,KAAK,aAAa,OAAO,IAAI,SAAS,KAAK,UAAU;QAClE,MAAM,IAAI,2OAAU,CAAC;IACzB;IACA,IAAI,IAAI,aAAa,KAAK,aAAa,OAAO,IAAI,aAAa,KAAK,UAAU;QAC1E,MAAM,IAAI,2OAAU,CAAC;IACzB;IACA,IAAI,IAAI,GAAG,KAAK,aAAa,OAAO,IAAI,GAAG,KAAK,UAAU;QACtD,MAAM,IAAI,2OAAU,CAAC;IACzB;IACA,IAAI,IAAI,MAAM,KAAK,aAAa,CAAC,IAAA,0OAAQ,EAAC,IAAI,MAAM,GAAG;QACnD,MAAM,IAAI,2OAAU,CAAC;IACzB;IACA,IAAI,IAAI,WAAW,KAAK,aAAa,CAAC,IAAA,0OAAQ,EAAC,IAAI,WAAW,GAAG;QAC7D,MAAM,IAAI,2OAAU,CAAC;IACzB;IACA,IAAI;IACJ,IAAI,IAAI,SAAS,EAAE;QACf,IAAI;YACA,MAAM,kBAAkB,IAAA,0OAAI,EAAC,IAAI,SAAS;YAC1C,aAAa,KAAK,KAAK,CAAC,6OAAO,CAAC,MAAM,CAAC;QAC3C,EACA,OAAM;YACF,MAAM,IAAI,2OAAU,CAAC;QACzB;IACJ;IACA,IAAI,CAAC,IAAA,4OAAU,EAAC,YAAY,IAAI,MAAM,EAAE,IAAI,WAAW,GAAG;QACtD,MAAM,IAAI,2OAAU,CAAC;IACzB;IACA,MAAM,aAAa;QACf,GAAG,UAAU;QACb,GAAG,IAAI,MAAM;QACb,GAAG,IAAI,WAAW;IACtB;IACA,IAAA,8OAAY,EAAC,2OAAU,EAAE,IAAI,OAAO,SAAS,MAAM,YAAY;IAC/D,IAAI,WAAW,GAAG,KAAK,WAAW;QAC9B,MAAM,IAAI,iPAAgB,CAAC;IAC/B;IACA,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;IACrB,IAAI,OAAO,QAAQ,YAAY,CAAC,KAAK;QACjC,MAAM,IAAI,2OAAU,CAAC;IACzB;IACA,IAAI,OAAO,QAAQ,YAAY,CAAC,KAAK;QACjC,MAAM,IAAI,2OAAU,CAAC;IACzB;IACA,MAAM,0BAA0B,WAAW,IAAA,oPAAkB,EAAC,2BAA2B,QAAQ,uBAAuB;IACxH,MAAM,8BAA8B,WAChC,IAAA,oPAAkB,EAAC,+BAA+B,QAAQ,2BAA2B;IACzF,IAAI,AAAC,2BAA2B,CAAC,wBAAwB,GAAG,CAAC,QACxD,CAAC,2BAA2B,IAAI,UAAU,CAAC,UAAW;QACvD,MAAM,IAAI,kPAAiB,CAAC;IAChC;IACA,IAAI,+BAA+B,CAAC,4BAA4B,GAAG,CAAC,MAAM;QACtE,MAAM,IAAI,kPAAiB,CAAC;IAChC;IACA,IAAI;IACJ,IAAI,IAAI,aAAa,KAAK,WAAW;QACjC,IAAI;YACA,eAAe,IAAA,0OAAI,EAAC,IAAI,aAAa;QACzC,EACA,OAAM;YACF,MAAM,IAAI,2OAAU,CAAC;QACzB;IACJ;IACA,IAAI,cAAc;IAClB,IAAI,OAAO,QAAQ,YAAY;QAC3B,MAAM,MAAM,IAAI,YAAY;QAC5B,cAAc;IAClB;IACA,IAAA,+OAAY,EAAC,QAAQ,QAAQ,MAAM,KAAK,KAAK;IAC7C,MAAM,IAAI,MAAM,IAAA,8OAAY,EAAC,KAAK;IAClC,IAAI;IACJ,IAAI;QACA,MAAM,MAAM,IAAA,uPAAoB,EAAC,KAAK,GAAG,cAAc,YAAY;IACvE,EACA,OAAO,KAAK;QACR,IAAI,eAAe,aAAa,eAAe,2OAAU,IAAI,eAAe,iPAAgB,EAAE;YAC1F,MAAM;QACV;QACA,MAAM,IAAA,oOAAW,EAAC;IACtB;IACA,IAAI;IACJ,IAAI;IACJ,IAAI,IAAI,EAAE,KAAK,WAAW;QACtB,IAAI;YACA,KAAK,IAAA,0OAAI,EAAC,IAAI,EAAE;QACpB,EACA,OAAM;YACF,MAAM,IAAI,2OAAU,CAAC;QACzB;IACJ;IACA,IAAI,IAAI,GAAG,KAAK,WAAW;QACvB,IAAI;YACA,MAAM,IAAA,0OAAI,EAAC,IAAI,GAAG;QACtB,EACA,OAAM;YACF,MAAM,IAAI,2OAAU,CAAC;QACzB;IACJ;IACA,MAAM,kBAAkB,6OAAO,CAAC,MAAM,CAAC,IAAI,SAAS,IAAI;IACxD,IAAI;IACJ,IAAI,IAAI,GAAG,KAAK,WAAW;QACvB,iBAAiB,IAAA,4OAAM,EAAC,iBAAiB,6OAAO,CAAC,MAAM,CAAC,MAAM,6OAAO,CAAC,MAAM,CAAC,IAAI,GAAG;IACxF,OACK;QACD,iBAAiB;IACrB;IACA,IAAI;IACJ,IAAI;QACA,aAAa,IAAA,0OAAI,EAAC,IAAI,UAAU;IACpC,EACA,OAAM;QACF,MAAM,IAAI,2OAAU,CAAC;IACzB;IACA,MAAM,YAAY,MAAM,IAAA,wOAAO,EAAC,KAAK,KAAK,YAAY,IAAI,KAAK;IAC/D,MAAM,SAAS;QAAE;IAAU;IAC3B,IAAI,IAAI,SAAS,KAAK,WAAW;QAC7B,OAAO,eAAe,GAAG;IAC7B;IACA,IAAI,IAAI,GAAG,KAAK,WAAW;QACvB,IAAI;YACA,OAAO,2BAA2B,GAAG,IAAA,0OAAI,EAAC,IAAI,GAAG;QACrD,EACA,OAAM;YACF,MAAM,IAAI,2OAAU,CAAC;QACzB;IACJ;IACA,IAAI,IAAI,WAAW,KAAK,WAAW;QAC/B,OAAO,uBAAuB,GAAG,IAAI,WAAW;IACpD;IACA,IAAI,IAAI,MAAM,KAAK,WAAW;QAC1B,OAAO,iBAAiB,GAAG,IAAI,MAAM;IACzC;IACA,IAAI,aAAa;QACb,OAAO;YAAE,GAAG,MAAM;YAAE,KAAK;QAAE;IAC/B;IACA,OAAO;AACX", "ignoreList": [0]}}, {"offset": {"line": 3427, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/frontend/node_modules/.pnpm/jose@6.1.0/node_modules/jose/dist/webapi/jwe/compact/decrypt.js"], "sourcesContent": ["import { flattenedDecrypt } from '../flattened/decrypt.js';\nimport { JWEInvalid } from '../../util/errors.js';\nimport { decoder } from '../../lib/buffer_utils.js';\nexport async function compactDecrypt(jwe, key, options) {\n    if (jwe instanceof Uint8Array) {\n        jwe = decoder.decode(jwe);\n    }\n    if (typeof jwe !== 'string') {\n        throw new JWEInvalid('Compact JWE must be a string or Uint8Array');\n    }\n    const { 0: protectedHeader, 1: encryptedKey, 2: iv, 3: ciphertext, 4: tag, length, } = jwe.split('.');\n    if (length !== 5) {\n        throw new JWEInvalid('Invalid Compact JWE');\n    }\n    const decrypted = await flattenedDecrypt({\n        ciphertext,\n        iv: iv || undefined,\n        protected: protectedHeader,\n        tag: tag || undefined,\n        encrypted_key: encryptedKey || undefined,\n    }, key, options);\n    const result = { plaintext: decrypted.plaintext, protectedHeader: decrypted.protectedHeader };\n    if (typeof key === 'function') {\n        return { ...result, key: decrypted.key };\n    }\n    return result;\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AACO,eAAe,eAAe,GAAG,EAAE,GAAG,EAAE,OAAO;IAClD,IAAI,eAAe,YAAY;QAC3B,MAAM,6OAAO,CAAC,MAAM,CAAC;IACzB;IACA,IAAI,OAAO,QAAQ,UAAU;QACzB,MAAM,IAAI,2OAAU,CAAC;IACzB;IACA,MAAM,EAAE,GAAG,eAAe,EAAE,GAAG,YAAY,EAAE,GAAG,EAAE,EAAE,GAAG,UAAU,EAAE,GAAG,GAAG,EAAE,MAAM,EAAG,GAAG,IAAI,KAAK,CAAC;IACjG,IAAI,WAAW,GAAG;QACd,MAAM,IAAI,2OAAU,CAAC;IACzB;IACA,MAAM,YAAY,MAAM,IAAA,8PAAgB,EAAC;QACrC;QACA,IAAI,MAAM;QACV,WAAW;QACX,KAAK,OAAO;QACZ,eAAe,gBAAgB;IACnC,GAAG,KAAK;IACR,MAAM,SAAS;QAAE,WAAW,UAAU,SAAS;QAAE,iBAAiB,UAAU,eAAe;IAAC;IAC5F,IAAI,OAAO,QAAQ,YAAY;QAC3B,OAAO;YAAE,GAAG,MAAM;YAAE,KAAK,UAAU,GAAG;QAAC;IAC3C;IACA,OAAO;AACX", "ignoreList": [0]}}, {"offset": {"line": 3471, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/frontend/node_modules/.pnpm/jose@6.1.0/node_modules/jose/dist/webapi/jwt/decrypt.js"], "sourcesContent": ["import { compactDecrypt } from '../jwe/compact/decrypt.js';\nimport { validateClaimsSet } from '../lib/jwt_claims_set.js';\nimport { JWTClaimValidationFailed } from '../util/errors.js';\nexport async function jwtDecrypt(jwt, key, options) {\n    const decrypted = await compactDecrypt(jwt, key, options);\n    const payload = validateClaimsSet(decrypted.protectedHeader, decrypted.plaintext, options);\n    const { protectedHeader } = decrypted;\n    if (protectedHeader.iss !== undefined && protectedHeader.iss !== payload.iss) {\n        throw new JWTClaimValidationFailed('replicated \"iss\" claim header parameter mismatch', payload, 'iss', 'mismatch');\n    }\n    if (protectedHeader.sub !== undefined && protectedHeader.sub !== payload.sub) {\n        throw new JWTClaimValidationFailed('replicated \"sub\" claim header parameter mismatch', payload, 'sub', 'mismatch');\n    }\n    if (protectedHeader.aud !== undefined &&\n        JSON.stringify(protectedHeader.aud) !== JSON.stringify(payload.aud)) {\n        throw new JWTClaimValidationFailed('replicated \"aud\" claim header parameter mismatch', payload, 'aud', 'mismatch');\n    }\n    const result = { payload, protectedHeader };\n    if (typeof key === 'function') {\n        return { ...result, key: decrypted.key };\n    }\n    return result;\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AACO,eAAe,WAAW,GAAG,EAAE,GAAG,EAAE,OAAO;IAC9C,MAAM,YAAY,MAAM,IAAA,0PAAc,EAAC,KAAK,KAAK;IACjD,MAAM,UAAU,IAAA,yPAAiB,EAAC,UAAU,eAAe,EAAE,UAAU,SAAS,EAAE;IAClF,MAAM,EAAE,eAAe,EAAE,GAAG;IAC5B,IAAI,gBAAgB,GAAG,KAAK,aAAa,gBAAgB,GAAG,KAAK,QAAQ,GAAG,EAAE;QAC1E,MAAM,IAAI,yPAAwB,CAAC,oDAAoD,SAAS,OAAO;IAC3G;IACA,IAAI,gBAAgB,GAAG,KAAK,aAAa,gBAAgB,GAAG,KAAK,QAAQ,GAAG,EAAE;QAC1E,MAAM,IAAI,yPAAwB,CAAC,oDAAoD,SAAS,OAAO;IAC3G;IACA,IAAI,gBAAgB,GAAG,KAAK,aACxB,KAAK,SAAS,CAAC,gBAAgB,GAAG,MAAM,KAAK,SAAS,CAAC,QAAQ,GAAG,GAAG;QACrE,MAAM,IAAI,yPAAwB,CAAC,oDAAoD,SAAS,OAAO;IAC3G;IACA,MAAM,SAAS;QAAE;QAAS;IAAgB;IAC1C,IAAI,OAAO,QAAQ,YAAY;QAC3B,OAAO;YAAE,GAAG,MAAM;YAAE,KAAK,UAAU,GAAG;QAAC;IAC3C;IACA,OAAO;AACX", "ignoreList": [0]}}, {"offset": {"line": 3510, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/frontend/node_modules/.pnpm/jose@6.1.0/node_modules/jose/dist/webapi/util/decode_jwt.js"], "sourcesContent": ["import { decode as b64u } from './base64url.js';\nimport { decoder } from '../lib/buffer_utils.js';\nimport isObject from '../lib/is_object.js';\nimport { JWTInvalid } from './errors.js';\nexport function decodeJwt(jwt) {\n    if (typeof jwt !== 'string')\n        throw new JWTInvalid('JWTs must use Compact JWS serialization, JWT must be a string');\n    const { 1: payload, length } = jwt.split('.');\n    if (length === 5)\n        throw new JWTInvalid('Only JWTs using Compact JWS serialization can be decoded');\n    if (length !== 3)\n        throw new JWTInvalid('Invalid JWT');\n    if (!payload)\n        throw new JWTInvalid('JWTs must contain a payload');\n    let decoded;\n    try {\n        decoded = b64u(payload);\n    }\n    catch {\n        throw new JWTInvalid('Failed to base64url decode the payload');\n    }\n    let result;\n    try {\n        result = JSON.parse(decoder.decode(decoded));\n    }\n    catch {\n        throw new JWTInvalid('Failed to parse the decoded payload as JSON');\n    }\n    if (!isObject(result))\n        throw new JWTInvalid('Invalid JWT Claims Set');\n    return result;\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;AACO,SAAS,UAAU,GAAG;IACzB,IAAI,OAAO,QAAQ,UACf,MAAM,IAAI,2OAAU,CAAC;IACzB,MAAM,EAAE,GAAG,OAAO,EAAE,MAAM,EAAE,GAAG,IAAI,KAAK,CAAC;IACzC,IAAI,WAAW,GACX,MAAM,IAAI,2OAAU,CAAC;IACzB,IAAI,WAAW,GACX,MAAM,IAAI,2OAAU,CAAC;IACzB,IAAI,CAAC,SACD,MAAM,IAAI,2OAAU,CAAC;IACzB,IAAI;IACJ,IAAI;QACA,UAAU,IAAA,0OAAI,EAAC;IACnB,EACA,OAAM;QACF,MAAM,IAAI,2OAAU,CAAC;IACzB;IACA,IAAI;IACJ,IAAI;QACA,SAAS,KAAK,KAAK,CAAC,6OAAO,CAAC,MAAM,CAAC;IACvC,EACA,OAAM;QACF,MAAM,IAAI,2OAAU,CAAC;IACzB;IACA,IAAI,CAAC,IAAA,0OAAQ,EAAC,SACV,MAAM,IAAI,2OAAU,CAAC;IACzB,OAAO;AACX", "ignoreList": [0]}}]}