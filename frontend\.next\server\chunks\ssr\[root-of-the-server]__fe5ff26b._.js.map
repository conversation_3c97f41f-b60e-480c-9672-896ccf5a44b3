{"version": 3, "sources": [], "sections": [{"offset": {"line": 10, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/components/provider.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { SessionProvider } from \"next-auth/react\";\r\nimport { PropsWithChildren } from \"react\";\r\n\r\nfunction Providers({ children }: PropsWithChildren) {\r\n  return <SessionProvider>{children}</SessionProvider>;\r\n}\r\n\r\nexport default Providers;\r\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAKA,SAAS,UAAU,EAAE,QAAQ,EAAqB;IAChD,qBAAO,yXAAC,gRAAe;kBAAE;;;;;;AAC3B;uCAEe", "debugId": null}}, {"offset": {"line": 33, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/node_modules/.pnpm/next%4015.5.2_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;;KAElC;IACL,IAAIF,QAAQC,GAAG,CAACK,yBAAyB,EAAE;;SAcpC;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;QAGT,OAAO;;IAOT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/node_modules/.pnpm/next%4015.5.2_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,gNACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 57, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/node_modules/.pnpm/next%4015.5.2_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactJsxRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,gNACRC,QAAQ,CAAC,YAAY,CAAEC,eAAe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/node_modules/.pnpm/next%4015.5.2_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,gNACRC,QAAQ,CAAC,YAAY,CAAEC,KAAK", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 67, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/node_modules/.pnpm/%40auth%2Bcore%400.40.0/node_modules/%40auth/core/errors.js"], "sourcesContent": ["/**\n * Base error class for all Auth.js errors.\n * It's optimized to be printed in the server logs in a nicely formatted way\n * via the [`logger.error`](https://authjs.dev/reference/core#logger) option.\n * @noInheritDoc\n */\nexport class AuthError extends Error {\n    /** @internal */\n    constructor(message, errorOptions) {\n        if (message instanceof Error) {\n            super(undefined, {\n                cause: { err: message, ...message.cause, ...errorOptions },\n            });\n        }\n        else if (typeof message === \"string\") {\n            if (errorOptions instanceof Error) {\n                errorOptions = { err: errorOptions, ...errorOptions.cause };\n            }\n            super(message, errorOptions);\n        }\n        else {\n            super(undefined, message);\n        }\n        this.name = this.constructor.name;\n        // @ts-expect-error https://github.com/microsoft/TypeScript/issues/3841\n        this.type = this.constructor.type ?? \"AuthError\";\n        // @ts-expect-error https://github.com/microsoft/TypeScript/issues/3841\n        this.kind = this.constructor.kind ?? \"error\";\n        Error.captureStackTrace?.(this, this.constructor);\n        const url = `https://errors.authjs.dev#${this.type.toLowerCase()}`;\n        this.message += `${this.message ? \". \" : \"\"}Read more at ${url}`;\n    }\n}\n/**\n * Thrown when the user's sign-in attempt failed.\n * @noInheritDoc\n */\nexport class SignInError extends AuthError {\n}\n/** @internal */\nSignInError.kind = \"signIn\";\n/**\n * One of the database [`Adapter` methods](https://authjs.dev/reference/core/adapters#methods)\n * failed during execution.\n *\n * :::tip\n * If `debug: true` is set, you can check out `[auth][debug]` in the logs to learn more about the failed adapter method execution.\n * @example\n * ```sh\n * [auth][debug]: adapter_getUserByEmail\n * { \"args\": [undefined] }\n * ```\n * :::\n * @noInheritDoc\n */\nexport class AdapterError extends AuthError {\n}\nAdapterError.type = \"AdapterError\";\n/**\n * Thrown when the execution of the [`signIn` callback](https://authjs.dev/reference/core/types#signin) fails\n * or if it returns `false`.\n * @noInheritDoc\n */\nexport class AccessDenied extends AuthError {\n}\nAccessDenied.type = \"AccessDenied\";\n/**\n * This error occurs when the user cannot finish login.\n * Depending on the provider type, this could have happened for multiple reasons.\n *\n * :::tip\n * Check out `[auth][details]` in the logs to know which provider failed.\n * @example\n * ```sh\n * [auth][details]: { \"provider\": \"github\" }\n * ```\n * :::\n *\n * For an [OAuth provider](https://authjs.dev/getting-started/authentication/oauth), possible causes are:\n * - The user denied access to the application\n * - There was an error parsing the OAuth Profile:\n *   Check out the provider's `profile` or `userinfo.request` method to make sure\n *   it correctly fetches the user's profile.\n * - The `signIn` or `jwt` callback methods threw an uncaught error:\n *   Check the callback method implementations.\n *\n * For an [Email provider](https://authjs.dev/getting-started/authentication/email), possible causes are:\n * - The provided email/token combination was invalid/missing:\n *   Check if the provider's `sendVerificationRequest` method correctly sends the email.\n * - The provided email/token combination has expired:\n *   Ask the user to log in again.\n * - There was an error with the database:\n *   Check the database logs.\n *\n * For a [Credentials provider](https://authjs.dev/getting-started/authentication/credentials), possible causes are:\n * - The `authorize` method threw an uncaught error:\n *   Check the provider's `authorize` method.\n * - The `signIn` or `jwt` callback methods threw an uncaught error:\n *   Check the callback method implementations.\n *\n * :::tip\n * Check out `[auth][cause]` in the error message for more details.\n * It will show the original stack trace.\n * :::\n * @noInheritDoc\n */\nexport class CallbackRouteError extends AuthError {\n}\nCallbackRouteError.type = \"CallbackRouteError\";\n/**\n * Thrown when Auth.js is misconfigured and accidentally tried to require authentication on a custom error page.\n * To prevent an infinite loop, Auth.js will instead render its default error page.\n *\n * To fix this, make sure that the `error` page does not require authentication.\n *\n * Learn more at [Guide: Error pages](https://authjs.dev/guides/pages/error)\n * @noInheritDoc\n */\nexport class ErrorPageLoop extends AuthError {\n}\nErrorPageLoop.type = \"ErrorPageLoop\";\n/**\n * One of the [`events` methods](https://authjs.dev/reference/core/types#eventcallbacks)\n * failed during execution.\n *\n * Make sure that the `events` methods are implemented correctly and uncaught errors are handled.\n *\n * Learn more at [`events`](https://authjs.dev/reference/core/types#eventcallbacks)\n * @noInheritDoc\n */\nexport class EventError extends AuthError {\n}\nEventError.type = \"EventError\";\n/**\n * Thrown when Auth.js is unable to verify a `callbackUrl` value.\n * The browser either disabled cookies or the `callbackUrl` is not a valid URL.\n *\n * Somebody might have tried to manipulate the callback URL that Auth.js uses to redirect the user back to the configured `callbackUrl`/page.\n * This could be a malicious hacker trying to redirect the user to a phishing site.\n * To prevent this, Auth.js checks if the callback URL is valid and throws this error if it is not.\n *\n * There is no action required, but it might be an indicator that somebody is trying to attack your application.\n * @noInheritDoc\n */\nexport class InvalidCallbackUrl extends AuthError {\n}\nInvalidCallbackUrl.type = \"InvalidCallbackUrl\";\n/**\n * Can be thrown from the `authorize` callback of the Credentials provider.\n * When an error occurs during the `authorize` callback, two things can happen:\n * 1. The user is redirected to the signin page, with `error=CredentialsSignin&code=credentials` in the URL. `code` is configurable.\n * 2. If you throw this error in a framework that handles form actions server-side, this error is thrown, instead of redirecting the user, so you'll need to handle.\n * @noInheritDoc\n */\nexport class CredentialsSignin extends SignInError {\n    constructor() {\n        super(...arguments);\n        /**\n         * The error code that is set in the `code` query parameter of the redirect URL.\n         *\n         *\n         * ⚠ NOTE: This property is going to be included in the URL, so make sure it does not hint at sensitive errors.\n         *\n         * The full error is always logged on the server, if you need to debug.\n         *\n         * Generally, we don't recommend hinting specifically if the user had either a wrong username or password specifically,\n         * try rather something like \"Invalid credentials\".\n         */\n        this.code = \"credentials\";\n    }\n}\nCredentialsSignin.type = \"CredentialsSignin\";\n/**\n * One of the configured OAuth or OIDC providers is missing the `authorization`, `token` or `userinfo`, or `issuer` configuration.\n * To perform OAuth or OIDC sign in, at least one of these endpoints is required.\n *\n * Learn more at [`OAuth2Config`](https://authjs.dev/reference/core/providers#oauth2configprofile) or [Guide: OAuth Provider](https://authjs.dev/guides/configuring-oauth-providers)\n * @noInheritDoc\n */\nexport class InvalidEndpoints extends AuthError {\n}\nInvalidEndpoints.type = \"InvalidEndpoints\";\n/**\n * Thrown when a PKCE, state or nonce OAuth check could not be performed.\n * This could happen if the OAuth provider is configured incorrectly or if the browser is blocking cookies.\n *\n * Learn more at [`checks`](https://authjs.dev/reference/core/providers#checks)\n * @noInheritDoc\n */\nexport class InvalidCheck extends AuthError {\n}\nInvalidCheck.type = \"InvalidCheck\";\n/**\n * Logged on the server when Auth.js could not decode or encode a JWT-based (`strategy: \"jwt\"`) session.\n *\n * Possible causes are either a misconfigured `secret` or a malformed JWT or `encode/decode` methods.\n *\n * :::note\n * When this error is logged, the session cookie is destroyed.\n * :::\n *\n * Learn more at [`secret`](https://authjs.dev/reference/core#secret), [`jwt.encode`](https://authjs.dev/reference/core/jwt#encode-1) or [`jwt.decode`](https://authjs.dev/reference/core/jwt#decode-2) for more information.\n * @noInheritDoc\n */\nexport class JWTSessionError extends AuthError {\n}\nJWTSessionError.type = \"JWTSessionError\";\n/**\n * Thrown if Auth.js is misconfigured. This could happen if you configured an Email provider but did not set up a database adapter,\n * or tried using a `strategy: \"database\"` session without a database adapter.\n * In both cases, make sure you either remove the configuration or add the missing adapter.\n *\n * Learn more at [Database Adapters](https://authjs.dev/getting-started/database), [Email provider](https://authjs.dev/getting-started/authentication/email) or [Concept: Database session strategy](https://authjs.dev/concepts/session-strategies#database-session)\n * @noInheritDoc\n */\nexport class MissingAdapter extends AuthError {\n}\nMissingAdapter.type = \"MissingAdapter\";\n/**\n * Thrown similarily to [`MissingAdapter`](https://authjs.dev/reference/core/errors#missingadapter), but only some required methods were missing.\n *\n * Make sure you either remove the configuration or add the missing methods to the adapter.\n *\n * Learn more at [Database Adapters](https://authjs.dev/getting-started/database)\n * @noInheritDoc\n */\nexport class MissingAdapterMethods extends AuthError {\n}\nMissingAdapterMethods.type = \"MissingAdapterMethods\";\n/**\n * Thrown when a Credentials provider is missing the `authorize` configuration.\n * To perform credentials sign in, the `authorize` method is required.\n *\n * Learn more at [Credentials provider](https://authjs.dev/getting-started/authentication/credentials)\n * @noInheritDoc\n */\nexport class MissingAuthorize extends AuthError {\n}\nMissingAuthorize.type = \"MissingAuthorize\";\n/**\n * Auth.js requires a secret or multiple secrets to be set, but none was not found. This is used to encrypt cookies, JWTs and other sensitive data.\n *\n * :::note\n * If you are using a framework like Next.js, we try to automatically infer the secret from the `AUTH_SECRET`, `AUTH_SECRET_1`, etc. environment variables.\n * Alternatively, you can also explicitly set the [`AuthConfig.secret`](https://authjs.dev/reference/core#secret) option.\n * :::\n *\n *\n * :::tip\n * To generate a random string, you can use the Auth.js CLI: `npx auth secret`\n * :::\n * @noInheritDoc\n */\nexport class MissingSecret extends AuthError {\n}\nMissingSecret.type = \"MissingSecret\";\n/**\n * Thrown when an Email address is already associated with an account\n * but the user is trying an OAuth account that is not linked to it.\n *\n * For security reasons, Auth.js does not automatically link OAuth accounts to existing accounts if the user is not signed in.\n *\n * :::tip\n * If you trust the OAuth provider to have verified the user's email address,\n * you can enable automatic account linking by setting [`allowDangerousEmailAccountLinking: true`](https://authjs.dev/reference/core/providers#allowdangerousemailaccountlinking)\n * in the provider configuration.\n * :::\n * @noInheritDoc\n */\nexport class OAuthAccountNotLinked extends SignInError {\n}\nOAuthAccountNotLinked.type = \"OAuthAccountNotLinked\";\n/**\n * Thrown when an OAuth provider returns an error during the sign in process.\n * This could happen for example if the user denied access to the application or there was a configuration error.\n *\n * For a full list of possible reasons, check out the specification [Authorization Code Grant: Error Response](https://www.rfc-editor.org/rfc/rfc6749#section-*******)\n * @noInheritDoc\n */\nexport class OAuthCallbackError extends SignInError {\n}\nOAuthCallbackError.type = \"OAuthCallbackError\";\n/**\n * This error occurs during an OAuth sign in attempt when the provider's\n * response could not be parsed. This could for example happen if the provider's API\n * changed, or the [`OAuth2Config.profile`](https://authjs.dev/reference/core/providers#oauth2configprofile) method is not implemented correctly.\n * @noInheritDoc\n */\nexport class OAuthProfileParseError extends AuthError {\n}\nOAuthProfileParseError.type = \"OAuthProfileParseError\";\n/**\n * Logged on the server when Auth.js could not retrieve a session from the database (`strategy: \"database\"`).\n *\n * The database adapter might be misconfigured or the database is not reachable.\n *\n * Learn more at [Concept: Database session strategy](https://authjs.dev/concepts/session-strategies#database)\n * @noInheritDoc\n */\nexport class SessionTokenError extends AuthError {\n}\nSessionTokenError.type = \"SessionTokenError\";\n/**\n * Happens when login by [OAuth](https://authjs.dev/getting-started/authentication/oauth) could not be started.\n *\n * Possible causes are:\n * - The Authorization Server is not compliant with the [OAuth 2.0](https://www.ietf.org/rfc/rfc6749.html) or the [OIDC](https://openid.net/specs/openid-connect-core-1_0.html) specification.\n *   Check the details in the error message.\n *\n * :::tip\n * Check out `[auth][details]` in the logs to know which provider failed.\n * @example\n * ```sh\n * [auth][details]: { \"provider\": \"github\" }\n * ```\n * :::\n * @noInheritDoc\n */\nexport class OAuthSignInError extends SignInError {\n}\nOAuthSignInError.type = \"OAuthSignInError\";\n/**\n * Happens when the login by an [Email provider](https://authjs.dev/getting-started/authentication/email) could not be started.\n *\n * Possible causes are:\n * - The email sent from the client is invalid, could not be normalized by [`EmailConfig.normalizeIdentifier`](https://authjs.dev/reference/core/providers/email#normalizeidentifier)\n * - The provided email/token combination has expired:\n *   Ask the user to log in again.\n * - There was an error with the database:\n *   Check the database logs.\n * @noInheritDoc\n */\nexport class EmailSignInError extends SignInError {\n}\nEmailSignInError.type = \"EmailSignInError\";\n/**\n * Represents an error that occurs during the sign-out process. This error\n * is logged when there are issues in terminating a user's session, either\n * by failing to delete the session from the database (in database session\n * strategies) or encountering issues during other parts of the sign-out\n * process, such as emitting sign-out events or clearing session cookies.\n *\n * The session cookie(s) are emptied even if this error is logged.\n * @noInheritDoc\n */\nexport class SignOutError extends AuthError {\n}\nSignOutError.type = \"SignOutError\";\n/**\n * Auth.js was requested to handle an operation that it does not support.\n *\n * See [`AuthAction`](https://authjs.dev/reference/core/types#authaction) for the supported actions.\n * @noInheritDoc\n */\nexport class UnknownAction extends AuthError {\n}\nUnknownAction.type = \"UnknownAction\";\n/**\n * Thrown when a Credentials provider is present but the JWT strategy (`strategy: \"jwt\"`) is not enabled.\n *\n * Learn more at [`strategy`](https://authjs.dev/reference/core#strategy) or [Credentials provider](https://authjs.dev/getting-started/authentication/credentials)\n * @noInheritDoc\n */\nexport class UnsupportedStrategy extends AuthError {\n}\nUnsupportedStrategy.type = \"UnsupportedStrategy\";\n/**\n * Thrown when an endpoint was incorrectly called without a provider, or with an unsupported provider.\n * @noInheritDoc\n */\nexport class InvalidProvider extends AuthError {\n}\nInvalidProvider.type = \"InvalidProvider\";\n/**\n * Thrown when the `trustHost` option was not set to `true`.\n *\n * Auth.js requires the `trustHost` option to be set to `true` since it's relying on the request headers' `host` value.\n *\n * :::note\n * Official Auth.js libraries might attempt to automatically set the `trustHost` option to `true` if the request is coming from a trusted host on a trusted platform.\n * :::\n *\n * Learn more at [`trustHost`](https://authjs.dev/reference/core#trusthost) or [Guide: Deployment](https://authjs.dev/getting-started/deployment)\n * @noInheritDoc\n */\nexport class UntrustedHost extends AuthError {\n}\nUntrustedHost.type = \"UntrustedHost\";\n/**\n * The user's email/token combination was invalid.\n * This could be because the email/token combination was not found in the database,\n * or because the token has expired. Ask the user to log in again.\n * @noInheritDoc\n */\nexport class Verification extends AuthError {\n}\nVerification.type = \"Verification\";\n/**\n * Error for missing CSRF tokens in client-side actions (`signIn`, `signOut`, `useSession#update`).\n * Thrown when actions lack the double submit cookie, essential for CSRF protection.\n *\n * CSRF ([Cross-Site Request Forgery](https://owasp.org/www-community/attacks/csrf))\n * is an attack leveraging authenticated user credentials for unauthorized actions.\n *\n * Double submit cookie pattern, a CSRF defense, requires matching values in a cookie\n * and request parameter. More on this at [MDN Web Docs](https://developer.mozilla.org/en-US/docs/Glossary/CSRF).\n * @noInheritDoc\n */\nexport class MissingCSRF extends SignInError {\n}\nMissingCSRF.type = \"MissingCSRF\";\nconst clientErrors = new Set([\n    \"CredentialsSignin\",\n    \"OAuthAccountNotLinked\",\n    \"OAuthCallbackError\",\n    \"AccessDenied\",\n    \"Verification\",\n    \"MissingCSRF\",\n    \"AccountNotLinked\",\n    \"WebAuthnVerificationError\",\n]);\n/**\n * Used to only allow sending a certain subset of errors to the client.\n * Errors are always logged on the server, but to prevent leaking sensitive information,\n * only a subset of errors are sent to the client as-is.\n * @internal\n */\nexport function isClientError(error) {\n    if (error instanceof AuthError)\n        return clientErrors.has(error.type);\n    return false;\n}\n/**\n * Thrown when multiple providers have `enableConditionalUI` set to `true`.\n * Only one provider can have this option enabled at a time.\n * @noInheritDoc\n */\nexport class DuplicateConditionalUI extends AuthError {\n}\nDuplicateConditionalUI.type = \"DuplicateConditionalUI\";\n/**\n * Thrown when a WebAuthn provider has `enableConditionalUI` set to `true` but no formField has `webauthn` in its autocomplete param.\n *\n * The `webauthn` autocomplete param is required for conditional UI to work.\n * @noInheritDoc\n */\nexport class MissingWebAuthnAutocomplete extends AuthError {\n}\nMissingWebAuthnAutocomplete.type = \"MissingWebAuthnAutocomplete\";\n/**\n * Thrown when a WebAuthn provider fails to verify a client response.\n * @noInheritDoc\n */\nexport class WebAuthnVerificationError extends AuthError {\n}\nWebAuthnVerificationError.type = \"WebAuthnVerificationError\";\n/**\n * Thrown when an Email address is already associated with an account\n * but the user is trying an account that is not linked to it.\n *\n * For security reasons, Auth.js does not automatically link accounts to existing accounts if the user is not signed in.\n * @noInheritDoc\n */\nexport class AccountNotLinked extends SignInError {\n}\nAccountNotLinked.type = \"AccountNotLinked\";\n/**\n * Thrown when an experimental feature is used but not enabled.\n * @noInheritDoc\n */\nexport class ExperimentalFeatureNotEnabled extends AuthError {\n}\nExperimentalFeatureNotEnabled.type = \"ExperimentalFeatureNotEnabled\";\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACM,MAAM,kBAAkB;IAC3B,cAAc,GACd,YAAY,OAAO,EAAE,YAAY,CAAE;QAC/B,IAAI,mBAAmB,OAAO;YAC1B,KAAK,CAAC,WAAW;gBACb,OAAO;oBAAE,KAAK;oBAAS,GAAG,QAAQ,KAAK;oBAAE,GAAG,YAAY;gBAAC;YAC7D;QACJ,OACK,IAAI,OAAO,YAAY,UAAU;YAClC,IAAI,wBAAwB,OAAO;gBAC/B,eAAe;oBAAE,KAAK;oBAAc,GAAG,aAAa,KAAK;gBAAC;YAC9D;YACA,KAAK,CAAC,SAAS;QACnB,OACK;YACD,KAAK,CAAC,WAAW;QACrB;QACA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI;QACjC,uEAAuE;QACvE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,IAAI;QACrC,uEAAuE;QACvE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,IAAI;QACrC,MAAM,iBAAiB,GAAG,IAAI,EAAE,IAAI,CAAC,WAAW;QAChD,MAAM,MAAM,CAAC,0BAA0B,EAAE,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI;QAClE,IAAI,CAAC,OAAO,IAAI,GAAG,IAAI,CAAC,OAAO,GAAG,OAAO,GAAG,aAAa,EAAE,KAAK;IACpE;AACJ;AAKO,MAAM,oBAAoB;AACjC;AACA,cAAc,GACd,YAAY,IAAI,GAAG;AAeZ,MAAM,qBAAqB;AAClC;AACA,aAAa,IAAI,GAAG;AAMb,MAAM,qBAAqB;AAClC;AACA,aAAa,IAAI,GAAG;AAyCb,MAAM,2BAA2B;AACxC;AACA,mBAAmB,IAAI,GAAG;AAUnB,MAAM,sBAAsB;AACnC;AACA,cAAc,IAAI,GAAG;AAUd,MAAM,mBAAmB;AAChC;AACA,WAAW,IAAI,GAAG;AAYX,MAAM,2BAA2B;AACxC;AACA,mBAAmB,IAAI,GAAG;AAQnB,MAAM,0BAA0B;IACnC,aAAc;QACV,KAAK,IAAI;QACT;;;;;;;;;;SAUC,GACD,IAAI,CAAC,IAAI,GAAG;IAChB;AACJ;AACA,kBAAkB,IAAI,GAAG;AAQlB,MAAM,yBAAyB;AACtC;AACA,iBAAiB,IAAI,GAAG;AAQjB,MAAM,qBAAqB;AAClC;AACA,aAAa,IAAI,GAAG;AAab,MAAM,wBAAwB;AACrC;AACA,gBAAgB,IAAI,GAAG;AAShB,MAAM,uBAAuB;AACpC;AACA,eAAe,IAAI,GAAG;AASf,MAAM,8BAA8B;AAC3C;AACA,sBAAsB,IAAI,GAAG;AAQtB,MAAM,yBAAyB;AACtC;AACA,iBAAiB,IAAI,GAAG;AAejB,MAAM,sBAAsB;AACnC;AACA,cAAc,IAAI,GAAG;AAcd,MAAM,8BAA8B;AAC3C;AACA,sBAAsB,IAAI,GAAG;AAQtB,MAAM,2BAA2B;AACxC;AACA,mBAAmB,IAAI,GAAG;AAOnB,MAAM,+BAA+B;AAC5C;AACA,uBAAuB,IAAI,GAAG;AASvB,MAAM,0BAA0B;AACvC;AACA,kBAAkB,IAAI,GAAG;AAiBlB,MAAM,yBAAyB;AACtC;AACA,iBAAiB,IAAI,GAAG;AAYjB,MAAM,yBAAyB;AACtC;AACA,iBAAiB,IAAI,GAAG;AAWjB,MAAM,qBAAqB;AAClC;AACA,aAAa,IAAI,GAAG;AAOb,MAAM,sBAAsB;AACnC;AACA,cAAc,IAAI,GAAG;AAOd,MAAM,4BAA4B;AACzC;AACA,oBAAoB,IAAI,GAAG;AAKpB,MAAM,wBAAwB;AACrC;AACA,gBAAgB,IAAI,GAAG;AAahB,MAAM,sBAAsB;AACnC;AACA,cAAc,IAAI,GAAG;AAOd,MAAM,qBAAqB;AAClC;AACA,aAAa,IAAI,GAAG;AAYb,MAAM,oBAAoB;AACjC;AACA,YAAY,IAAI,GAAG;AACnB,MAAM,eAAe,IAAI,IAAI;IACzB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACH;AAOM,SAAS,cAAc,KAAK;IAC/B,IAAI,iBAAiB,WACjB,OAAO,aAAa,GAAG,CAAC,MAAM,IAAI;IACtC,OAAO;AACX;AAMO,MAAM,+BAA+B;AAC5C;AACA,uBAAuB,IAAI,GAAG;AAOvB,MAAM,oCAAoC;AACjD;AACA,4BAA4B,IAAI,GAAG;AAK5B,MAAM,kCAAkC;AAC/C;AACA,0BAA0B,IAAI,GAAG;AAQ1B,MAAM,yBAAyB;AACtC;AACA,iBAAiB,IAAI,GAAG;AAKjB,MAAM,sCAAsC;AACnD;AACA,8BAA8B,IAAI,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 306, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/node_modules/.pnpm/next-auth%405.0.0-beta.29_nex_626fd582465e254ed3dcaa950f0f297e/node_modules/next-auth/lib/client.js"], "sourcesContent": ["\"use client\";\nimport * as React from \"react\";\nimport { AuthError } from \"@auth/core/errors\";\n/** @todo */\nclass ClientFetchError extends AuthError {\n}\n/** @todo */\nexport class ClientSessionError extends AuthError {\n}\n// ------------------------ Internal ------------------------\n/**\n * If passed 'appContext' via getInitialProps() in _app.js\n * then get the req object from ctx and use that for the\n * req value to allow `fetchData` to\n * work seemlessly in getInitialProps() on server side\n * pages *and* in _app.js.\n * @internal\n */\nexport async function fetchData(path, __NEXTAUTH, logger, req = {}) {\n    const url = `${apiBaseUrl(__NEXTAUTH)}/${path}`;\n    try {\n        const options = {\n            headers: {\n                \"Content-Type\": \"application/json\",\n                ...(req?.headers?.cookie ? { cookie: req.headers.cookie } : {}),\n            },\n        };\n        if (req?.body) {\n            options.body = JSON.stringify(req.body);\n            options.method = \"POST\";\n        }\n        const res = await fetch(url, options);\n        const data = await res.json();\n        if (!res.ok)\n            throw data;\n        return data;\n    }\n    catch (error) {\n        logger.error(new ClientFetchError(error.message, error));\n        return null;\n    }\n}\n/** @internal */\nexport function apiBaseUrl(__NEXTAUTH) {\n    if (typeof window === \"undefined\") {\n        // Return absolute path when called server side\n        return `${__NEXTAUTH.baseUrlServer}${__NEXTAUTH.basePathServer}`;\n    }\n    // Return relative path when called client side\n    return __NEXTAUTH.basePath;\n}\n/** @internal  */\nexport function useOnline() {\n    const [isOnline, setIsOnline] = React.useState(typeof navigator !== \"undefined\" ? navigator.onLine : false);\n    const setOnline = () => setIsOnline(true);\n    const setOffline = () => setIsOnline(false);\n    React.useEffect(() => {\n        window.addEventListener(\"online\", setOnline);\n        window.addEventListener(\"offline\", setOffline);\n        return () => {\n            window.removeEventListener(\"online\", setOnline);\n            window.removeEventListener(\"offline\", setOffline);\n        };\n    }, []);\n    return isOnline;\n}\n/**\n * Returns the number of seconds elapsed since January 1, 1970 00:00:00 UTC.\n * @internal\n */\nexport function now() {\n    return Math.floor(Date.now() / 1000);\n}\n/**\n * Returns an `URL` like object to make requests/redirects from server-side\n * @internal\n */\nexport function parseUrl(url) {\n    const defaultUrl = new URL(\"http://localhost:3000/api/auth\");\n    if (url && !url.startsWith(\"http\")) {\n        url = `https://${url}`;\n    }\n    const _url = new URL(url || defaultUrl);\n    const path = (_url.pathname === \"/\" ? defaultUrl.pathname : _url.pathname)\n        // Remove trailing slash\n        .replace(/\\/$/, \"\");\n    const base = `${_url.origin}${path}`;\n    return {\n        origin: _url.origin,\n        host: _url.host,\n        path,\n        base,\n        toString: () => base,\n    };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AACA;AACA;AAFA;;;AAGA,UAAU,GACV,MAAM,yBAAyB,6NAAS;AACxC;AAEO,MAAM,2BAA2B,6NAAS;AACjD;AAUO,eAAe,UAAU,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;IAC9D,MAAM,MAAM,GAAG,WAAW,YAAY,CAAC,EAAE,MAAM;IAC/C,IAAI;QACA,MAAM,UAAU;YACZ,SAAS;gBACL,gBAAgB;gBAChB,GAAI,KAAK,SAAS,SAAS;oBAAE,QAAQ,IAAI,OAAO,CAAC,MAAM;gBAAC,IAAI,CAAC,CAAC;YAClE;QACJ;QACA,IAAI,KAAK,MAAM;YACX,QAAQ,IAAI,GAAG,KAAK,SAAS,CAAC,IAAI,IAAI;YACtC,QAAQ,MAAM,GAAG;QACrB;QACA,MAAM,MAAM,MAAM,MAAM,KAAK;QAC7B,MAAM,OAAO,MAAM,IAAI,IAAI;QAC3B,IAAI,CAAC,IAAI,EAAE,EACP,MAAM;QACV,OAAO;IACX,EACA,OAAO,OAAO;QACV,OAAO,KAAK,CAAC,IAAI,iBAAiB,MAAM,OAAO,EAAE;QACjD,OAAO;IACX;AACJ;AAEO,SAAS,WAAW,UAAU;IACjC,wCAAmC;QAC/B,+CAA+C;QAC/C,OAAO,GAAG,WAAW,aAAa,GAAG,WAAW,cAAc,EAAE;IACpE;;;AAGJ;AAEO,SAAS;IACZ,MAAM,CAAC,UAAU,YAAY,GAAG,4VAAc,CAAC,OAAO,cAAc,cAAc,UAAU,MAAM,GAAG;IACrG,MAAM,YAAY,IAAM,YAAY;IACpC,MAAM,aAAa,IAAM,YAAY;IACrC,6VAAe,CAAC;QACZ,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,gBAAgB,CAAC,WAAW;QACnC,OAAO;YACH,OAAO,mBAAmB,CAAC,UAAU;YACrC,OAAO,mBAAmB,CAAC,WAAW;QAC1C;IACJ,GAAG,EAAE;IACL,OAAO;AACX;AAKO,SAAS;IACZ,OAAO,KAAK,KAAK,CAAC,KAAK,GAAG,KAAK;AACnC;AAKO,SAAS,SAAS,GAAG;IACxB,MAAM,aAAa,IAAI,IAAI;IAC3B,IAAI,OAAO,CAAC,IAAI,UAAU,CAAC,SAAS;QAChC,MAAM,CAAC,QAAQ,EAAE,KAAK;IAC1B;IACA,MAAM,OAAO,IAAI,IAAI,OAAO;IAC5B,MAAM,OAAO,CAAC,KAAK,QAAQ,KAAK,MAAM,WAAW,QAAQ,GAAG,KAAK,QAAQ,CACrE,wBAAwB;KACvB,OAAO,CAAC,OAAO;IACpB,MAAM,OAAO,GAAG,KAAK,MAAM,GAAG,MAAM;IACpC,OAAO;QACH,QAAQ,KAAK,MAAM;QACnB,MAAM,KAAK,IAAI;QACf;QACA;QACA,UAAU,IAAM;IACpB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 399, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/node_modules/.pnpm/next-auth%405.0.0-beta.29_nex_626fd582465e254ed3dcaa950f0f297e/node_modules/next-auth/react.js"], "sourcesContent": ["/**\n *\n * NextAuth.js is the official integration of Auth.js for Next.js applications. It supports both\n * [Client Components](https://nextjs.org/docs/app/building-your-application/rendering/client-components) and the\n * [Pages Router](https://nextjs.org/docs/pages). It includes methods for signing in, signing out, hooks, and a React\n * Context provider to wrap your application and make session data available anywhere.\n *\n * For use in [Server Actions](https://nextjs.org/docs/app/api-reference/functions/server-actions), check out [these methods](https://authjs.dev/guides/upgrade-to-v5#methods)\n *\n * @module react\n */\n\"use client\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport * as React from \"react\";\nimport { apiBaseUrl, ClientSessionError, fetchData, now, parseUrl, useOnline, } from \"./lib/client.js\";\n// This behaviour mirrors the default behaviour for getting the site name that\n// happens server side in server/index.js\n// 1. An empty value is legitimate when the code is being invoked client side as\n//    relative URLs are valid in that context and so defaults to empty.\n// 2. When invoked server side the value is picked up from an environment\n//    variable and defaults to 'http://localhost:3000'.\nexport const __NEXTAUTH = {\n    baseUrl: parseUrl(process.env.NEXTAUTH_URL ?? process.env.VERCEL_URL).origin,\n    basePath: parseUrl(process.env.NEXTAUTH_URL).path,\n    baseUrlServer: parseUrl(process.env.NEXTAUTH_URL_INTERNAL ??\n        process.env.NEXTAUTH_URL ??\n        process.env.VERCEL_URL).origin,\n    basePathServer: parseUrl(process.env.NEXTAUTH_URL_INTERNAL ?? process.env.NEXTAUTH_URL).path,\n    _lastSync: 0,\n    _session: undefined,\n    _getSession: () => { },\n};\n// https://github.com/nextauthjs/next-auth/pull/10762\nlet broadcastChannel = null;\nfunction getNewBroadcastChannel() {\n    if (typeof BroadcastChannel === \"undefined\") {\n        return {\n            postMessage: () => { },\n            addEventListener: () => { },\n            removeEventListener: () => { },\n            name: \"next-auth\",\n            onmessage: null,\n            onmessageerror: null,\n            close: () => { },\n            dispatchEvent: () => false,\n        };\n    }\n    return new BroadcastChannel(\"next-auth\");\n}\nfunction broadcast() {\n    if (broadcastChannel === null) {\n        broadcastChannel = getNewBroadcastChannel();\n    }\n    return broadcastChannel;\n}\n// TODO:\nconst logger = {\n    debug: console.debug,\n    error: console.error,\n    warn: console.warn,\n};\nexport const SessionContext = React.createContext?.(undefined);\n/**\n * React Hook that gives you access to the logged in user's session data and lets you modify it.\n *\n * :::info\n * `useSession` is for client-side use only and when using [Next.js App Router (`app/`)](https://nextjs.org/blog/next-13-4#nextjs-app-router) you should prefer the `auth()` export.\n * :::\n */\nexport function useSession(options) {\n    if (!SessionContext) {\n        throw new Error(\"React Context is unavailable in Server Components\");\n    }\n    // @ts-expect-error Satisfy TS if branch on line below\n    const value = React.useContext(SessionContext);\n    if (!value && process.env.NODE_ENV !== \"production\") {\n        throw new Error(\"[next-auth]: `useSession` must be wrapped in a <SessionProvider />\");\n    }\n    const { required, onUnauthenticated } = options ?? {};\n    const requiredAndNotLoading = required && value.status === \"unauthenticated\";\n    React.useEffect(() => {\n        if (requiredAndNotLoading) {\n            const url = `${__NEXTAUTH.basePath}/signin?${new URLSearchParams({\n                error: \"SessionRequired\",\n                callbackUrl: window.location.href,\n            })}`;\n            if (onUnauthenticated)\n                onUnauthenticated();\n            else\n                window.location.href = url;\n        }\n    }, [requiredAndNotLoading, onUnauthenticated]);\n    if (requiredAndNotLoading) {\n        return {\n            data: value.data,\n            update: value.update,\n            status: \"loading\",\n        };\n    }\n    return value;\n}\nexport async function getSession(params) {\n    const session = await fetchData(\"session\", __NEXTAUTH, logger, params);\n    if (params?.broadcast ?? true) {\n        // https://github.com/nextauthjs/next-auth/pull/11470\n        getNewBroadcastChannel().postMessage({\n            event: \"session\",\n            data: { trigger: \"getSession\" },\n        });\n    }\n    return session;\n}\n/**\n * Returns the current Cross-Site Request Forgery Token (CSRF Token)\n * required to make requests that changes state. (e.g. signing in or out, or updating the session).\n *\n * [CSRF Prevention: Double Submit Cookie](https://cheatsheetseries.owasp.org/cheatsheets/Cross-Site_Request_Forgery_Prevention_Cheat_Sheet.html#double-submit-cookie)\n */\nexport async function getCsrfToken() {\n    const response = await fetchData(\"csrf\", __NEXTAUTH, logger);\n    return response?.csrfToken ?? \"\";\n}\nexport async function getProviders() {\n    return fetchData(\"providers\", __NEXTAUTH, logger);\n}\nexport async function signIn(provider, options, authorizationParams) {\n    const { callbackUrl, ...rest } = options ?? {};\n    const { redirect = true, redirectTo = callbackUrl ?? window.location.href, ...signInParams } = rest;\n    const baseUrl = apiBaseUrl(__NEXTAUTH);\n    const providers = await getProviders();\n    if (!providers) {\n        const url = `${baseUrl}/error`;\n        window.location.href = url;\n        return; // TODO: Return error if `redirect: false`\n    }\n    if (!provider || !providers[provider]) {\n        const url = `${baseUrl}/signin?${new URLSearchParams({\n            callbackUrl: redirectTo,\n        })}`;\n        window.location.href = url;\n        return; // TODO: Return error if `redirect: false`\n    }\n    const providerType = providers[provider].type;\n    if (providerType === \"webauthn\") {\n        // TODO: Add docs link with explanation\n        throw new TypeError([\n            `Provider id \"${provider}\" refers to a WebAuthn provider.`,\n            'Please use `import { signIn } from \"next-auth/webauthn\"` instead.',\n        ].join(\"\\n\"));\n    }\n    const signInUrl = `${baseUrl}/${providerType === \"credentials\" ? \"callback\" : \"signin\"}/${provider}`;\n    const csrfToken = await getCsrfToken();\n    const res = await fetch(`${signInUrl}?${new URLSearchParams(authorizationParams)}`, {\n        method: \"post\",\n        headers: {\n            \"Content-Type\": \"application/x-www-form-urlencoded\",\n            \"X-Auth-Return-Redirect\": \"1\",\n        },\n        body: new URLSearchParams({\n            ...signInParams,\n            csrfToken,\n            callbackUrl: redirectTo,\n        }),\n    });\n    const data = await res.json();\n    if (redirect) {\n        const url = data.url ?? redirectTo;\n        window.location.href = url;\n        // If url contains a hash, the browser does not reload the page. We reload manually\n        if (url.includes(\"#\"))\n            window.location.reload();\n        return;\n    }\n    const error = new URL(data.url).searchParams.get(\"error\") ?? undefined;\n    const code = new URL(data.url).searchParams.get(\"code\") ?? undefined;\n    if (res.ok) {\n        await __NEXTAUTH._getSession({ event: \"storage\" });\n    }\n    return {\n        error,\n        code,\n        status: res.status,\n        ok: res.ok,\n        url: error ? null : data.url,\n    };\n}\nexport async function signOut(options) {\n    const { redirect = true, redirectTo = options?.callbackUrl ?? window.location.href, } = options ?? {};\n    const baseUrl = apiBaseUrl(__NEXTAUTH);\n    const csrfToken = await getCsrfToken();\n    const res = await fetch(`${baseUrl}/signout`, {\n        method: \"post\",\n        headers: {\n            \"Content-Type\": \"application/x-www-form-urlencoded\",\n            \"X-Auth-Return-Redirect\": \"1\",\n        },\n        body: new URLSearchParams({ csrfToken, callbackUrl: redirectTo }),\n    });\n    const data = await res.json();\n    broadcast().postMessage({ event: \"session\", data: { trigger: \"signout\" } });\n    if (redirect) {\n        const url = data.url ?? redirectTo;\n        window.location.href = url;\n        // If url contains a hash, the browser does not reload the page. We reload manually\n        if (url.includes(\"#\"))\n            window.location.reload();\n        return;\n    }\n    await __NEXTAUTH._getSession({ event: \"storage\" });\n    return data;\n}\n/**\n * [React Context](https://react.dev/learn/passing-data-deeply-with-context) provider to wrap the app (`pages/`) to make session data available anywhere.\n *\n * When used, the session state is automatically synchronized across all open tabs/windows and they are all updated whenever they gain or lose focus\n * or the state changes (e.g. a user signs in or out) when {@link SessionProviderProps.refetchOnWindowFocus} is `true`.\n *\n * :::info\n * `SessionProvider` is for client-side use only and when using [Next.js App Router (`app/`)](https://nextjs.org/blog/next-13-4#nextjs-app-router) you should prefer the `auth()` export.\n * :::\n */\nexport function SessionProvider(props) {\n    if (!SessionContext) {\n        throw new Error(\"React Context is unavailable in Server Components\");\n    }\n    const { children, basePath, refetchInterval, refetchWhenOffline } = props;\n    if (basePath)\n        __NEXTAUTH.basePath = basePath;\n    /**\n     * If session was `null`, there was an attempt to fetch it,\n     * but it failed, but we still treat it as a valid initial value.\n     */\n    const hasInitialSession = props.session !== undefined;\n    /** If session was passed, initialize as already synced */\n    __NEXTAUTH._lastSync = hasInitialSession ? now() : 0;\n    const [session, setSession] = React.useState(() => {\n        if (hasInitialSession)\n            __NEXTAUTH._session = props.session;\n        return props.session;\n    });\n    /** If session was passed, initialize as not loading */\n    const [loading, setLoading] = React.useState(!hasInitialSession);\n    React.useEffect(() => {\n        __NEXTAUTH._getSession = async ({ event } = {}) => {\n            try {\n                const storageEvent = event === \"storage\";\n                // We should always update if we don't have a client session yet\n                // or if there are events from other tabs/windows\n                if (storageEvent || __NEXTAUTH._session === undefined) {\n                    __NEXTAUTH._lastSync = now();\n                    __NEXTAUTH._session = await getSession({\n                        broadcast: !storageEvent,\n                    });\n                    setSession(__NEXTAUTH._session);\n                    return;\n                }\n                if (\n                // If there is no time defined for when a session should be considered\n                // stale, then it's okay to use the value we have until an event is\n                // triggered which updates it\n                !event ||\n                    // If the client doesn't have a session then we don't need to call\n                    // the server to check if it does (if they have signed in via another\n                    // tab or window that will come through as a \"stroage\" event\n                    // event anyway)\n                    __NEXTAUTH._session === null ||\n                    // Bail out early if the client session is not stale yet\n                    now() < __NEXTAUTH._lastSync) {\n                    return;\n                }\n                // An event or session staleness occurred, update the client session.\n                __NEXTAUTH._lastSync = now();\n                __NEXTAUTH._session = await getSession();\n                setSession(__NEXTAUTH._session);\n            }\n            catch (error) {\n                logger.error(new ClientSessionError(error.message, error));\n            }\n            finally {\n                setLoading(false);\n            }\n        };\n        __NEXTAUTH._getSession();\n        return () => {\n            __NEXTAUTH._lastSync = 0;\n            __NEXTAUTH._session = undefined;\n            __NEXTAUTH._getSession = () => { };\n        };\n    }, []);\n    React.useEffect(() => {\n        const handle = () => __NEXTAUTH._getSession({ event: \"storage\" });\n        // Listen for storage events and update session if event fired from\n        // another window (but suppress firing another event to avoid a loop)\n        // Fetch new session data but tell it to not to fire another event to\n        // avoid an infinite loop.\n        // Note: We could pass session data through and do something like\n        // `setData(message.data)` but that can cause problems depending\n        // on how the session object is being used in the client; it is\n        // more robust to have each window/tab fetch it's own copy of the\n        // session object rather than share it across instances.\n        broadcast().addEventListener(\"message\", handle);\n        return () => broadcast().removeEventListener(\"message\", handle);\n    }, []);\n    React.useEffect(() => {\n        const { refetchOnWindowFocus = true } = props;\n        // Listen for when the page is visible, if the user switches tabs\n        // and makes our tab visible again, re-fetch the session, but only if\n        // this feature is not disabled.\n        const visibilityHandler = () => {\n            if (refetchOnWindowFocus && document.visibilityState === \"visible\")\n                __NEXTAUTH._getSession({ event: \"visibilitychange\" });\n        };\n        document.addEventListener(\"visibilitychange\", visibilityHandler, false);\n        return () => document.removeEventListener(\"visibilitychange\", visibilityHandler, false);\n    }, [props.refetchOnWindowFocus]);\n    const isOnline = useOnline();\n    // TODO: Flip this behavior in next major version\n    const shouldRefetch = refetchWhenOffline !== false || isOnline;\n    React.useEffect(() => {\n        if (refetchInterval && shouldRefetch) {\n            const refetchIntervalTimer = setInterval(() => {\n                if (__NEXTAUTH._session) {\n                    __NEXTAUTH._getSession({ event: \"poll\" });\n                }\n            }, refetchInterval * 1000);\n            return () => clearInterval(refetchIntervalTimer);\n        }\n    }, [refetchInterval, shouldRefetch]);\n    const value = React.useMemo(() => ({\n        data: session,\n        status: loading\n            ? \"loading\"\n            : session\n                ? \"authenticated\"\n                : \"unauthenticated\",\n        async update(data) {\n            if (loading)\n                return;\n            setLoading(true);\n            const newSession = await fetchData(\"session\", __NEXTAUTH, logger, typeof data === \"undefined\"\n                ? undefined\n                : { body: { csrfToken: await getCsrfToken(), data } });\n            setLoading(false);\n            if (newSession) {\n                setSession(newSession);\n                broadcast().postMessage({\n                    event: \"session\",\n                    data: { trigger: \"getSession\" },\n                });\n            }\n            return newSession;\n        },\n    }), [session, loading]);\n    return (\n    // @ts-expect-error\n    _jsx(SessionContext.Provider, { value: value, children: children }));\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;CAUC;;;;;;;;;;;;;;;;;;;;AAED;AACA;AACA;AAHA;;;;AAUO,MAAM,aAAa;IACtB,SAAS,IAAA,iRAAQ,EAAC,QAAQ,GAAG,CAAC,YAAY,IAAI,QAAQ,GAAG,CAAC,UAAU,EAAE,MAAM;IAC5E,UAAU,IAAA,iRAAQ,EAAC,QAAQ,GAAG,CAAC,YAAY,EAAE,IAAI;IACjD,eAAe,IAAA,iRAAQ,EAAC,QAAQ,GAAG,CAAC,qBAAqB,IACrD,QAAQ,GAAG,CAAC,YAAY,IACxB,QAAQ,GAAG,CAAC,UAAU,EAAE,MAAM;IAClC,gBAAgB,IAAA,iRAAQ,EAAC,QAAQ,GAAG,CAAC,qBAAqB,IAAI,QAAQ,GAAG,CAAC,YAAY,EAAE,IAAI;IAC5F,WAAW;IACX,UAAU;IACV,aAAa,KAAQ;AACzB;AACA,qDAAqD;AACrD,IAAI,mBAAmB;AACvB,SAAS;IACL,IAAI,OAAO,qBAAqB,aAAa;QACzC,OAAO;YACH,aAAa,KAAQ;YACrB,kBAAkB,KAAQ;YAC1B,qBAAqB,KAAQ;YAC7B,MAAM;YACN,WAAW;YACX,gBAAgB;YAChB,OAAO,KAAQ;YACf,eAAe,IAAM;QACzB;IACJ;IACA,OAAO,IAAI,iBAAiB;AAChC;AACA,SAAS;IACL,IAAI,qBAAqB,MAAM;QAC3B,mBAAmB;IACvB;IACA,OAAO;AACX;AACA,QAAQ;AACR,MAAM,SAAS;IACX,OAAO,QAAQ,KAAK;IACpB,OAAO,QAAQ,KAAK;IACpB,MAAM,QAAQ,IAAI;AACtB;AACO,MAAM,iBAAiB,iWAAmB,GAAG;AAQ7C,SAAS,WAAW,OAAO;IAC9B,IAAI,CAAC,gBAAgB;QACjB,MAAM,IAAI,MAAM;IACpB;IACA,sDAAsD;IACtD,MAAM,QAAQ,8VAAgB,CAAC;IAC/B,IAAI,CAAC,SAAS,oDAAyB,cAAc;QACjD,MAAM,IAAI,MAAM;IACpB;IACA,MAAM,EAAE,QAAQ,EAAE,iBAAiB,EAAE,GAAG,WAAW,CAAC;IACpD,MAAM,wBAAwB,YAAY,MAAM,MAAM,KAAK;IAC3D,6VAAe,CAAC;QACZ,IAAI,uBAAuB;YACvB,MAAM,MAAM,GAAG,WAAW,QAAQ,CAAC,QAAQ,EAAE,IAAI,gBAAgB;gBAC7D,OAAO;gBACP,aAAa,OAAO,QAAQ,CAAC,IAAI;YACrC,IAAI;YACJ,IAAI,mBACA;iBAEA,OAAO,QAAQ,CAAC,IAAI,GAAG;QAC/B;IACJ,GAAG;QAAC;QAAuB;KAAkB;IAC7C,IAAI,uBAAuB;QACvB,OAAO;YACH,MAAM,MAAM,IAAI;YAChB,QAAQ,MAAM,MAAM;YACpB,QAAQ;QACZ;IACJ;IACA,OAAO;AACX;AACO,eAAe,WAAW,MAAM;IACnC,MAAM,UAAU,MAAM,IAAA,kRAAS,EAAC,WAAW,YAAY,QAAQ;IAC/D,IAAI,QAAQ,aAAa,MAAM;QAC3B,qDAAqD;QACrD,yBAAyB,WAAW,CAAC;YACjC,OAAO;YACP,MAAM;gBAAE,SAAS;YAAa;QAClC;IACJ;IACA,OAAO;AACX;AAOO,eAAe;IAClB,MAAM,WAAW,MAAM,IAAA,kRAAS,EAAC,QAAQ,YAAY;IACrD,OAAO,UAAU,aAAa;AAClC;AACO,eAAe;IAClB,OAAO,IAAA,kRAAS,EAAC,aAAa,YAAY;AAC9C;AACO,eAAe,OAAO,QAAQ,EAAE,OAAO,EAAE,mBAAmB;IAC/D,MAAM,EAAE,WAAW,EAAE,GAAG,MAAM,GAAG,WAAW,CAAC;IAC7C,MAAM,EAAE,WAAW,IAAI,EAAE,aAAa,eAAe,OAAO,QAAQ,CAAC,IAAI,EAAE,GAAG,cAAc,GAAG;IAC/F,MAAM,UAAU,IAAA,mRAAU,EAAC;IAC3B,MAAM,YAAY,MAAM;IACxB,IAAI,CAAC,WAAW;QACZ,MAAM,MAAM,GAAG,QAAQ,MAAM,CAAC;QAC9B,OAAO,QAAQ,CAAC,IAAI,GAAG;QACvB,QAAQ,0CAA0C;IACtD;IACA,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,SAAS,EAAE;QACnC,MAAM,MAAM,GAAG,QAAQ,QAAQ,EAAE,IAAI,gBAAgB;YACjD,aAAa;QACjB,IAAI;QACJ,OAAO,QAAQ,CAAC,IAAI,GAAG;QACvB,QAAQ,0CAA0C;IACtD;IACA,MAAM,eAAe,SAAS,CAAC,SAAS,CAAC,IAAI;IAC7C,IAAI,iBAAiB,YAAY;QAC7B,uCAAuC;QACvC,MAAM,IAAI,UAAU;YAChB,CAAC,aAAa,EAAE,SAAS,gCAAgC,CAAC;YAC1D;SACH,CAAC,IAAI,CAAC;IACX;IACA,MAAM,YAAY,GAAG,QAAQ,CAAC,EAAE,iBAAiB,gBAAgB,aAAa,SAAS,CAAC,EAAE,UAAU;IACpG,MAAM,YAAY,MAAM;IACxB,MAAM,MAAM,MAAM,MAAM,GAAG,UAAU,CAAC,EAAE,IAAI,gBAAgB,sBAAsB,EAAE;QAChF,QAAQ;QACR,SAAS;YACL,gBAAgB;YAChB,0BAA0B;QAC9B;QACA,MAAM,IAAI,gBAAgB;YACtB,GAAG,YAAY;YACf;YACA,aAAa;QACjB;IACJ;IACA,MAAM,OAAO,MAAM,IAAI,IAAI;IAC3B,IAAI,UAAU;QACV,MAAM,MAAM,KAAK,GAAG,IAAI;QACxB,OAAO,QAAQ,CAAC,IAAI,GAAG;QACvB,mFAAmF;QACnF,IAAI,IAAI,QAAQ,CAAC,MACb,OAAO,QAAQ,CAAC,MAAM;QAC1B;IACJ;IACA,MAAM,QAAQ,IAAI,IAAI,KAAK,GAAG,EAAE,YAAY,CAAC,GAAG,CAAC,YAAY;IAC7D,MAAM,OAAO,IAAI,IAAI,KAAK,GAAG,EAAE,YAAY,CAAC,GAAG,CAAC,WAAW;IAC3D,IAAI,IAAI,EAAE,EAAE;QACR,MAAM,WAAW,WAAW,CAAC;YAAE,OAAO;QAAU;IACpD;IACA,OAAO;QACH;QACA;QACA,QAAQ,IAAI,MAAM;QAClB,IAAI,IAAI,EAAE;QACV,KAAK,QAAQ,OAAO,KAAK,GAAG;IAChC;AACJ;AACO,eAAe,QAAQ,OAAO;IACjC,MAAM,EAAE,WAAW,IAAI,EAAE,aAAa,SAAS,eAAe,OAAO,QAAQ,CAAC,IAAI,EAAG,GAAG,WAAW,CAAC;IACpG,MAAM,UAAU,IAAA,mRAAU,EAAC;IAC3B,MAAM,YAAY,MAAM;IACxB,MAAM,MAAM,MAAM,MAAM,GAAG,QAAQ,QAAQ,CAAC,EAAE;QAC1C,QAAQ;QACR,SAAS;YACL,gBAAgB;YAChB,0BAA0B;QAC9B;QACA,MAAM,IAAI,gBAAgB;YAAE;YAAW,aAAa;QAAW;IACnE;IACA,MAAM,OAAO,MAAM,IAAI,IAAI;IAC3B,YAAY,WAAW,CAAC;QAAE,OAAO;QAAW,MAAM;YAAE,SAAS;QAAU;IAAE;IACzE,IAAI,UAAU;QACV,MAAM,MAAM,KAAK,GAAG,IAAI;QACxB,OAAO,QAAQ,CAAC,IAAI,GAAG;QACvB,mFAAmF;QACnF,IAAI,IAAI,QAAQ,CAAC,MACb,OAAO,QAAQ,CAAC,MAAM;QAC1B;IACJ;IACA,MAAM,WAAW,WAAW,CAAC;QAAE,OAAO;IAAU;IAChD,OAAO;AACX;AAWO,SAAS,gBAAgB,KAAK;IACjC,IAAI,CAAC,gBAAgB;QACjB,MAAM,IAAI,MAAM;IACpB;IACA,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,eAAe,EAAE,kBAAkB,EAAE,GAAG;IACpE,IAAI,UACA,WAAW,QAAQ,GAAG;IAC1B;;;KAGC,GACD,MAAM,oBAAoB,MAAM,OAAO,KAAK;IAC5C,wDAAwD,GACxD,WAAW,SAAS,GAAG,oBAAoB,IAAA,4QAAG,MAAK;IACnD,MAAM,CAAC,SAAS,WAAW,GAAG,4VAAc,CAAC;QACzC,IAAI,mBACA,WAAW,QAAQ,GAAG,MAAM,OAAO;QACvC,OAAO,MAAM,OAAO;IACxB;IACA,qDAAqD,GACrD,MAAM,CAAC,SAAS,WAAW,GAAG,4VAAc,CAAC,CAAC;IAC9C,6VAAe,CAAC;QACZ,WAAW,WAAW,GAAG,OAAO,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;YAC1C,IAAI;gBACA,MAAM,eAAe,UAAU;gBAC/B,gEAAgE;gBAChE,iDAAiD;gBACjD,IAAI,gBAAgB,WAAW,QAAQ,KAAK,WAAW;oBACnD,WAAW,SAAS,GAAG,IAAA,4QAAG;oBAC1B,WAAW,QAAQ,GAAG,MAAM,WAAW;wBACnC,WAAW,CAAC;oBAChB;oBACA,WAAW,WAAW,QAAQ;oBAC9B;gBACJ;gBACA,IACA,sEAAsE;gBACtE,mEAAmE;gBACnE,6BAA6B;gBAC7B,CAAC,SACG,kEAAkE;gBAClE,qEAAqE;gBACrE,4DAA4D;gBAC5D,gBAAgB;gBAChB,WAAW,QAAQ,KAAK,QACxB,wDAAwD;gBACxD,IAAA,4QAAG,MAAK,WAAW,SAAS,EAAE;oBAC9B;gBACJ;gBACA,qEAAqE;gBACrE,WAAW,SAAS,GAAG,IAAA,4QAAG;gBAC1B,WAAW,QAAQ,GAAG,MAAM;gBAC5B,WAAW,WAAW,QAAQ;YAClC,EACA,OAAO,OAAO;gBACV,OAAO,KAAK,CAAC,IAAI,2RAAkB,CAAC,MAAM,OAAO,EAAE;YACvD,SACQ;gBACJ,WAAW;YACf;QACJ;QACA,WAAW,WAAW;QACtB,OAAO;YACH,WAAW,SAAS,GAAG;YACvB,WAAW,QAAQ,GAAG;YACtB,WAAW,WAAW,GAAG,KAAQ;QACrC;IACJ,GAAG,EAAE;IACL,6VAAe,CAAC;QACZ,MAAM,SAAS,IAAM,WAAW,WAAW,CAAC;gBAAE,OAAO;YAAU;QAC/D,mEAAmE;QACnE,qEAAqE;QACrE,qEAAqE;QACrE,0BAA0B;QAC1B,iEAAiE;QACjE,gEAAgE;QAChE,+DAA+D;QAC/D,iEAAiE;QACjE,wDAAwD;QACxD,YAAY,gBAAgB,CAAC,WAAW;QACxC,OAAO,IAAM,YAAY,mBAAmB,CAAC,WAAW;IAC5D,GAAG,EAAE;IACL,6VAAe,CAAC;QACZ,MAAM,EAAE,uBAAuB,IAAI,EAAE,GAAG;QACxC,iEAAiE;QACjE,qEAAqE;QACrE,gCAAgC;QAChC,MAAM,oBAAoB;YACtB,IAAI,wBAAwB,SAAS,eAAe,KAAK,WACrD,WAAW,WAAW,CAAC;gBAAE,OAAO;YAAmB;QAC3D;QACA,SAAS,gBAAgB,CAAC,oBAAoB,mBAAmB;QACjE,OAAO,IAAM,SAAS,mBAAmB,CAAC,oBAAoB,mBAAmB;IACrF,GAAG;QAAC,MAAM,oBAAoB;KAAC;IAC/B,MAAM,WAAW,IAAA,kRAAS;IAC1B,iDAAiD;IACjD,MAAM,gBAAgB,uBAAuB,SAAS;IACtD,6VAAe,CAAC;QACZ,IAAI,mBAAmB,eAAe;YAClC,MAAM,uBAAuB,YAAY;gBACrC,IAAI,WAAW,QAAQ,EAAE;oBACrB,WAAW,WAAW,CAAC;wBAAE,OAAO;oBAAO;gBAC3C;YACJ,GAAG,kBAAkB;YACrB,OAAO,IAAM,cAAc;QAC/B;IACJ,GAAG;QAAC;QAAiB;KAAc;IACnC,MAAM,QAAQ,2VAAa,CAAC,IAAM,CAAC;YAC/B,MAAM;YACN,QAAQ,UACF,YACA,UACI,kBACA;YACV,MAAM,QAAO,IAAI;gBACb,IAAI,SACA;gBACJ,WAAW;gBACX,MAAM,aAAa,MAAM,IAAA,kRAAS,EAAC,WAAW,YAAY,QAAQ,OAAO,SAAS,cAC5E,YACA;oBAAE,MAAM;wBAAE,WAAW,MAAM;wBAAgB;oBAAK;gBAAE;gBACxD,WAAW;gBACX,IAAI,YAAY;oBACZ,WAAW;oBACX,YAAY,WAAW,CAAC;wBACpB,OAAO;wBACP,MAAM;4BAAE,SAAS;wBAAa;oBAClC;gBACJ;gBACA,OAAO;YACX;QACJ,CAAC,GAAG;QAAC;QAAS;KAAQ;IACtB,OACA,mBAAmB;IACnB,IAAA,yWAAI,EAAC,eAAe,QAAQ,EAAE;QAAE,OAAO;QAAO,UAAU;IAAS;AACrE", "ignoreList": [0], "debugId": null}}]}