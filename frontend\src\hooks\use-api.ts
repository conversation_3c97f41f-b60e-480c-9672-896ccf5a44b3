import { useSession } from "next-auth/react";
import { apiService } from "@/lib/api";
import {
	Session,
	Caisse,
	Reunion,
	Member,
	Payment,
	SessionMember,
	CreateSessionDto,
	UpdateSessionDto,
	CreateCaisseDto,
	UpdateCaisseDto,
	UpdateReunionDto,
	CreateMemberDto,
	UpdateMemberDto,
	CreatePaymentDto,
	CreateSessionMemberDto,
	MemberDebrief,
	PaymentFilters,
} from "@/types";

export function useApi() {
	const { data: session } = useSession();

	const authenticatedRequest = async <T>(
		endpoint: string,
		options: RequestInit = {},
	): Promise<T> => {
		if (!session?.accessToken) {
			throw new Error("Non authentifié");
		}

		return apiService.authenticatedRequest<T>(
			endpoint,
			session.accessToken,
			options,
		);
	};

	return {
		// Méthodes d'authentification (pas besoin de token)
		login: apiService.login.bind(apiService),
		register: apiService.register.bind(apiService),

		// Méthodes authentifiées
		authenticatedRequest,

		// Raccourcis pour les endpoints courants
		getUsers: () => authenticatedRequest<any[]>("/users"),
		getUser: (id: string) => authenticatedRequest<any>(`/users/${id}`),
		createUser: (userData: any) =>
			authenticatedRequest<any>("/users", {
				method: "POST",
				body: JSON.stringify(userData),
			}),
		updateUser: (id: string, userData: any) =>
			authenticatedRequest<any>(`/users/${id}`, {
				method: "PATCH",
				body: JSON.stringify(userData),
			}),
		deleteUser: (id: string) =>
			authenticatedRequest<any>(`/users/${id}`, {
				method: "DELETE",
			}),

		// Sessions
		getSessions: () => authenticatedRequest<Session[]>("/sessions"),
		getSession: (id: string) =>
			authenticatedRequest<Session>(`/sessions/${id}`),
		createSession: (sessionData: CreateSessionDto) =>
			authenticatedRequest<Session>("/sessions", {
				method: "POST",
				body: JSON.stringify(sessionData),
			}),
		updateSession: (id: string, sessionData: UpdateSessionDto) =>
			authenticatedRequest<Session>(`/sessions/${id}`, {
				method: "PATCH",
				body: JSON.stringify(sessionData),
			}),
		deleteSession: (id: string) =>
			authenticatedRequest<void>(`/sessions/${id}`, {
				method: "DELETE",
			}),

		// Caisses
		getCaisses: () => authenticatedRequest<Caisse[]>("/caisses"),
		getCaisse: (id: string) => authenticatedRequest<Caisse>(`/caisses/${id}`),
		createCaisse: (caisseData: CreateCaisseDto) =>
			authenticatedRequest<Caisse>("/caisses", {
				method: "POST",
				body: JSON.stringify(caisseData),
			}),
		updateCaisse: (id: string, caisseData: UpdateCaisseDto) =>
			authenticatedRequest<Caisse>(`/caisses/${id}`, {
				method: "PATCH",
				body: JSON.stringify(caisseData),
			}),
		deleteCaisse: (id: string) =>
			authenticatedRequest<void>(`/caisses/${id}`, {
				method: "DELETE",
			}),
		emargerCaisse: (id: string) =>
			authenticatedRequest<Caisse>(`/caisses/${id}/emarger`, {
				method: "POST",
			}),

		// Réunions
		getReunions: () => authenticatedRequest<Reunion[]>("/reunions"),
		getReunion: (id: string) =>
			authenticatedRequest<Reunion>(`/reunions/${id}`),
		updateReunion: (id: string, reunionData: UpdateReunionDto) =>
			authenticatedRequest<Reunion>(`/reunions/${id}`, {
				method: "PATCH",
				body: JSON.stringify(reunionData),
			}),

		// Members
		getMembers: () => authenticatedRequest<Member[]>("/members"),
		getMember: (id: string) => authenticatedRequest<Member>(`/members/${id}`),
		createMember: (memberData: CreateMemberDto) =>
			authenticatedRequest<Member>("/members", {
				method: "POST",
				body: JSON.stringify(memberData),
			}),
		updateMember: (id: string, memberData: UpdateMemberDto) =>
			authenticatedRequest<Member>(`/members/${id}`, {
				method: "PATCH",
				body: JSON.stringify(memberData),
			}),
		deleteMember: (id: string) =>
			authenticatedRequest<void>(`/members/${id}`, {
				method: "DELETE",
			}),
		getMemberDebrief: (id: string, filters?: PaymentFilters) => {
			const params = new URLSearchParams();
			if (filters?.dateFrom) params.append('dateFrom', filters.dateFrom);
			if (filters?.dateTo) params.append('dateTo', filters.dateTo);
			if (filters?.sessionId) params.append('sessionId', filters.sessionId);
			const query = params.toString() ? `?${params.toString()}` : '';
			return authenticatedRequest<MemberDebrief>(`/members/${id}/debrief${query}`);
		},

		// Payments
		createPayment: (paymentData: CreatePaymentDto) =>
			authenticatedRequest<Payment>("/payments", {
				method: "POST",
				body: JSON.stringify(paymentData),
			}),

		// Session Members (inscription des membres aux sessions)
		getSessionMembers: (sessionId: string) =>
			authenticatedRequest<SessionMember[]>(`/sessions/${sessionId}/members`),
		addSessionMember: (sessionMemberData: CreateSessionMemberDto) =>
			authenticatedRequest<SessionMember>("/session-members", {
				method: "POST",
				body: JSON.stringify(sessionMemberData),
			}),
		removeSessionMember: (sessionId: string, memberId: string) =>
			authenticatedRequest<void>(`/sessions/${sessionId}/members/${memberId}`, {
				method: "DELETE",
			}),
	};
}
