"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import Link from "next/link";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@/components/ui/form";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { apiService } from "@/lib/api";
import { UserRole } from "@/types";

const registerSchema = z.object({
	username: z
		.string()
		.min(3, "Le nom d'utilisateur doit contenir au moins 3 caractères"),
	password: z
		.string()
		.min(6, "Le mot de passe doit contenir au moins 6 caractères"),
	role: z.nativeEnum(UserRole),
});

type RegisterForm = z.infer<typeof registerSchema>;

export default function RegisterPage() {
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const [success, setSuccess] = useState(false);
	const router = useRouter();

	const form = useForm<RegisterForm>({
		resolver: zodResolver(registerSchema),
		defaultValues: {
			username: "",
			password: "",
			role: UserRole.CASHIER,
		},
	});

	const onSubmit = async (data: RegisterForm) => {
		setIsLoading(true);
		setError(null);

		try {
			const response = await apiService.register({
				...data,
			});

			console.log("Inscription réussie:", response);
			setSuccess(true);

			// Rediriger vers la page de connexion après 2 secondes
			setTimeout(() => {
				router.push("/auth/signin");
			}, 2000);
		} catch (error) {
			console.error("Erreur d'inscription:", error);
			if (error instanceof Error) {
				setError(error.message);
			} else {
				setError("Erreur lors de la création du compte. Veuillez réessayer.");
			}
		} finally {
			setIsLoading(false);
		}
	};

	if (success) {
		return (
			<div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
				<Card className="w-full max-w-md">
					<CardHeader className="text-center">
						<CardTitle className="text-2xl font-bold text-green-600">
							Compte créé avec succès !
						</CardTitle>
						<CardDescription>
							Vous allez être redirigé vers la page de connexion...
						</CardDescription>
					</CardHeader>
				</Card>
			</div>
		);
	}

	return (
		<div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
			<Card className="w-full max-w-md">
				<CardHeader className="space-y-1">
					<CardTitle className="text-2xl font-bold text-center">
						Créer un compte
					</CardTitle>
					<CardDescription className="text-center">
						Créez votre compte Tontine
					</CardDescription>
					<div className="text-xs text-orange-600 text-center bg-orange-50 p-2 rounded">
						⚠️ Page temporaire - Sera supprimée en production
					</div>
				</CardHeader>
				<CardContent>
					<Form {...form}>
						<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
							<FormField
								control={form.control}
								name="username"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Nom d'utilisateur</FormLabel>
										<FormControl>
											<Input
												placeholder="Nom d'utilisateur unique"
												{...field}
												disabled={isLoading}
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="password"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Mot de passe</FormLabel>
										<FormControl>
											<Input
												type="password"
												placeholder="Minimum 6 caractères"
												{...field}
												disabled={isLoading}
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="role"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Rôle</FormLabel>
										<Select
											onValueChange={field.onChange}
											defaultValue={field.value}
										>
											<FormControl>
												<SelectTrigger disabled={isLoading}>
													<SelectValue placeholder="Sélectionnez un rôle" />
												</SelectTrigger>
											</FormControl>
											<SelectContent>
												<SelectItem value={UserRole.CASHIER}>Caissier</SelectItem>
												<SelectItem value={UserRole.CONTROLLER}>Contrôleur</SelectItem>
												<SelectItem value={UserRole.SECRETARY_GENERAL}>Secrétaire Général</SelectItem>
											</SelectContent>
										</Select>
										<FormMessage />
									</FormItem>
								)}
							/>

							{error && (
								<div className="text-red-600 text-sm text-center bg-red-50 p-2 rounded">
									{error}
								</div>
							)}

							<Button type="submit" className="w-full" disabled={isLoading}>
								{isLoading ? "Création du compte..." : "Créer le compte"}
							</Button>
						</form>
					</Form>

					<div className="mt-4 text-center text-sm text-gray-600">
						Vous avez déjà un compte ?{" "}
						<Link href="/auth/signin" className="text-blue-600 hover:underline">
							Se connecter
						</Link>
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
