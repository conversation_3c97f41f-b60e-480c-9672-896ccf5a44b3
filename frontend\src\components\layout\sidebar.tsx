"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { signOut, useSession } from "next-auth/react";
import {
	Home,
	Users,
	Settings,
	LogOut,
	Calendar,
	Wallet,
	BarChart3,
	CreditCard,
	UserPlus,
} from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import {
	Sidebar,
	SidebarContent,
	SidebarFooter,
	SidebarGroup,
	SidebarGroupContent,
	SidebarGroupLabel,
	SidebarHeader,
	SidebarMenu,
	SidebarMenuButton,
	SidebarMenuItem,
	useSidebar,
} from "@/components/ui/sidebar";

interface NavigationItem {
	name: string;
	href: string;
	icon: React.ComponentType<{ className?: string }>;
	roles?: string[];
}

interface UserWithRole {
	name?: string | null;
	email?: string | null;
	role?: string;
}

const navigation: NavigationItem[] = [
	{ name: "Dashboard", href: "/dashboard", icon: Home },
	{
		name: "<PERSON><PERSON><PERSON>",
		href: "/dashboard/members",
		icon: UserPlus,
		roles: ["SECRETARY_GENERAL", "CONTROLLER"],
	},
	{
		name: "<PERSON>",
		href: "/dashboard/sessions",
		icon: Calendar,
		roles: ["SECRETARY_GENERAL", "CONTROLLER", "CASHIER"],
	},
	{
		name: "Caisses",
		href: "/dashboard/caisses",
		icon: Wallet,
		roles: ["SECRETARY_GENERAL", "CONTROLLER"],
	},
	{
		name: "Paiements",
		href: "/dashboard/payments",
		icon: CreditCard,
		roles: ["SECRETARY_GENERAL", "CONTROLLER", "CASHIER"],
	},
	{
		name: "Utilisateurs",
		href: "/dashboard/users",
		icon: Users,
		roles: ["SECRETARY_GENERAL"],
	},
	{
		name: "Rapports",
		href: "/dashboard/reports",
		icon: BarChart3,
		roles: ["SECRETARY_GENERAL", "CONTROLLER"],
	},
	{
		name: "Paramètres",
		href: "/dashboard/settings",
		icon: Settings,
		roles: ["SECRETARY_GENERAL"],
	},
];

export function AppSidebar() {
	const { state } = useSidebar();
	const pathname = usePathname();
	const { data: session } = useSession();

	const handleSignOut = () => {
		signOut({ callbackUrl: "/auth/signin" });
	};

	// Filter navigation items based on user role
	const filteredNavigation = navigation.filter((item) => {
		// Show all items without specific roles
		if (!item.roles) return true;

		// Check if user has the required role
		const userRole = (session?.user as UserWithRole)?.role;
		return userRole && item.roles.includes(userRole);
	});

	return (
		<Sidebar collapsible="icon">
			<SidebarHeader>
				<div className="flex items-center gap-2">
					<div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground">
						<Wallet className="size-4" />
					</div>
					<div className="grid flex-1 text-left text-sm leading-tight">
						<span className="truncate font-semibold">Tontine</span>
						<span className="truncate text-xs">Gestion de tontines</span>
					</div>
				</div>
			</SidebarHeader>

			<SidebarContent>
				<SidebarGroup>
					<SidebarGroupLabel>Navigation</SidebarGroupLabel>
					<SidebarGroupContent>
						<SidebarMenu>
							{filteredNavigation.map((item) => {
								const Icon = item.icon;
								const isActive =
									pathname === item.href ||
									pathname.startsWith(`${item.href}/`);

								return (
									<SidebarMenuItem key={item.name}>
										<SidebarMenuButton asChild isActive={isActive}>
											<Link href={item.href}>
												<Icon />
												<span>{item.name}</span>
											</Link>
										</SidebarMenuButton>
									</SidebarMenuItem>
								);
							})}
						</SidebarMenu>
					</SidebarGroupContent>
				</SidebarGroup>
			</SidebarContent>

			<SidebarFooter>
				{session?.user && (
					<>
						<div className="flex items-center gap-2">
							<div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-accent text-sidebar-accent-foreground">
								<span className="text-xs font-medium">
									{session.user.name?.charAt(0).toUpperCase()}
								</span>
							</div>
							<div className="grid flex-1 text-left text-sm leading-tight">
								<span className="truncate font-semibold">
									{session.user.name}
								</span>
								<span className="truncate text-xs">{session.user.email}</span>
							</div>
						</div>
						<Button
							variant="destructive"
							onClick={handleSignOut}
							className="w-full justify-start"
						>
							<LogOut className="mr-2 h-4 w-4" />
							{state === "expanded" ? "Sign out" : null}
						</Button>
					</>
				)}
			</SidebarFooter>
		</Sidebar>
	);
}
