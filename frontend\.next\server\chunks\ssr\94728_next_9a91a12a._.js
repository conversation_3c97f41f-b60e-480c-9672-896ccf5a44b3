module.exports = [
"[project]/frontend/node_modules/.pnpm/next@15.5.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/navigation.js [app-ssr] (ecmascript)", ((__turbopack_context__, module, exports) => {

module.exports = __turbopack_context__.r("[project]/frontend/node_modules/.pnpm/next@15.5.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/client/components/navigation.js [app-ssr] (ecmascript)");
}),
"[project]/frontend/node_modules/.pnpm/next@15.5.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/server.js [app-ssr] (ecmascript)", ((__turbopack_context__, module, exports) => {

const serverExports = {
    NextRequest: __turbopack_context__.r("[project]/frontend/node_modules/.pnpm/next@15.5.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/web/spec-extension/request.js [app-ssr] (ecmascript)").NextRequest,
    NextResponse: __turbopack_context__.r("[project]/frontend/node_modules/.pnpm/next@15.5.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/web/spec-extension/response.js [app-ssr] (ecmascript)").NextResponse,
    ImageResponse: __turbopack_context__.r("[project]/frontend/node_modules/.pnpm/next@15.5.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/web/spec-extension/image-response.js [app-ssr] (ecmascript)").ImageResponse,
    userAgentFromString: __turbopack_context__.r("[project]/frontend/node_modules/.pnpm/next@15.5.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/web/spec-extension/user-agent.js [app-ssr] (ecmascript)").userAgentFromString,
    userAgent: __turbopack_context__.r("[project]/frontend/node_modules/.pnpm/next@15.5.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/web/spec-extension/user-agent.js [app-ssr] (ecmascript)").userAgent,
    URLPattern: __turbopack_context__.r("[project]/frontend/node_modules/.pnpm/next@15.5.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/web/spec-extension/url-pattern.js [app-ssr] (ecmascript)").URLPattern,
    after: __turbopack_context__.r("[project]/frontend/node_modules/.pnpm/next@15.5.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/after/index.js [app-ssr] (ecmascript)").after,
    connection: __turbopack_context__.r("[project]/frontend/node_modules/.pnpm/next@15.5.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/request/connection.js [app-ssr] (ecmascript)").connection,
    unstable_rootParams: __turbopack_context__.r("[project]/frontend/node_modules/.pnpm/next@15.5.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/request/root-params.js [app-ssr] (ecmascript)").unstable_rootParams
};
// https://nodejs.org/api/esm.html#commonjs-namespaces
// When importing CommonJS modules, the module.exports object is provided as the default export
module.exports = serverExports;
// make import { xxx } from 'next/server' work
exports.NextRequest = serverExports.NextRequest;
exports.NextResponse = serverExports.NextResponse;
exports.ImageResponse = serverExports.ImageResponse;
exports.userAgentFromString = serverExports.userAgentFromString;
exports.userAgent = serverExports.userAgent;
exports.URLPattern = serverExports.URLPattern;
exports.after = serverExports.after;
exports.connection = serverExports.connection;
exports.unstable_rootParams = serverExports.unstable_rootParams;
}),
"[project]/frontend/node_modules/.pnpm/next@15.5.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/headers.js [app-ssr] (ecmascript)", ((__turbopack_context__, module, exports) => {

module.exports.cookies = __turbopack_context__.r("[project]/frontend/node_modules/.pnpm/next@15.5.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/request/cookies.js [app-ssr] (ecmascript)").cookies;
module.exports.headers = __turbopack_context__.r("[project]/frontend/node_modules/.pnpm/next@15.5.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/request/headers.js [app-ssr] (ecmascript)").headers;
module.exports.draftMode = __turbopack_context__.r("[project]/frontend/node_modules/.pnpm/next@15.5.2_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/request/draft-mode.js [app-ssr] (ecmascript)").draftMode;
}),
];

//# sourceMappingURL=94728_next_9a91a12a._.js.map