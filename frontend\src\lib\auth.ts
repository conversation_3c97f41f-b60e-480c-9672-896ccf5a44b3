import NextAuth from "next-auth";
import Cred<PERSON><PERSON><PERSON><PERSON>ider from "next-auth/providers/credentials";
import { z } from "zod";
import { apiService } from "./api";
import { decodeJwt } from "./utils";

const loginSchema = z.object({
	username: z.string().min(1, "Username is required"),
	password: z.string().min(1, "Password is required"),
});

export const { handlers, signIn, signOut, auth } = NextAuth({
	providers: [
		CredentialsProvider({
			name: "credentials",
			credentials: {
				username: { label: "Username", type: "text" },
				password: { label: "Password", type: "password" },
			},
			async authorize(credentials) {
				try {
					const { username, password } = loginSchema.parse(credentials);

					// Authentification avec l'API backend
					const response = await apiService.login({ username, password });
					const jwt = decodeJwt(response.access_token);
					if(!jwt) return null;
					const jwtPayload = jwt.payload as unknown as { sub: string; username: string; role:  'secretary_general' | 'controller' | 'cashier' };
					
						return {
							id: jwtPayload.sub,
							username: jwtPayload.username,
							role: jwtPayload.role,
							accessToken: response.access_token,
					}

				} catch (error) {
					console.error("Erreur d'authentification:", error);
					return null;
				}
			},
		}),
	],
	pages: {
		signIn: "/auth/signin",
	},
	callbacks: {
		authorized: ({ auth }) => !!auth,
		async jwt({ token, user }) {
			if (user) {
				token.username = user.username;
				token.role = user.role;
				token.accessToken = user.accessToken;
			}
			return token;
		},
		async session({ session, token }) {
			if (token) {
				session.user.id = token.sub || "";
				session.user.username = token.username as string;
				session.user.role = token.role as  'secretary_general' | 'controller' | 'cashier';
				session.accessToken = token.accessToken as string;
			}
			return session;
		},
	},
	session: {
		strategy: "jwt",
	},
});
