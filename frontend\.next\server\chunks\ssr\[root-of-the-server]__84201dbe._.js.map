{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport { cn } from '@/lib/utils';\n\nfunction Card({ className, ...props }: React.ComponentProps<'div'>) {\n  return (\n    <div\n      data-slot='card'\n      className={cn(\n        'bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm',\n        className\n      )}\n      {...props}\n    />\n  );\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<'div'>) {\n  return (\n    <div\n      data-slot='card-header'\n      className={cn(\n        '@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6',\n        className\n      )}\n      {...props}\n    />\n  );\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<'div'>) {\n  return (\n    <div\n      data-slot='card-title'\n      className={cn('leading-none font-semibold', className)}\n      {...props}\n    />\n  );\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<'div'>) {\n  return (\n    <div\n      data-slot='card-description'\n      className={cn('text-muted-foreground text-sm', className)}\n      {...props}\n    />\n  );\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<'div'>) {\n  return (\n    <div\n      data-slot='card-action'\n      className={cn(\n        'col-start-2 row-span-2 row-start-1 self-start justify-self-end',\n        className\n      )}\n      {...props}\n    />\n  );\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<'div'>) {\n  return (\n    <div\n      data-slot='card-content'\n      className={cn('px-6', className)}\n      {...props}\n    />\n  );\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<'div'>) {\n  return (\n    <div\n      data-slot='card-footer'\n      className={cn('flex items-center px-6 [.border-t]:pt-6', className)}\n      {...props}\n    />\n  );\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,yXAAC;QACC,aAAU;QACV,WAAW,IAAA,qIAAE,EACX,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,yXAAC;QACC,aAAU;QACV,WAAW,IAAA,qIAAE,EACX,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,yXAAC;QACC,aAAU;QACV,WAAW,IAAA,qIAAE,EAAC,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,yXAAC;QACC,aAAU;QACV,WAAW,IAAA,qIAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,yXAAC;QACC,aAAU;QACV,WAAW,IAAA,qIAAE,EACX,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,yXAAC;QACC,aAAU;QACV,WAAW,IAAA,qIAAE,EAAC,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,yXAAC;QACC,aAAU;QACV,WAAW,IAAA,qIAAE,EAAC,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 112, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/lib/auth.ts"], "sourcesContent": ["import NextAuth from \"next-auth\";\nimport Cred<PERSON><PERSON><PERSON><PERSON>ider from \"next-auth/providers/credentials\";\nimport { z } from \"zod\";\nimport { apiService } from \"./api\";\nimport { decodeJwt } from \"./utils\";\n\nconst loginSchema = z.object({\n\tusername: z.string().min(1, \"Username is required\"),\n\tpassword: z.string().min(1, \"Password is required\"),\n});\n\nexport const { handlers, signIn, signOut, auth } = NextAuth({\n\tproviders: [\n\t\tCredentialsProvider({\n\t\t\tname: \"credentials\",\n\t\t\tcredentials: {\n\t\t\t\tusername: { label: \"Username\", type: \"text\" },\n\t\t\t\tpassword: { label: \"Password\", type: \"password\" },\n\t\t\t},\n\t\t\tasync authorize(credentials) {\n\t\t\t\ttry {\n\t\t\t\t\tconst { username, password } = loginSchema.parse(credentials);\n\n\t\t\t\t\t// Authentification avec l'API backend\n\t\t\t\t\tconst response = await apiService.login({ username, password });\n\t\t\t\t\tconst jwt = decodeJwt(response.access_token);\n\t\t\t\t\tif(!jwt) return null;\n\t\t\t\t\tconst jwtPayload = jwt.payload as unknown as { sub: string; username: string; role:  'secretary_general' | 'controller' | 'cashier' };\n\t\t\t\t\t\n\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\tid: jwtPayload.sub,\n\t\t\t\t\t\t\tusername: jwtPayload.username,\n\t\t\t\t\t\t\trole: jwtPayload.role,\n\t\t\t\t\t\t\taccessToken: response.access_token,\n\t\t\t\t\t}\n\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error(\"Erreur d'authentification:\", error);\n\t\t\t\t\treturn null;\n\t\t\t\t}\n\t\t\t},\n\t\t}),\n\t],\n\tpages: {\n\t\tsignIn: \"/auth/signin\",\n\t},\n\tcallbacks: {\n\t\tauthorized: ({ auth }) => !!auth,\n\t\tasync jwt({ token, user }) {\n\t\t\tif (user) {\n\t\t\t\ttoken.username = user.username;\n\t\t\t\ttoken.role = user.role;\n\t\t\t\ttoken.accessToken = user.accessToken;\n\t\t\t}\n\t\t\treturn token;\n\t\t},\n\t\tasync session({ session, token }) {\n\t\t\tif (token) {\n\t\t\t\tsession.user.id = token.sub || \"\";\n\t\t\t\tsession.user.username = token.username as string;\n\t\t\t\tsession.user.role = token.role as  'secretary_general' | 'controller' | 'cashier';\n\t\t\t\tsession.accessToken = token.accessToken as string;\n\t\t\t}\n\t\t\treturn session;\n\t\t},\n\t},\n\tsession: {\n\t\tstrategy: \"jwt\",\n\t},\n});\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,cAAc,8OAAC,CAAC,MAAM,CAAC;IAC5B,UAAU,8OAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,UAAU,8OAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AAC7B;AAEO,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,IAAA,wRAAQ,EAAC;IAC3D,WAAW;QACV,IAAA,6OAAmB,EAAC;YACnB,MAAM;YACN,aAAa;gBACZ,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAO;gBAC5C,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YACjD;YACA,MAAM,WAAU,WAAW;gBAC1B,IAAI;oBACH,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,YAAY,KAAK,CAAC;oBAEjD,sCAAsC;oBACtC,MAAM,WAAW,MAAM,2IAAU,CAAC,KAAK,CAAC;wBAAE;wBAAU;oBAAS;oBAC7D,MAAM,MAAM,IAAA,4IAAS,EAAC,SAAS,YAAY;oBAC3C,IAAG,CAAC,KAAK,OAAO;oBAChB,MAAM,aAAa,IAAI,OAAO;oBAE7B,OAAO;wBACN,IAAI,WAAW,GAAG;wBAClB,UAAU,WAAW,QAAQ;wBAC7B,MAAM,WAAW,IAAI;wBACrB,aAAa,SAAS,YAAY;oBACpC;gBAED,EAAE,OAAO,OAAO;oBACf,QAAQ,KAAK,CAAC,8BAA8B;oBAC5C,OAAO;gBACR;YACD;QACD;KACA;IACD,OAAO;QACN,QAAQ;IACT;IACA,WAAW;QACV,YAAY,CAAC,EAAE,IAAI,EAAE,GAAK,CAAC,CAAC;QAC5B,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACxB,IAAI,MAAM;gBACT,MAAM,QAAQ,GAAG,KAAK,QAAQ;gBAC9B,MAAM,IAAI,GAAG,KAAK,IAAI;gBACtB,MAAM,WAAW,GAAG,KAAK,WAAW;YACrC;YACA,OAAO;QACR;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC/B,IAAI,OAAO;gBACV,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG,IAAI;gBAC/B,QAAQ,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;gBACtC,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;gBAC9B,QAAQ,WAAW,GAAG,MAAM,WAAW;YACxC;YACA,OAAO;QACR;IACD;IACA,SAAS;QACR,UAAU;IACX;AACD", "debugId": null}}, {"offset": {"line": 206, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/lib/api.ts"], "sourcesContent": ["import { signOut } from \"next-auth/react\";\nimport { signOut as ServerSignOut } from \"./auth\";\n\n// Configuration de l'API\nexport const API_BASE_URL =\n\tprocess.env.NEXT_PUBLIC_API_URL || \"http://localhost:4000\";\n\n// Types pour l'authentification\nexport interface LoginRequest {\n\tusername: string;\n\tpassword: string;\n}\n\nexport interface LoginResponse {\n\taccess_token: string;\n\tuser: {\n\t\tid: string;\n\t\tusername: string;\n\t\tnom: string;\n\t\tprenom: string;\n\t\temail: string;\n\t\trole: \"admin\" | \"tresorier\" | \"membre\";\n\t\tstatut: \"actif\" | \"en_attente\" | \"suspendu\";\n\t};\n}\n\nexport interface RegisterRequest {\n\tusername: string;\n\tpassword: string;\n\trole: string;\n\t// statut?: \"actif\" | \"en_attente\" | \"suspendu\";\n}\n\nexport interface ApiError {\n\tmessage: string;\n\tstatusCode: number;\n\terror?: string;\n}\n\n// Service API\nexport class ApiService {\n\tprivate baseUrl: string;\n\n\tconstructor(baseUrl: string = API_BASE_URL) {\n\t\tthis.baseUrl = baseUrl;\n\t}\n\n\tprivate async request<T>(\n\t\tendpoint: string,\n\t\toptions: RequestInit = {},\n\t): Promise<T> {\n\t\tconst url = `${this.baseUrl}${endpoint}`;\n\n\t\tconst config: RequestInit = {\n\t\t\theaders: {\n\t\t\t\t\"Content-Type\": \"application/json\",\n\t\t\t\t...options.headers,\n\t\t\t},\n\t\t\t...options,\n\t\t};\n\n\t\ttry {\n\t\t\tconst response = await fetch(url, config);\n\n\t\t\tif (!response.ok) {\n\t\t\t\tconst errorData: ApiError = await response.json().catch(() => ({\n\t\t\t\t\tmessage: \"Une erreur est survenue\",\n\t\t\t\t\tstatusCode: response.status,\n\t\t\t\t}));\n\n\t\t\t\t// 🚨 Gestion du cas 401\n\t\t\t\tif (response.status === 401) {\n\t\t\t\t\t// Si côté client → on déconnecte\n\t\t\t\t\tif (typeof window !== \"undefined\") {\n\t\t\t\t\t\tawait signOut({ callbackUrl: \"/auth/signin\" });\n\t\t\t\t\t} else {\n\t\t\t\t\t\tawait ServerSignOut({ redirectTo: \"/auth/signin\" });\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tthrow new Error(errorData.message || `HTTP ${response.status}`);\n\t\t\t}\n\n\t\t\treturn await response.json();\n\t\t} catch (error) {\n\t\t\tconsole.log(error);\n\n\t\t\tif (error instanceof Error) {\n\t\t\t\tthrow error;\n\t\t\t}\n\t\t\tthrow new Error(\"Erreur de connexion au serveur\");\n\t\t}\n\t}\n\n\t// Authentification\n\tasync login(credentials: LoginRequest): Promise<LoginResponse> {\n\t\treturn this.request<LoginResponse>(\"/auth/login\", {\n\t\t\tmethod: \"POST\",\n\t\t\tbody: JSON.stringify(credentials),\n\t\t});\n\t}\n\n\tasync register(\n\t\tuserData: RegisterRequest,\n\t): Promise<{ message: string; user: any }> {\n\t\treturn this.request<{ message: string; user: any }>(\"/auth/register\", {\n\t\t\tmethod: \"POST\",\n\t\t\tbody: JSON.stringify(userData),\n\t\t});\n\t}\n\n\t// Méthodes avec authentification\n\tasync authenticatedRequest<T>(\n\t\tendpoint: string,\n\t\ttoken: string,\n\t\toptions: RequestInit = {},\n\t): Promise<T> {\n\t\treturn this.request<T>(endpoint, {\n\t\t\t...options,\n\t\t\theaders: {\n\t\t\t\t...options.headers,\n\t\t\t\t\"Content-Type\": \"application/json\",\n\t\t\t\tAuthorization: `Bearer ${token}`,\n\t\t\t},\n\t\t});\n\t}\n\n\t// Utilisateurs\n\tasync getUsers(token: string): Promise<any[]> {\n\t\treturn this.authenticatedRequest<any[]>(\"/users\", token);\n\t}\n\n\tasync getUser(id: string, token: string): Promise<any> {\n\t\treturn this.authenticatedRequest<any>(`/users/${id}`, token);\n\t}\n}\n\n// Instance par défaut\nexport const apiService = new ApiService();\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAGO,MAAM,eACZ,QAAQ,GAAG,CAAC,mBAAmB,IAAI;AAmC7B,MAAM;IACJ,QAAgB;IAExB,YAAY,UAAkB,YAAY,CAAE;QAC3C,IAAI,CAAC,OAAO,GAAG;IAChB;IAEA,MAAc,QACb,QAAgB,EAChB,UAAuB,CAAC,CAAC,EACZ;QACb,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,GAAG,UAAU;QAExC,MAAM,SAAsB;YAC3B,SAAS;gBACR,gBAAgB;gBAChB,GAAG,QAAQ,OAAO;YACnB;YACA,GAAG,OAAO;QACX;QAEA,IAAI;YACH,MAAM,WAAW,MAAM,MAAM,KAAK;YAElC,IAAI,CAAC,SAAS,EAAE,EAAE;gBACjB,MAAM,YAAsB,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC;wBAC9D,SAAS;wBACT,YAAY,SAAS,MAAM;oBAC5B,CAAC;gBAED,wBAAwB;gBACxB,IAAI,SAAS,MAAM,KAAK,KAAK;oBAC5B,iCAAiC;oBACjC;;yBAEO;wBACN,MAAM,IAAA,yIAAa,EAAC;4BAAE,YAAY;wBAAe;oBAClD;gBACD;gBACA,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,CAAC,KAAK,EAAE,SAAS,MAAM,EAAE;YAC/D;YAEA,OAAO,MAAM,SAAS,IAAI;QAC3B,EAAE,OAAO,OAAO;YACf,QAAQ,GAAG,CAAC;YAEZ,IAAI,iBAAiB,OAAO;gBAC3B,MAAM;YACP;YACA,MAAM,IAAI,MAAM;QACjB;IACD;IAEA,mBAAmB;IACnB,MAAM,MAAM,WAAyB,EAA0B;QAC9D,OAAO,IAAI,CAAC,OAAO,CAAgB,eAAe;YACjD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACtB;IACD;IAEA,MAAM,SACL,QAAyB,EACiB;QAC1C,OAAO,IAAI,CAAC,OAAO,CAAiC,kBAAkB;YACrE,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACtB;IACD;IAEA,iCAAiC;IACjC,MAAM,qBACL,QAAgB,EAChB,KAAa,EACb,UAAuB,CAAC,CAAC,EACZ;QACb,OAAO,IAAI,CAAC,OAAO,CAAI,UAAU;YAChC,GAAG,OAAO;YACV,SAAS;gBACR,GAAG,QAAQ,OAAO;gBAClB,gBAAgB;gBAChB,eAAe,CAAC,OAAO,EAAE,OAAO;YACjC;QACD;IACD;IAEA,eAAe;IACf,MAAM,SAAS,KAAa,EAAkB;QAC7C,OAAO,IAAI,CAAC,oBAAoB,CAAQ,UAAU;IACnD;IAEA,MAAM,QAAQ,EAAU,EAAE,KAAa,EAAgB;QACtD,OAAO,IAAI,CAAC,oBAAoB,CAAM,CAAC,OAAO,EAAE,IAAI,EAAE;IACvD;AACD;AAGO,MAAM,aAAa,IAAI", "debugId": null}}, {"offset": {"line": 299, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/hooks/use-api.ts"], "sourcesContent": ["import { useSession } from \"next-auth/react\";\nimport { apiService } from \"@/lib/api\";\nimport {\n\tSession,\n\tCaisse,\n\tReunion,\n\tMember,\n\tPayment,\n\tSessionMember,\n\tCreateSessionDto,\n\tUpdateSessionDto,\n\tCreateCaisseDto,\n\tUpdateCaisseDto,\n\tUpdateReunionDto,\n\tCreateMemberDto,\n\tUpdateMemberDto,\n\tCreatePaymentDto,\n\tCreateSessionMemberDto,\n\tMemberDebrief,\n\tPaymentFilters,\n} from \"@/types\";\n\nexport function useApi() {\n\tconst { data: session } = useSession();\n\n\tconst authenticatedRequest = async <T>(\n\t\tendpoint: string,\n\t\toptions: RequestInit = {},\n\t): Promise<T> => {\n\t\tif (!session?.accessToken) {\n\t\t\tthrow new Error(\"Non authentifié\");\n\t\t}\n\n\t\treturn apiService.authenticatedRequest<T>(\n\t\t\tendpoint,\n\t\t\tsession.accessToken,\n\t\t\toptions,\n\t\t);\n\t};\n\n\treturn {\n\t\t// Méthodes d'authentification (pas besoin de token)\n\t\tlogin: apiService.login.bind(apiService),\n\t\tregister: apiService.register.bind(apiService),\n\n\t\t// Méthodes authentifiées\n\t\tauthenticatedRequest,\n\n\t\t// Raccourcis pour les endpoints courants\n\t\tgetUsers: () => authenticatedRequest<any[]>(\"/users\"),\n\t\tgetUser: (id: string) => authenticatedRequest<any>(`/users/${id}`),\n\t\tcreateUser: (userData: any) =>\n\t\t\tauthenticatedRequest<any>(\"/users\", {\n\t\t\t\tmethod: \"POST\",\n\t\t\t\tbody: JSON.stringify(userData),\n\t\t\t}),\n\t\tupdateUser: (id: string, userData: any) =>\n\t\t\tauthenticatedRequest<any>(`/users/${id}`, {\n\t\t\t\tmethod: \"PATCH\",\n\t\t\t\tbody: JSON.stringify(userData),\n\t\t\t}),\n\t\tdeleteUser: (id: string) =>\n\t\t\tauthenticatedRequest<any>(`/users/${id}`, {\n\t\t\t\tmethod: \"DELETE\",\n\t\t\t}),\n\n\t\t// Sessions\n\t\tgetSessions: () => authenticatedRequest<Session[]>(\"/sessions\"),\n\t\tgetSession: (id: string) =>\n\t\t\tauthenticatedRequest<Session>(`/sessions/${id}`),\n\t\tcreateSession: (sessionData: CreateSessionDto) =>\n\t\t\tauthenticatedRequest<Session>(\"/sessions\", {\n\t\t\t\tmethod: \"POST\",\n\t\t\t\tbody: JSON.stringify(sessionData),\n\t\t\t}),\n\t\tupdateSession: (id: string, sessionData: UpdateSessionDto) =>\n\t\t\tauthenticatedRequest<Session>(`/sessions/${id}`, {\n\t\t\t\tmethod: \"PATCH\",\n\t\t\t\tbody: JSON.stringify(sessionData),\n\t\t\t}),\n\t\tdeleteSession: (id: string) =>\n\t\t\tauthenticatedRequest<void>(`/sessions/${id}`, {\n\t\t\t\tmethod: \"DELETE\",\n\t\t\t}),\n\n\t\t// Caisses\n\t\tgetCaisses: () => authenticatedRequest<Caisse[]>(\"/caisses\"),\n\t\tgetCaisse: (id: string) => authenticatedRequest<Caisse>(`/caisses/${id}`),\n\t\tcreateCaisse: (caisseData: CreateCaisseDto) =>\n\t\t\tauthenticatedRequest<Caisse>(\"/caisses\", {\n\t\t\t\tmethod: \"POST\",\n\t\t\t\tbody: JSON.stringify(caisseData),\n\t\t\t}),\n\t\tupdateCaisse: (id: string, caisseData: UpdateCaisseDto) =>\n\t\t\tauthenticatedRequest<Caisse>(`/caisses/${id}`, {\n\t\t\t\tmethod: \"PATCH\",\n\t\t\t\tbody: JSON.stringify(caisseData),\n\t\t\t}),\n\t\tdeleteCaisse: (id: string) =>\n\t\t\tauthenticatedRequest<void>(`/caisses/${id}`, {\n\t\t\t\tmethod: \"DELETE\",\n\t\t\t}),\n\t\temargerCaisse: (id: string) =>\n\t\t\tauthenticatedRequest<Caisse>(`/caisses/${id}/emarger`, {\n\t\t\t\tmethod: \"POST\",\n\t\t\t}),\n\n\t\t// Réunions\n\t\tgetReunions: () => authenticatedRequest<Reunion[]>(\"/reunions\"),\n\t\tgetReunion: (id: string) =>\n\t\t\tauthenticatedRequest<Reunion>(`/reunions/${id}`),\n\t\tupdateReunion: (id: string, reunionData: UpdateReunionDto) =>\n\t\t\tauthenticatedRequest<Reunion>(`/reunions/${id}`, {\n\t\t\t\tmethod: \"PATCH\",\n\t\t\t\tbody: JSON.stringify(reunionData),\n\t\t\t}),\n\n\t\t// Members\n\t\tgetMembers: () => authenticatedRequest<Member[]>(\"/members\"),\n\t\tgetMember: (id: string) => authenticatedRequest<Member>(`/members/${id}`),\n\t\tcreateMember: (memberData: CreateMemberDto) =>\n\t\t\tauthenticatedRequest<Member>(\"/members\", {\n\t\t\t\tmethod: \"POST\",\n\t\t\t\tbody: JSON.stringify(memberData),\n\t\t\t}),\n\t\tupdateMember: (id: string, memberData: UpdateMemberDto) =>\n\t\t\tauthenticatedRequest<Member>(`/members/${id}`, {\n\t\t\t\tmethod: \"PATCH\",\n\t\t\t\tbody: JSON.stringify(memberData),\n\t\t\t}),\n\t\tdeleteMember: (id: string) =>\n\t\t\tauthenticatedRequest<void>(`/members/${id}`, {\n\t\t\t\tmethod: \"DELETE\",\n\t\t\t}),\n\t\tgetMemberDebrief: (id: string, filters?: PaymentFilters) => {\n\t\t\tconst params = new URLSearchParams();\n\t\t\tif (filters?.dateFrom) params.append('dateFrom', filters.dateFrom);\n\t\t\tif (filters?.dateTo) params.append('dateTo', filters.dateTo);\n\t\t\tif (filters?.sessionId) params.append('sessionId', filters.sessionId);\n\t\t\tconst query = params.toString() ? `?${params.toString()}` : '';\n\t\t\treturn authenticatedRequest<MemberDebrief>(`/members/${id}/debrief${query}`);\n\t\t},\n\n\t\t// Payments\n\t\tcreatePayment: (paymentData: CreatePaymentDto) =>\n\t\t\tauthenticatedRequest<Payment>(\"/payments\", {\n\t\t\t\tmethod: \"POST\",\n\t\t\t\tbody: JSON.stringify(paymentData),\n\t\t\t}),\n\n\t\t// Session Members (inscription des membres aux sessions)\n\t\tgetSessionMembers: (sessionId: string) =>\n\t\t\tauthenticatedRequest<SessionMember[]>(`/sessions/${sessionId}/members`),\n\t\taddSessionMember: (sessionMemberData: CreateSessionMemberDto) =>\n\t\t\tauthenticatedRequest<SessionMember>(\"/session-members\", {\n\t\t\t\tmethod: \"POST\",\n\t\t\t\tbody: JSON.stringify(sessionMemberData),\n\t\t\t}),\n\t\tremoveSessionMember: (sessionId: string, memberId: string) =>\n\t\t\tauthenticatedRequest<void>(`/sessions/${sessionId}/members/${memberId}`, {\n\t\t\t\tmethod: \"DELETE\",\n\t\t\t}),\n\t};\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAqBO,SAAS;IACf,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,IAAA,2QAAU;IAEpC,MAAM,uBAAuB,OAC5B,UACA,UAAuB,CAAC,CAAC;QAEzB,IAAI,CAAC,SAAS,aAAa;YAC1B,MAAM,IAAI,MAAM;QACjB;QAEA,OAAO,2IAAU,CAAC,oBAAoB,CACrC,UACA,QAAQ,WAAW,EACnB;IAEF;IAEA,OAAO;QACN,oDAAoD;QACpD,OAAO,2IAAU,CAAC,KAAK,CAAC,IAAI,CAAC,2IAAU;QACvC,UAAU,2IAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,2IAAU;QAE7C,yBAAyB;QACzB;QAEA,yCAAyC;QACzC,UAAU,IAAM,qBAA4B;QAC5C,SAAS,CAAC,KAAe,qBAA0B,CAAC,OAAO,EAAE,IAAI;QACjE,YAAY,CAAC,WACZ,qBAA0B,UAAU;gBACnC,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACtB;QACD,YAAY,CAAC,IAAY,WACxB,qBAA0B,CAAC,OAAO,EAAE,IAAI,EAAE;gBACzC,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACtB;QACD,YAAY,CAAC,KACZ,qBAA0B,CAAC,OAAO,EAAE,IAAI,EAAE;gBACzC,QAAQ;YACT;QAED,WAAW;QACX,aAAa,IAAM,qBAAgC;QACnD,YAAY,CAAC,KACZ,qBAA8B,CAAC,UAAU,EAAE,IAAI;QAChD,eAAe,CAAC,cACf,qBAA8B,aAAa;gBAC1C,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACtB;QACD,eAAe,CAAC,IAAY,cAC3B,qBAA8B,CAAC,UAAU,EAAE,IAAI,EAAE;gBAChD,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACtB;QACD,eAAe,CAAC,KACf,qBAA2B,CAAC,UAAU,EAAE,IAAI,EAAE;gBAC7C,QAAQ;YACT;QAED,UAAU;QACV,YAAY,IAAM,qBAA+B;QACjD,WAAW,CAAC,KAAe,qBAA6B,CAAC,SAAS,EAAE,IAAI;QACxE,cAAc,CAAC,aACd,qBAA6B,YAAY;gBACxC,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACtB;QACD,cAAc,CAAC,IAAY,aAC1B,qBAA6B,CAAC,SAAS,EAAE,IAAI,EAAE;gBAC9C,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACtB;QACD,cAAc,CAAC,KACd,qBAA2B,CAAC,SAAS,EAAE,IAAI,EAAE;gBAC5C,QAAQ;YACT;QACD,eAAe,CAAC,KACf,qBAA6B,CAAC,SAAS,EAAE,GAAG,QAAQ,CAAC,EAAE;gBACtD,QAAQ;YACT;QAED,WAAW;QACX,aAAa,IAAM,qBAAgC;QACnD,YAAY,CAAC,KACZ,qBAA8B,CAAC,UAAU,EAAE,IAAI;QAChD,eAAe,CAAC,IAAY,cAC3B,qBAA8B,CAAC,UAAU,EAAE,IAAI,EAAE;gBAChD,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACtB;QAED,UAAU;QACV,YAAY,IAAM,qBAA+B;QACjD,WAAW,CAAC,KAAe,qBAA6B,CAAC,SAAS,EAAE,IAAI;QACxE,cAAc,CAAC,aACd,qBAA6B,YAAY;gBACxC,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACtB;QACD,cAAc,CAAC,IAAY,aAC1B,qBAA6B,CAAC,SAAS,EAAE,IAAI,EAAE;gBAC9C,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACtB;QACD,cAAc,CAAC,KACd,qBAA2B,CAAC,SAAS,EAAE,IAAI,EAAE;gBAC5C,QAAQ;YACT;QACD,kBAAkB,CAAC,IAAY;YAC9B,MAAM,SAAS,IAAI;YACnB,IAAI,SAAS,UAAU,OAAO,MAAM,CAAC,YAAY,QAAQ,QAAQ;YACjE,IAAI,SAAS,QAAQ,OAAO,MAAM,CAAC,UAAU,QAAQ,MAAM;YAC3D,IAAI,SAAS,WAAW,OAAO,MAAM,CAAC,aAAa,QAAQ,SAAS;YACpE,MAAM,QAAQ,OAAO,QAAQ,KAAK,CAAC,CAAC,EAAE,OAAO,QAAQ,IAAI,GAAG;YAC5D,OAAO,qBAAoC,CAAC,SAAS,EAAE,GAAG,QAAQ,EAAE,OAAO;QAC5E;QAEA,WAAW;QACX,eAAe,CAAC,cACf,qBAA8B,aAAa;gBAC1C,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACtB;QAED,yDAAyD;QACzD,mBAAmB,CAAC,YACnB,qBAAsC,CAAC,UAAU,EAAE,UAAU,QAAQ,CAAC;QACvE,kBAAkB,CAAC,oBAClB,qBAAoC,oBAAoB;gBACvD,QAAQ;gBACR,MAAM,KAAK,SAAS,CAAC;YACtB;QACD,qBAAqB,CAAC,WAAmB,WACxC,qBAA2B,CAAC,UAAU,EAAE,UAAU,SAAS,EAAE,UAAU,EAAE;gBACxE,QAAQ;YACT;IACF;AACD", "debugId": null}}, {"offset": {"line": 415, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/types/index.ts"], "sourcesContent": ["// Types pour l'application Tontine\n\n// Enums\nexport enum CaisseType {\n  PRINCIPALE = 'PRINCIPALE',\n  REUNION = 'REUNION',\n}\n\nexport enum UserRole {\n  SECRETARY_GENERAL = 'secretary_general',\n  CONTROLLER = 'controller',\n  CASHIER = 'cashier',\n}\n\nexport enum UserStatus {\n  ACTIF = 'actif',\n  EN_ATTENTE = 'en_attente',\n  SUSPENDU = 'suspendu',\n}\n\nexport enum PaymentDirection {\n  IN = 'IN',\n  OUT = 'OUT',\n}\n\nexport enum PaymentFunction {\n  CONTRIBUTION = 'cotisation',\n  TRANSFER = 'transfert',\n  EXTERNAL = 'exterieur',\n}\n\n// Interfaces principales\nexport interface User {\n  _id: string;\n  username: string;\n  role: UserRole;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface Session {\n  _id: string;\n  annee: number;\n  dateDebut: string;\n  dateFin: string;\n  dateProchaineReunion?: string;\n  nextReunionId?: string;\n  partFixe: number;\n  createdBy: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface Reunion {\n  _id: string;\n  dateReunion: string;\n  lieu?: string;\n  caissePrincipale?: string;\n  sessionId: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface Caisse {\n  _id: string;\n  nom: string;\n  type: CaisseType;\n  soldeActuel: number;\n  sessionId?: string;\n  createdBy: string;\n  cashierId?: string;\n  caissePrincipaleId?: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\n// Nouveau: Member (différent de User)\nexport interface Member {\n  _id: string;\n  firstName: string;\n  lastName: string;\n  phone?: string;\n  email?: string;\n  address?: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\n// Nouveau: SessionMember (inscription d'un membre à une session)\nexport interface SessionMember {\n  _id: string;\n  sessionId: string;\n  memberId: string;\n  parts: number;\n  totalDue: number;\n  paidSoFar: number;\n  expectedToDate: number;\n  overdueAmount: number;\n  createdAt: string;\n  updatedAt: string;\n}\n\n// Nouveau: Payment\nexport interface Payment {\n  _id: string;\n  direction: PaymentDirection;\n  func: PaymentFunction;\n  amount: number;\n  date: string;\n  sessionId?: string;\n  reunionId?: string;\n  caisseId: string;\n  receivingCaisseId?: string;\n  memberId?: string;\n  reason?: string;\n  exitOrderId?: string;\n  createdBy: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\n// DTOs pour les formulaires\nexport interface CreateSessionDto {\n  annee: number;\n  dateDebut: string;\n  dateFin: string;\n  partFixe: number;\n}\n\nexport interface UpdateSessionDto {\n  annee?: number;\n  dateDebut?: string;\n  dateFin?: string;\n  partFixe?: number;\n}\n\nexport interface CreateCaisseDto {\n  nom: string;\n  type: CaisseType;\n  soldeActuel?: number;\n  sessionId?: string;\n  cashierId?: string;\n  caissePrincipaleId?: string;\n}\n\nexport interface UpdateCaisseDto {\n  nom?: string;\n  type?: CaisseType;\n  soldeActuel?: number;\n  sessionId?: string;\n  cashierId?: string;\n  caissePrincipaleId?: string;\n}\n\nexport interface UpdateReunionDto {\n  dateReunion?: string;\n  lieu?: string;\n  caissePrincipale?: string;\n}\n\n// Nouveaux DTOs\nexport interface CreateMemberDto {\n  firstName: string;\n  lastName: string;\n  phone?: string;\n  email?: string;\n  address?: string;\n}\n\nexport interface UpdateMemberDto {\n  firstName?: string;\n  lastName?: string;\n  phone?: string;\n  email?: string;\n  address?: string;\n}\n\nexport interface CreatePaymentDto {\n  direction: PaymentDirection;\n  func: PaymentFunction;\n  amount: number;\n  caisseId: string;\n  receivingCaisseId?: string;\n  sessionId?: string;\n  reunionId?: string;\n  memberId?: string;\n  reason?: string;\n  exitOrderId?: string;\n}\n\nexport interface CreateSessionMemberDto {\n  sessionId: string;\n  memberId: string;\n  parts: number;\n}\n\n// Types pour les statistiques\nexport interface SessionStats {\n  total: number;\n  active: number;\n  completed: number;\n  totalPartFixe: number;\n}\n\nexport interface CaisseStats {\n  total: number;\n  principales: number;\n  reunions: number;\n  soldeTotal: number;\n  soldePrincipales: number;\n  soldeReunions: number;\n}\n\nexport interface MemberStats {\n  total: number;\n  withEmail: number;\n  withPhone: number;\n  withAddress: number;\n}\n\nexport interface PaymentStats {\n  totalIn: number;\n  totalOut: number;\n  netAmount: number;\n  contributionsTotal: number;\n  transfersTotal: number;\n  externalTotal: number;\n}\n\n// Types pour les formulaires\nexport interface SessionForm {\n  annee: number;\n  dateDebut: string;\n  dateFin: string;\n  partFixe: number;\n}\n\nexport interface CaisseForm {\n  nom: string;\n  type: CaisseType;\n  soldeActuel: number;\n  sessionId?: string;\n  cashierId?: string;\n  caissePrincipaleId?: string;\n}\n\nexport interface MemberForm {\n  firstName: string;\n  lastName: string;\n  phone?: string;\n  email?: string;\n  address?: string;\n}\n\nexport interface PaymentForm {\n  direction: PaymentDirection;\n  func: PaymentFunction;\n  amount: number;\n  caisseId: string;\n  receivingCaisseId?: string;\n  sessionId?: string;\n  reunionId?: string;\n  memberId?: string;\n  reason?: string;\n}\n\n// Types pour les réponses API\nexport interface ApiResponse<T> {\n  data: T;\n  message?: string;\n  success: boolean;\n}\n\nexport interface PaginatedResponse<T> {\n  data: T[];\n  total: number;\n  page: number;\n  limit: number;\n  totalPages: number;\n}\n\n// Types pour les filtres\nexport interface SessionFilters {\n  annee?: number;\n  status?: 'active' | 'completed' | 'all';\n}\n\nexport interface CaisseFilters {\n  type?: CaisseType | 'all';\n  sessionId?: string;\n}\n\nexport interface MemberFilters {\n  search?: string;\n  hasEmail?: boolean;\n  hasPhone?: boolean;\n}\n\nexport interface PaymentFilters {\n  direction?: PaymentDirection | 'all';\n  func?: PaymentFunction | 'all';\n  caisseId?: string;\n  sessionId?: string;\n  memberId?: string;\n  dateFrom?: string;\n  dateTo?: string;\n}\n\n// Types pour les réponses spéciales\nexport interface MemberDebrief {\n  member: Member;\n  totalIn: number;\n  totalOut: number;\n  netAmount: number;\n  contributionsTotal: number;\n  transfersTotal: number;\n  externalTotal: number;\n  payments: Payment[];\n}\n"], "names": [], "mappings": "AAAA,mCAAmC;AAEnC,QAAQ;;;;;;;;;;;;;AACD,IAAA,AAAK,oCAAA;;;WAAA;;AAKL,IAAA,AAAK,kCAAA;;;;WAAA;;AAML,IAAA,AAAK,oCAAA;;;;WAAA;;AAML,IAAA,AAAK,0CAAA;;;WAAA;;AAKL,IAAA,AAAK,yCAAA;;;;WAAA", "debugId": null}}, {"offset": {"line": 461, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Projets/tontine/frontend/src/app/dashboard/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { useSession } from \"next-auth/react\";\nimport Link from \"next/link\";\nimport {\n\tCard,\n\tCardContent,\n\tCardDescription,\n\tCardHeader,\n\tCardTitle,\n} from \"@/components/ui/card\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { DollarSign, Calendar, Wallet, Plus, ArrowRight } from \"lucide-react\";\nimport { useApi } from \"@/hooks/use-api\";\nimport { type Session, type Caisse, CaisseType } from \"@/types\";\n\nexport default function DashboardPage() {\n\tconst { data: session, status } = useSession();\n\tconst api = useApi();\n\n\tconst [sessions, setSessions] = useState<Session[]>([]);\n\tconst [caisses, setCaisses] = useState<Caisse[]>([]);\n\tconst [loading, setLoading] = useState(true);\n\n\t// Vérifier les permissions\n\tconst canViewSessions =\n\t\tsession?.user &&\n\t\t[\"admin\", \"tresorier\", \"membre\"].includes((session.user as any).role);\n\tconst canViewCaisses =\n\t\tsession?.user &&\n\t\t[\"admin\", \"tresorier\"].includes((session.user as any).role);\n\tconst canCreateSessions =\n\t\tsession?.user && (session.user as any).role === \"admin\";\n\tconst canCreateCaisses =\n\t\tsession?.user &&\n\t\t[\"admin\", \"tresorier\"].includes((session.user as any).role);\n\n\t// Charger les données\n\tuseEffect(() => {\n\t\tlet isMounted = true;\n\n\t\tconst loadData = async () => {\n\t\t\tif (!session?.accessToken) return;\n\n\t\t\t// Permissions calculées ici\n\t\t\tconst role = (session.user as any)?.role;\n\t\t\tconst canViewSessions = [\"admin\", \"tresorier\", \"membre\"].includes(role);\n\t\t\tconst canViewCaisses = [\"admin\", \"tresorier\"].includes(role);\n\n\t\t\tif (!canViewSessions && !canViewCaisses) {\n\t\t\t\tsetLoading(false);\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\ttry {\n\t\t\t\tsetLoading(true);\n\t\t\t\tconst promises = [];\n\n\t\t\t\tif (canViewSessions) promises.push(api.getSessions());\n\t\t\t\tif (canViewCaisses) promises.push(api.getCaisses());\n\n\t\t\t\tconst results = await Promise.all(promises);\n\n\t\t\t\tlet idx = 0;\n\t\t\t\tif (canViewSessions && isMounted)\n\t\t\t\t\tsetSessions((results[idx++] as Session[]) || []);\n\t\t\t\tif (canViewCaisses && isMounted)\n\t\t\t\t\tsetCaisses((results[idx++] as Caisse[]) || []);\n\t\t\t} catch (err) {\n\t\t\t\tconsole.error(err);\n\t\t\t} finally {\n\t\t\t\tif (isMounted) setLoading(false);\n\t\t\t}\n\t\t};\n\n\t\tif (status === \"authenticated\") {\n\t\t\tloadData();\n\t\t} else if (status === \"unauthenticated\") {\n\t\t\tsetLoading(false);\n\t\t}\n\n\t\treturn () => {\n\t\t\tisMounted = false;\n\t\t};\n\t}, [status]);\n\n\t// Calculer les statistiques\n\tconst now = new Date();\n\tconst activeSessions = sessions.filter(\n\t\t(s) => new Date(s.dateDebut) <= now && new Date(s.dateFin) >= now,\n\t);\n\tconst totalSolde = caisses.reduce((sum, c) => sum + c.soldeActuel, 0);\n\tconst caissesPrincipales = caisses.filter(\n\t\t(c) => c.type === CaisseType.PRINCIPALE,\n\t);\n\tconst caissesReunions = caisses.filter((c) => c.type === CaisseType.REUNION);\n\n\tif (loading) {\n\t\treturn (\n\t\t\t<div className=\"flex items-center justify-center h-64\">\n\t\t\t\t<div className=\"text-center\">\n\t\t\t\t\t<div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto\"></div>\n\t\t\t\t\t<p className=\"mt-2 text-sm text-gray-600\">\n\t\t\t\t\t\tChargement du tableau de bord...\n\t\t\t\t\t</p>\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t);\n\t}\n\n\treturn (\n\t\t<div className=\"space-y-6\">\n\t\t\t{/* Welcome section */}\n\t\t\t<div>\n\t\t\t\t<h1 className=\"text-2xl font-bold text-gray-900\">\n\t\t\t\t\tBienvenue, {session?.user?.name}!\n\t\t\t\t</h1>\n\t\t\t\t<p className=\"text-gray-600 mt-1\">\n\t\t\t\t\tVoici un aperçu de votre tontine aujourd'hui.\n\t\t\t\t</p>\n\t\t\t\t{session?.user && (\n\t\t\t\t\t<div className=\"mt-2 text-sm text-gray-500\">\n\t\t\t\t\t\tConnecté en tant que{\" \"}\n\t\t\t\t\t\t<span className=\"font-medium\">{(session.user as any).role}</span> (\n\t\t\t\t\t\t{(session.user as any).username})\n\t\t\t\t\t</div>\n\t\t\t\t)}\n\t\t\t</div>\n\n\t\t\t{/* Stats grid */}\n\t\t\t<div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n\t\t\t\t{canViewSessions && (\n\t\t\t\t\t<Card>\n\t\t\t\t\t\t<CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n\t\t\t\t\t\t\t<CardTitle className=\"text-sm font-medium\">\n\t\t\t\t\t\t\t\tSessions Totales\n\t\t\t\t\t\t\t</CardTitle>\n\t\t\t\t\t\t\t<Calendar className=\"h-4 w-4 text-muted-foreground\" />\n\t\t\t\t\t\t</CardHeader>\n\t\t\t\t\t\t<CardContent>\n\t\t\t\t\t\t\t<div className=\"text-2xl font-bold\">{sessions.length}</div>\n\t\t\t\t\t\t\t<p className=\"text-xs text-muted-foreground\">\n\t\t\t\t\t\t\t\t{activeSessions.length} actives\n\t\t\t\t\t\t\t</p>\n\t\t\t\t\t\t</CardContent>\n\t\t\t\t\t</Card>\n\t\t\t\t)}\n\n\t\t\t\t{canViewSessions && (\n\t\t\t\t\t<Card>\n\t\t\t\t\t\t<CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n\t\t\t\t\t\t\t<CardTitle className=\"text-sm font-medium\">\n\t\t\t\t\t\t\t\tSessions Actives\n\t\t\t\t\t\t\t</CardTitle>\n\t\t\t\t\t\t\t<Calendar className=\"h-4 w-4 text-green-600\" />\n\t\t\t\t\t\t</CardHeader>\n\t\t\t\t\t\t<CardContent>\n\t\t\t\t\t\t\t<div className=\"text-2xl font-bold text-green-600\">\n\t\t\t\t\t\t\t\t{activeSessions.length}\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t<p className=\"text-xs text-muted-foreground\">\n\t\t\t\t\t\t\t\tEn cours actuellement\n\t\t\t\t\t\t\t</p>\n\t\t\t\t\t\t</CardContent>\n\t\t\t\t\t</Card>\n\t\t\t\t)}\n\n\t\t\t\t{canViewCaisses && (\n\t\t\t\t\t<Card>\n\t\t\t\t\t\t<CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n\t\t\t\t\t\t\t<CardTitle className=\"text-sm font-medium\">\n\t\t\t\t\t\t\t\tTotal Caisses\n\t\t\t\t\t\t\t</CardTitle>\n\t\t\t\t\t\t\t<Wallet className=\"h-4 w-4 text-muted-foreground\" />\n\t\t\t\t\t\t</CardHeader>\n\t\t\t\t\t\t<CardContent>\n\t\t\t\t\t\t\t<div className=\"text-2xl font-bold\">{caisses.length}</div>\n\t\t\t\t\t\t\t<p className=\"text-xs text-muted-foreground\">\n\t\t\t\t\t\t\t\t{caissesPrincipales.length} principales,{\" \"}\n\t\t\t\t\t\t\t\t{caissesReunions.length} réunions\n\t\t\t\t\t\t\t</p>\n\t\t\t\t\t\t</CardContent>\n\t\t\t\t\t</Card>\n\t\t\t\t)}\n\n\t\t\t\t{canViewCaisses && (\n\t\t\t\t\t<Card>\n\t\t\t\t\t\t<CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n\t\t\t\t\t\t\t<CardTitle className=\"text-sm font-medium\">Solde Total</CardTitle>\n\t\t\t\t\t\t\t<DollarSign className=\"h-4 w-4 text-green-600\" />\n\t\t\t\t\t\t</CardHeader>\n\t\t\t\t\t\t<CardContent>\n\t\t\t\t\t\t\t<div className=\"text-2xl font-bold text-green-600\">\n\t\t\t\t\t\t\t\t{totalSolde.toLocaleString()} FCFA\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t<p className=\"text-xs text-muted-foreground\">\n\t\t\t\t\t\t\t\tToutes caisses confondues\n\t\t\t\t\t\t\t</p>\n\t\t\t\t\t\t</CardContent>\n\t\t\t\t\t</Card>\n\t\t\t\t)}\n\t\t\t</div>\n\n\t\t\t{/* Actions rapides */}\n\t\t\t<div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n\t\t\t\t{canViewSessions && (\n\t\t\t\t\t<Card>\n\t\t\t\t\t\t<CardHeader className=\"flex flex-row items-center justify-between\">\n\t\t\t\t\t\t\t<div>\n\t\t\t\t\t\t\t\t<CardTitle>Sessions Récentes</CardTitle>\n\t\t\t\t\t\t\t\t<CardDescription>Dernières sessions créées</CardDescription>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t{canCreateSessions && (\n\t\t\t\t\t\t\t\t<Button asChild size=\"sm\">\n\t\t\t\t\t\t\t\t\t<Link href=\"/dashboard/sessions/new\">\n\t\t\t\t\t\t\t\t\t\t<Plus className=\"mr-2 h-4 w-4\" />\n\t\t\t\t\t\t\t\t\t\tNouvelle\n\t\t\t\t\t\t\t\t\t</Link>\n\t\t\t\t\t\t\t\t</Button>\n\t\t\t\t\t\t\t)}\n\t\t\t\t\t\t</CardHeader>\n\t\t\t\t\t\t<CardContent>\n\t\t\t\t\t\t\t<div className=\"space-y-4\">\n\t\t\t\t\t\t\t\t{sessions.slice(0, 4).map((session) => (\n\t\t\t\t\t\t\t\t\t<div\n\t\t\t\t\t\t\t\t\t\tkey={session._id}\n\t\t\t\t\t\t\t\t\t\tclassName=\"flex items-center justify-between\"\n\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t<div>\n\t\t\t\t\t\t\t\t\t\t\t<p className=\"text-sm font-medium text-gray-900\">\n\t\t\t\t\t\t\t\t\t\t\t\tSession {session.annee}\n\t\t\t\t\t\t\t\t\t\t\t</p>\n\t\t\t\t\t\t\t\t\t\t\t<p className=\"text-xs text-gray-500\">\n\t\t\t\t\t\t\t\t\t\t\t\t{new Date(session.dateDebut).toLocaleDateString()} -{\" \"}\n\t\t\t\t\t\t\t\t\t\t\t\t{new Date(session.dateFin).toLocaleDateString()}\n\t\t\t\t\t\t\t\t\t\t\t</p>\n\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t<div className=\"text-sm font-medium text-blue-600\">\n\t\t\t\t\t\t\t\t\t\t\t{session.partFixe.toLocaleString()} FCFA\n\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t))}\n\t\t\t\t\t\t\t\t{sessions.length === 0 && (\n\t\t\t\t\t\t\t\t\t<p className=\"text-sm text-muted-foreground text-center py-4\">\n\t\t\t\t\t\t\t\t\t\tAucune session trouvée\n\t\t\t\t\t\t\t\t\t</p>\n\t\t\t\t\t\t\t\t)}\n\t\t\t\t\t\t\t\t{sessions.length > 0 && (\n\t\t\t\t\t\t\t\t\t<div className=\"pt-2\">\n\t\t\t\t\t\t\t\t\t\t<Button\n\t\t\t\t\t\t\t\t\t\t\tvariant=\"outline\"\n\t\t\t\t\t\t\t\t\t\t\tsize=\"sm\"\n\t\t\t\t\t\t\t\t\t\t\tasChild\n\t\t\t\t\t\t\t\t\t\t\tclassName=\"w-full\"\n\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t<Link href=\"/dashboard/sessions\">\n\t\t\t\t\t\t\t\t\t\t\t\tVoir toutes les sessions\n\t\t\t\t\t\t\t\t\t\t\t\t<ArrowRight className=\"ml-2 h-4 w-4\" />\n\t\t\t\t\t\t\t\t\t\t\t</Link>\n\t\t\t\t\t\t\t\t\t\t</Button>\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t)}\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</CardContent>\n\t\t\t\t\t</Card>\n\t\t\t\t)}\n\n\t\t\t\t{canViewCaisses && (\n\t\t\t\t\t<Card>\n\t\t\t\t\t\t<CardHeader className=\"flex flex-row items-center justify-between\">\n\t\t\t\t\t\t\t<div>\n\t\t\t\t\t\t\t\t<CardTitle>Caisses Récentes</CardTitle>\n\t\t\t\t\t\t\t\t<CardDescription>Dernières caisses créées</CardDescription>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t{canCreateCaisses && (\n\t\t\t\t\t\t\t\t<Button asChild size=\"sm\">\n\t\t\t\t\t\t\t\t\t<Link href=\"/dashboard/caisses/new\">\n\t\t\t\t\t\t\t\t\t\t<Plus className=\"mr-2 h-4 w-4\" />\n\t\t\t\t\t\t\t\t\t\tNouvelle\n\t\t\t\t\t\t\t\t\t</Link>\n\t\t\t\t\t\t\t\t</Button>\n\t\t\t\t\t\t\t)}\n\t\t\t\t\t\t</CardHeader>\n\t\t\t\t\t\t<CardContent>\n\t\t\t\t\t\t\t<div className=\"space-y-4\">\n\t\t\t\t\t\t\t\t{caisses.slice(0, 4).map((caisse) => (\n\t\t\t\t\t\t\t\t\t<div\n\t\t\t\t\t\t\t\t\t\tkey={caisse._id}\n\t\t\t\t\t\t\t\t\t\tclassName=\"flex items-center justify-between\"\n\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t<div>\n\t\t\t\t\t\t\t\t\t\t\t<p className=\"text-sm font-medium text-gray-900\">\n\t\t\t\t\t\t\t\t\t\t\t\t{caisse.nom}\n\t\t\t\t\t\t\t\t\t\t\t</p>\n\t\t\t\t\t\t\t\t\t\t\t<p className=\"text-xs text-gray-500\">\n\t\t\t\t\t\t\t\t\t\t\t\t{caisse.type === CaisseType.PRINCIPALE\n\t\t\t\t\t\t\t\t\t\t\t\t\t? \"Principale\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t: \"Réunion\"}\n\t\t\t\t\t\t\t\t\t\t\t</p>\n\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t<div\n\t\t\t\t\t\t\t\t\t\t\tclassName={`text-sm font-medium ${\n\t\t\t\t\t\t\t\t\t\t\t\tcaisse.soldeActuel > 0\n\t\t\t\t\t\t\t\t\t\t\t\t\t? \"text-green-600\"\n\t\t\t\t\t\t\t\t\t\t\t\t\t: \"text-gray-500\"\n\t\t\t\t\t\t\t\t\t\t\t}`}\n\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t{caisse.soldeActuel.toLocaleString()} FCFA\n\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t))}\n\t\t\t\t\t\t\t\t{caisses.length === 0 && (\n\t\t\t\t\t\t\t\t\t<p className=\"text-sm text-muted-foreground text-center py-4\">\n\t\t\t\t\t\t\t\t\t\tAucune caisse trouvée\n\t\t\t\t\t\t\t\t\t</p>\n\t\t\t\t\t\t\t\t)}\n\t\t\t\t\t\t\t\t{caisses.length > 0 && (\n\t\t\t\t\t\t\t\t\t<div className=\"pt-2\">\n\t\t\t\t\t\t\t\t\t\t<Button\n\t\t\t\t\t\t\t\t\t\t\tvariant=\"outline\"\n\t\t\t\t\t\t\t\t\t\t\tsize=\"sm\"\n\t\t\t\t\t\t\t\t\t\t\tasChild\n\t\t\t\t\t\t\t\t\t\t\tclassName=\"w-full\"\n\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t<Link href=\"/dashboard/caisses\">\n\t\t\t\t\t\t\t\t\t\t\t\tVoir toutes les caisses\n\t\t\t\t\t\t\t\t\t\t\t\t<ArrowRight className=\"ml-2 h-4 w-4\" />\n\t\t\t\t\t\t\t\t\t\t\t</Link>\n\t\t\t\t\t\t\t\t\t\t</Button>\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t)}\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</CardContent>\n\t\t\t\t\t</Card>\n\t\t\t\t)}\n\t\t\t</div>\n\t\t</div>\n\t);\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AAOA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAfA;;;;;;;;;;AAiBe,SAAS;IACvB,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,IAAA,2QAAU;IAC5C,MAAM,MAAM,IAAA,gJAAM;IAElB,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,4VAAQ,EAAY,EAAE;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,4VAAQ,EAAW,EAAE;IACnD,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,4VAAQ,EAAC;IAEvC,2BAA2B;IAC3B,MAAM,kBACL,SAAS,QACT;QAAC;QAAS;QAAa;KAAS,CAAC,QAAQ,CAAC,AAAC,QAAQ,IAAI,CAAS,IAAI;IACrE,MAAM,iBACL,SAAS,QACT;QAAC;QAAS;KAAY,CAAC,QAAQ,CAAC,AAAC,QAAQ,IAAI,CAAS,IAAI;IAC3D,MAAM,oBACL,SAAS,QAAQ,AAAC,QAAQ,IAAI,CAAS,IAAI,KAAK;IACjD,MAAM,mBACL,SAAS,QACT;QAAC;QAAS;KAAY,CAAC,QAAQ,CAAC,AAAC,QAAQ,IAAI,CAAS,IAAI;IAE3D,sBAAsB;IACtB,IAAA,6VAAS,EAAC;QACT,IAAI,YAAY;QAEhB,MAAM,WAAW;YAChB,IAAI,CAAC,SAAS,aAAa;YAE3B,4BAA4B;YAC5B,MAAM,OAAQ,QAAQ,IAAI,EAAU;YACpC,MAAM,kBAAkB;gBAAC;gBAAS;gBAAa;aAAS,CAAC,QAAQ,CAAC;YAClE,MAAM,iBAAiB;gBAAC;gBAAS;aAAY,CAAC,QAAQ,CAAC;YAEvD,IAAI,CAAC,mBAAmB,CAAC,gBAAgB;gBACxC,WAAW;gBACX;YACD;YAEA,IAAI;gBACH,WAAW;gBACX,MAAM,WAAW,EAAE;gBAEnB,IAAI,iBAAiB,SAAS,IAAI,CAAC,IAAI,WAAW;gBAClD,IAAI,gBAAgB,SAAS,IAAI,CAAC,IAAI,UAAU;gBAEhD,MAAM,UAAU,MAAM,QAAQ,GAAG,CAAC;gBAElC,IAAI,MAAM;gBACV,IAAI,mBAAmB,WACtB,YAAY,AAAC,OAAO,CAAC,MAAM,IAAkB,EAAE;gBAChD,IAAI,kBAAkB,WACrB,WAAW,AAAC,OAAO,CAAC,MAAM,IAAiB,EAAE;YAC/C,EAAE,OAAO,KAAK;gBACb,QAAQ,KAAK,CAAC;YACf,SAAU;gBACT,IAAI,WAAW,WAAW;YAC3B;QACD;QAEA,IAAI,WAAW,iBAAiB;YAC/B;QACD,OAAO,IAAI,WAAW,mBAAmB;YACxC,WAAW;QACZ;QAEA,OAAO;YACN,YAAY;QACb;IACD,GAAG;QAAC;KAAO;IAEX,4BAA4B;IAC5B,MAAM,MAAM,IAAI;IAChB,MAAM,iBAAiB,SAAS,MAAM,CACrC,CAAC,IAAM,IAAI,KAAK,EAAE,SAAS,KAAK,OAAO,IAAI,KAAK,EAAE,OAAO,KAAK;IAE/D,MAAM,aAAa,QAAQ,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,WAAW,EAAE;IACnE,MAAM,qBAAqB,QAAQ,MAAM,CACxC,CAAC,IAAM,EAAE,IAAI,KAAK,+IAAU,CAAC,UAAU;IAExC,MAAM,kBAAkB,QAAQ,MAAM,CAAC,CAAC,IAAM,EAAE,IAAI,KAAK,+IAAU,CAAC,OAAO;IAE3E,IAAI,SAAS;QACZ,qBACC,yXAAC;YAAI,WAAU;sBACd,cAAA,yXAAC;gBAAI,WAAU;;kCACd,yXAAC;wBAAI,WAAU;;;;;;kCACf,yXAAC;wBAAE,WAAU;kCAA6B;;;;;;;;;;;;;;;;;IAM9C;IAEA,qBACC,yXAAC;QAAI,WAAU;;0BAEd,yXAAC;;kCACA,yXAAC;wBAAG,WAAU;;4BAAmC;4BACpC,SAAS,MAAM;4BAAK;;;;;;;kCAEjC,yXAAC;wBAAE,WAAU;kCAAqB;;;;;;oBAGjC,SAAS,sBACT,yXAAC;wBAAI,WAAU;;4BAA6B;4BACtB;0CACrB,yXAAC;gCAAK,WAAU;0CAAe,AAAC,QAAQ,IAAI,CAAS,IAAI;;;;;;4BAAQ;4BAC/D,QAAQ,IAAI,CAAS,QAAQ;4BAAC;;;;;;;;;;;;;0BAMnC,yXAAC;gBAAI,WAAU;;oBACb,iCACA,yXAAC,oJAAI;;0CACJ,yXAAC,0JAAU;gCAAC,WAAU;;kDACrB,yXAAC,yJAAS;wCAAC,WAAU;kDAAsB;;;;;;kDAG3C,yXAAC,sTAAQ;wCAAC,WAAU;;;;;;;;;;;;0CAErB,yXAAC,2JAAW;;kDACX,yXAAC;wCAAI,WAAU;kDAAsB,SAAS,MAAM;;;;;;kDACpD,yXAAC;wCAAE,WAAU;;4CACX,eAAe,MAAM;4CAAC;;;;;;;;;;;;;;;;;;;oBAM1B,iCACA,yXAAC,oJAAI;;0CACJ,yXAAC,0JAAU;gCAAC,WAAU;;kDACrB,yXAAC,yJAAS;wCAAC,WAAU;kDAAsB;;;;;;kDAG3C,yXAAC,sTAAQ;wCAAC,WAAU;;;;;;;;;;;;0CAErB,yXAAC,2JAAW;;kDACX,yXAAC;wCAAI,WAAU;kDACb,eAAe,MAAM;;;;;;kDAEvB,yXAAC;wCAAE,WAAU;kDAAgC;;;;;;;;;;;;;;;;;;oBAO/C,gCACA,yXAAC,oJAAI;;0CACJ,yXAAC,0JAAU;gCAAC,WAAU;;kDACrB,yXAAC,yJAAS;wCAAC,WAAU;kDAAsB;;;;;;kDAG3C,yXAAC,gTAAM;wCAAC,WAAU;;;;;;;;;;;;0CAEnB,yXAAC,2JAAW;;kDACX,yXAAC;wCAAI,WAAU;kDAAsB,QAAQ,MAAM;;;;;;kDACnD,yXAAC;wCAAE,WAAU;;4CACX,mBAAmB,MAAM;4CAAC;4CAAc;4CACxC,gBAAgB,MAAM;4CAAC;;;;;;;;;;;;;;;;;;;oBAM3B,gCACA,yXAAC,oJAAI;;0CACJ,yXAAC,0JAAU;gCAAC,WAAU;;kDACrB,yXAAC,yJAAS;wCAAC,WAAU;kDAAsB;;;;;;kDAC3C,yXAAC,gUAAU;wCAAC,WAAU;;;;;;;;;;;;0CAEvB,yXAAC,2JAAW;;kDACX,yXAAC;wCAAI,WAAU;;4CACb,WAAW,cAAc;4CAAG;;;;;;;kDAE9B,yXAAC;wCAAE,WAAU;kDAAgC;;;;;;;;;;;;;;;;;;;;;;;;0BASjD,yXAAC;gBAAI,WAAU;;oBACb,iCACA,yXAAC,oJAAI;;0CACJ,yXAAC,0JAAU;gCAAC,WAAU;;kDACrB,yXAAC;;0DACA,yXAAC,yJAAS;0DAAC;;;;;;0DACX,yXAAC,+JAAe;0DAAC;;;;;;;;;;;;oCAEjB,mCACA,yXAAC,wJAAM;wCAAC,OAAO;wCAAC,MAAK;kDACpB,cAAA,yXAAC,kTAAI;4CAAC,MAAK;;8DACV,yXAAC,0SAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;0CAMrC,yXAAC,2JAAW;0CACX,cAAA,yXAAC;oCAAI,WAAU;;wCACb,SAAS,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,wBAC1B,yXAAC;gDAEA,WAAU;;kEAEV,yXAAC;;0EACA,yXAAC;gEAAE,WAAU;;oEAAoC;oEACvC,QAAQ,KAAK;;;;;;;0EAEvB,yXAAC;gEAAE,WAAU;;oEACX,IAAI,KAAK,QAAQ,SAAS,EAAE,kBAAkB;oEAAG;oEAAG;oEACpD,IAAI,KAAK,QAAQ,OAAO,EAAE,kBAAkB;;;;;;;;;;;;;kEAG/C,yXAAC;wDAAI,WAAU;;4DACb,QAAQ,QAAQ,CAAC,cAAc;4DAAG;;;;;;;;+CAb/B,QAAQ,GAAG;;;;;wCAiBjB,SAAS,MAAM,KAAK,mBACpB,yXAAC;4CAAE,WAAU;sDAAiD;;;;;;wCAI9D,SAAS,MAAM,GAAG,mBAClB,yXAAC;4CAAI,WAAU;sDACd,cAAA,yXAAC,wJAAM;gDACN,SAAQ;gDACR,MAAK;gDACL,OAAO;gDACP,WAAU;0DAEV,cAAA,yXAAC,kTAAI;oDAAC,MAAK;;wDAAsB;sEAEhC,yXAAC,gUAAU;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAU7B,gCACA,yXAAC,oJAAI;;0CACJ,yXAAC,0JAAU;gCAAC,WAAU;;kDACrB,yXAAC;;0DACA,yXAAC,yJAAS;0DAAC;;;;;;0DACX,yXAAC,+JAAe;0DAAC;;;;;;;;;;;;oCAEjB,kCACA,yXAAC,wJAAM;wCAAC,OAAO;wCAAC,MAAK;kDACpB,cAAA,yXAAC,kTAAI;4CAAC,MAAK;;8DACV,yXAAC,0SAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;0CAMrC,yXAAC,2JAAW;0CACX,cAAA,yXAAC;oCAAI,WAAU;;wCACb,QAAQ,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,uBACzB,yXAAC;gDAEA,WAAU;;kEAEV,yXAAC;;0EACA,yXAAC;gEAAE,WAAU;0EACX,OAAO,GAAG;;;;;;0EAEZ,yXAAC;gEAAE,WAAU;0EACX,OAAO,IAAI,KAAK,+IAAU,CAAC,UAAU,GACnC,eACA;;;;;;;;;;;;kEAGL,yXAAC;wDACA,WAAW,CAAC,oBAAoB,EAC/B,OAAO,WAAW,GAAG,IAClB,mBACA,iBACF;;4DAED,OAAO,WAAW,CAAC,cAAc;4DAAG;;;;;;;;+CApBjC,OAAO,GAAG;;;;;wCAwBhB,QAAQ,MAAM,KAAK,mBACnB,yXAAC;4CAAE,WAAU;sDAAiD;;;;;;wCAI9D,QAAQ,MAAM,GAAG,mBACjB,yXAAC;4CAAI,WAAU;sDACd,cAAA,yXAAC,wJAAM;gDACN,SAAQ;gDACR,MAAK;gDACL,OAAO;gDACP,WAAU;0DAEV,cAAA,yXAAC,kTAAI;oDAAC,MAAK;;wDAAqB;sEAE/B,yXAAC,gUAAU;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYlC", "debugId": null}}]}