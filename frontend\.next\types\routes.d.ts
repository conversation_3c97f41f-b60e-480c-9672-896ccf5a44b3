// This file is generated automatically by Next.js
// Do not edit this file manually

type AppRoutes = "/" | "/auth/register" | "/auth/signin" | "/dashboard" | "/dashboard/caisses" | "/dashboard/caisses/[id]/edit" | "/dashboard/caisses/new" | "/dashboard/members" | "/dashboard/members/[id]" | "/dashboard/members/[id]/edit" | "/dashboard/members/new" | "/dashboard/payments" | "/dashboard/payments/new" | "/dashboard/sessions" | "/dashboard/sessions/[id]" | "/dashboard/sessions/[id]/edit" | "/dashboard/sessions/new"
type AppRouteHandlerRoutes = "/api/auth/[...nextauth]"
type PageRoutes = never
type LayoutRoutes = "/" | "/dashboard"
type RedirectRoutes = never
type RewriteRoutes = never
type Routes = AppRoutes | PageRoutes | LayoutRoutes | RedirectRoutes | RewriteRoutes | AppRouteHandlerRoutes


interface ParamMap {
  "/": {}
  "/api/auth/[...nextauth]": { "nextauth": string[]; }
  "/auth/register": {}
  "/auth/signin": {}
  "/dashboard": {}
  "/dashboard/caisses": {}
  "/dashboard/caisses/[id]/edit": { "id": string; }
  "/dashboard/caisses/new": {}
  "/dashboard/members": {}
  "/dashboard/members/[id]": { "id": string; }
  "/dashboard/members/[id]/edit": { "id": string; }
  "/dashboard/members/new": {}
  "/dashboard/payments": {}
  "/dashboard/payments/new": {}
  "/dashboard/sessions": {}
  "/dashboard/sessions/[id]": { "id": string; }
  "/dashboard/sessions/[id]/edit": { "id": string; }
  "/dashboard/sessions/new": {}
}


export type ParamsOf<Route extends Routes> = ParamMap[Route]

interface LayoutSlotMap {
  "/": never
  "/dashboard": never
}


export type { AppRoutes, PageRoutes, LayoutRoutes, RedirectRoutes, RewriteRoutes, ParamMap, AppRouteHandlerRoutes }

declare global {
  /**
   * Props for Next.js App Router page components
   * @example
   * ```tsx
   * export default function Page(props: PageProps<'/blog/[slug]'>) {
   *   const { slug } = await props.params
   *   return <div>Blog post: {slug}</div>
   * }
   * ```
   */
  interface PageProps<AppRoute extends AppRoutes> {
    params: Promise<ParamMap[AppRoute]>
    searchParams: Promise<Record<string, string | string[] | undefined>>
  }

  /**
   * Props for Next.js App Router layout components
   * @example
   * ```tsx
   * export default function Layout(props: LayoutProps<'/dashboard'>) {
   *   return <div>{props.children}</div>
   * }
   * ```
   */
  type LayoutProps<LayoutRoute extends LayoutRoutes> = {
    params: Promise<ParamMap[LayoutRoute]>
    children: React.ReactNode
  } & {
    [K in LayoutSlotMap[LayoutRoute]]: React.ReactNode
  }

  /**
   * Context for Next.js App Router route handlers
   * @example
   * ```tsx
   * export async function GET(request: NextRequest, context: RouteContext<'/api/users/[id]'>) {
   *   const { id } = await context.params
   *   return Response.json({ id })
   * }
   * ```
   */
  interface RouteContext<AppRouteHandlerRoute extends AppRouteHandlerRoutes> {
    params: Promise<ParamMap[AppRouteHandlerRoute]>
  }
}
