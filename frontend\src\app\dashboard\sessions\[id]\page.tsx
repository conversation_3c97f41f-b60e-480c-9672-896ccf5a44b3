"use client";

import { useState, useEffect } from "react";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { ArrowLeft, Users, Plus, Trash2, DollarSign } from "lucide-react";
import Link from "next/link";

import { Button } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@/components/ui/table";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
} from "@/components/ui/dialog";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useApi } from "@/hooks/use-api";
import { Session, SessionMember, Member, UserRole } from "@/types";

export default function SessionDetailPage() {
	const params = useParams();
	const router = useRouter();
	const { data: session } = useSession();
	const api = useApi();

	const [sessionData, setSessionData] = useState<Session | null>(null);
	const [sessionMembers, setSessionMembers] = useState<SessionMember[]>([]);
	const [availableMembers, setAvailableMembers] = useState<Member[]>([]);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
	const [selectedMemberId, setSelectedMemberId] = useState("");
	const [memberParts, setMemberParts] = useState(1);

	const sessionId = params.id as string;

	// Vérifier les permissions
	const canManageSession =
		session?.user &&
		((session.user as any).role === UserRole.SECRETARY_GENERAL ||
			(session.user as any).role === UserRole.CONTROLLER);

	useEffect(() => {
		if (session?.accessToken && sessionId) {
			loadData();
		}
	}, [session, sessionId]);

	const loadData = async () => {
		try {
			setLoading(true);
			setError(null);

			const [sessionData, sessionMembersData, allMembers] = await Promise.all([
				api.getSession(sessionId),
				api.getSessionMembers(sessionId),
				api.getMembers(),
			]);

			setSessionData(sessionData);
			setSessionMembers(sessionMembersData);
			
			// Filtrer les membres disponibles (non encore inscrits)
			const enrolledMemberIds = sessionMembersData.map(sm => sm.memberId);
			const available = allMembers.filter(m => !enrolledMemberIds.includes(m._id));
			setAvailableMembers(available);
		} catch (error) {
			console.error("Erreur lors du chargement:", error);
			setError("Erreur lors du chargement des données");
		} finally {
			setLoading(false);
		}
	};

	const handleAddMember = async () => {
		if (!selectedMemberId || !sessionData) return;

		try {
			const totalDue = sessionData.partFixe * memberParts;
			
			await api.addSessionMember({
				sessionId: sessionId,
				memberId: selectedMemberId,
				parts: memberParts,
			});

			// Recharger les données
			await loadData();
			
			// Réinitialiser le formulaire
			setSelectedMemberId("");
			setMemberParts(1);
			setIsAddDialogOpen(false);
		} catch (error) {
			console.error("Erreur lors de l'ajout du membre:", error);
		}
	};

	const handleRemoveMember = async (memberId: string) => {
		if (!confirm("Êtes-vous sûr de vouloir retirer ce membre de la session ?")) {
			return;
		}

		try {
			await api.removeSessionMember(sessionId, memberId);
			await loadData();
		} catch (error) {
			console.error("Erreur lors de la suppression:", error);
		}
	};

	const formatCurrency = (amount: number) => {
		return new Intl.NumberFormat('fr-FR', {
			style: 'currency',
			currency: 'XAF',
		}).format(amount);
	};

	const formatDate = (dateString: string) => {
		return new Date(dateString).toLocaleDateString('fr-FR', {
			year: 'numeric',
			month: 'long',
			day: 'numeric',
		});
	};

	if (!canManageSession) {
		return (
			<div className="space-y-6">
				<div className="flex items-center gap-4">
					<Link href="/dashboard/sessions">
						<Button variant="ghost" size="sm">
							<ArrowLeft className="h-4 w-4 mr-2" />
							Retour
						</Button>
					</Link>
				</div>
				<div className="flex items-center justify-center h-64">
					<div className="text-center">
						<h2 className="text-lg font-semibold text-gray-900">Accès refusé</h2>
						<p className="text-gray-600">
							Vous n'avez pas les permissions pour accéder à cette page.
						</p>
					</div>
				</div>
			</div>
		);
	}

	if (loading) {
		return (
			<div className="space-y-6">
				<div className="flex items-center gap-4">
					<Link href="/dashboard/sessions">
						<Button variant="ghost" size="sm">
							<ArrowLeft className="h-4 w-4 mr-2" />
							Retour
						</Button>
					</Link>
				</div>
				<div className="flex justify-center py-8">
					<div className="text-gray-500">Chargement...</div>
				</div>
			</div>
		);
	}

	if (error || !sessionData) {
		return (
			<div className="space-y-6">
				<div className="flex items-center gap-4">
					<Link href="/dashboard/sessions">
						<Button variant="ghost" size="sm">
							<ArrowLeft className="h-4 w-4 mr-2" />
							Retour
						</Button>
					</Link>
				</div>
				<div className="flex items-center justify-center h-64">
					<div className="text-center">
						<h2 className="text-lg font-semibold text-gray-900">Erreur</h2>
						<p className="text-gray-600">{error || "Session introuvable"}</p>
					</div>
				</div>
			</div>
		);
	}

	const totalMembers = sessionMembers.length;
	const totalParts = sessionMembers.reduce((sum, sm) => sum + sm.parts, 0);
	const totalDue = sessionMembers.reduce((sum, sm) => sum + sm.totalDue, 0);
	const totalPaid = sessionMembers.reduce((sum, sm) => sum + sm.paidSoFar, 0);

	return (
		<div className="space-y-6">
			{/* En-tête */}
			<div className="flex items-center justify-between">
				<div className="flex items-center gap-4">
					<Link href="/dashboard/sessions">
						<Button variant="ghost" size="sm">
							<ArrowLeft className="h-4 w-4 mr-2" />
							Retour
						</Button>
					</Link>
					<div>
						<h1 className="text-2xl font-bold text-gray-900">
							Session {sessionData.annee}
						</h1>
						<p className="text-gray-600">
							{formatDate(sessionData.dateDebut)} - {formatDate(sessionData.dateFin)}
						</p>
					</div>
				</div>
				<Link href={`/dashboard/sessions/${sessionData._id}/edit`}>
					<Button>Modifier</Button>
				</Link>
			</div>

			{/* Informations de la session */}
			<Card>
				<CardHeader>
					<CardTitle>Informations de la session</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="grid grid-cols-1 md:grid-cols-4 gap-6">
						<div>
							<h3 className="font-medium text-gray-900 mb-2">Année</h3>
							<p className="text-gray-600">{sessionData.annee}</p>
						</div>
						<div>
							<h3 className="font-medium text-gray-900 mb-2">Part fixe</h3>
							<p className="text-gray-600">{formatCurrency(sessionData.partFixe)}</p>
						</div>
						<div>
							<h3 className="font-medium text-gray-900 mb-2">Date de début</h3>
							<p className="text-gray-600">{formatDate(sessionData.dateDebut)}</p>
						</div>
						<div>
							<h3 className="font-medium text-gray-900 mb-2">Date de fin</h3>
							<p className="text-gray-600">{formatDate(sessionData.dateFin)}</p>
						</div>
					</div>
				</CardContent>
			</Card>

			{/* Statistiques des membres */}
			<div className="grid grid-cols-1 md:grid-cols-4 gap-4">
				<Card>
					<CardHeader className="pb-2">
						<CardTitle className="text-sm font-medium text-gray-600">
							Membres inscrits
						</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">{totalMembers}</div>
					</CardContent>
				</Card>
				<Card>
					<CardHeader className="pb-2">
						<CardTitle className="text-sm font-medium text-gray-600">
							Total parts
						</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold text-blue-600">{totalParts}</div>
					</CardContent>
				</Card>
				<Card>
					<CardHeader className="pb-2">
						<CardTitle className="text-sm font-medium text-gray-600">
							Montant dû
						</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold text-orange-600">
							{formatCurrency(totalDue)}
						</div>
					</CardContent>
				</Card>
				<Card>
					<CardHeader className="pb-2">
						<CardTitle className="text-sm font-medium text-gray-600">
							Montant payé
						</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold text-green-600">
							{formatCurrency(totalPaid)}
						</div>
					</CardContent>
				</Card>
			</div>

			{/* Liste des membres */}
			<Card>
				<CardHeader>
					<div className="flex items-center justify-between">
						<div>
							<CardTitle>Membres de la session</CardTitle>
							<CardDescription>
								{totalMembers} membre(s) inscrit(s)
							</CardDescription>
						</div>
						<Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
							<DialogTrigger asChild>
								<Button>
									<Plus className="h-4 w-4 mr-2" />
									Ajouter un membre
								</Button>
							</DialogTrigger>
							<DialogContent>
								<DialogHeader>
									<DialogTitle>Ajouter un membre à la session</DialogTitle>
									<DialogDescription>
										Sélectionnez un membre et définissez le nombre de parts
									</DialogDescription>
								</DialogHeader>
								<div className="space-y-4">
									<div>
										<Label htmlFor="member">Membre</Label>
										<Select value={selectedMemberId} onValueChange={setSelectedMemberId}>
											<SelectTrigger>
												<SelectValue placeholder="Sélectionner un membre" />
											</SelectTrigger>
											<SelectContent>
												{availableMembers.map((member) => (
													<SelectItem key={member._id} value={member._id}>
														{member.firstName} {member.lastName}
													</SelectItem>
												))}
											</SelectContent>
										</Select>
									</div>
									<div>
										<Label htmlFor="parts">Nombre de parts</Label>
										<Input
											id="parts"
											type="number"
											min="1"
											value={memberParts}
											onChange={(e) => setMemberParts(Number(e.target.value))}
										/>
									</div>
									{sessionData && memberParts > 0 && (
										<div className="text-sm text-gray-600">
											Montant total dû: {formatCurrency(sessionData.partFixe * memberParts)}
										</div>
									)}
									<div className="flex justify-end gap-2">
										<Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
											Annuler
										</Button>
										<Button onClick={handleAddMember} disabled={!selectedMemberId}>
											Ajouter
										</Button>
									</div>
								</div>
							</DialogContent>
						</Dialog>
					</div>
				</CardHeader>
				<CardContent>
					{sessionMembers.length > 0 ? (
						<Table>
							<TableHeader>
								<TableRow>
									<TableHead>Membre</TableHead>
									<TableHead>Parts</TableHead>
									<TableHead>Montant dû</TableHead>
									<TableHead>Payé</TableHead>
									<TableHead>Reste</TableHead>
									<TableHead>Actions</TableHead>
								</TableRow>
							</TableHeader>
							<TableBody>
								{sessionMembers.map((sessionMember) => {
									const remainingAmount = sessionMember.totalDue - sessionMember.paidSoFar;
									return (
										<TableRow key={sessionMember._id}>
											<TableCell>
												<div className="font-medium">
													{/* Note: Dans une vraie implémentation, il faudrait joindre les données du membre */}
													Membre ID: {sessionMember.memberId}
												</div>
											</TableCell>
											<TableCell>{sessionMember.parts}</TableCell>
											<TableCell>{formatCurrency(sessionMember.totalDue)}</TableCell>
											<TableCell className="text-green-600">
												{formatCurrency(sessionMember.paidSoFar)}
											</TableCell>
											<TableCell className={remainingAmount > 0 ? "text-red-600" : "text-green-600"}>
												{formatCurrency(remainingAmount)}
											</TableCell>
											<TableCell>
												<Button
													variant="ghost"
													size="sm"
													onClick={() => handleRemoveMember(sessionMember.memberId)}
													className="text-red-600 hover:text-red-700"
												>
													<Trash2 className="h-4 w-4" />
												</Button>
											</TableCell>
										</TableRow>
									);
								})}
							</TableBody>
						</Table>
					) : (
						<div className="text-center py-8 text-gray-500">
							Aucun membre inscrit à cette session
						</div>
					)}
				</CardContent>
			</Card>
		</div>
	);
}
