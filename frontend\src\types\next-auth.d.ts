import NextAuth from "next-auth"
declare module "next-auth" {
  interface Session {
    user: {
      id: string
    
      username: string
      role:  'secretary_general' | 'controller' | 'cashier'
    }
    accessToken: string
  }

  interface User {
    id: string
    username: string
    role: 'secretary_general' | 'controller' | 'cashier'
    accessToken: string
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    username: string
    role: 'secretary_general' | 'controller' | 'cashier'
    accessToken: string
  }
}
