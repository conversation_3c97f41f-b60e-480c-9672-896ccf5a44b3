"use client";

import { useState, useEffect } from "react";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { ArrowLeft, Mail, Phone, MapPin, DollarSign, TrendingUp, TrendingDown } from "lucide-react";
import Link from "next/link";

import { But<PERSON> } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { useApi } from "@/hooks/use-api";
import { Member, MemberDebrief, UserRole, PaymentDirection, PaymentFunction } from "@/types";

export default function MemberDetailPage() {
	const params = useParams();
	const router = useRouter();
	const { data: session } = useSession();
	const api = useApi();

	const [member, setMember] = useState<Member | null>(null);
	const [debrief, setDebrief] = useState<MemberDebrief | null>(null);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);

	const memberId = params.id as string;

	// Vérifier les permissions
	const canViewMembers =
		session?.user &&
		((session.user as any).role === UserRole.SECRETARY_GENERAL ||
			(session.user as any).role === UserRole.CONTROLLER);

	useEffect(() => {
		if (session?.accessToken && memberId) {
			loadData();
		}
	}, [session, memberId]);

	const loadData = async () => {
		try {
			setLoading(true);
			setError(null);

			const [memberData, debriefData] = await Promise.all([
				api.getMember(memberId),
				api.getMemberDebrief(memberId),
			]);

			setMember(memberData);
			setDebrief(debriefData);
		} catch (error) {
			console.error("Erreur lors du chargement:", error);
			setError("Erreur lors du chargement des données");
		} finally {
			setLoading(false);
		}
	};

	const formatCurrency = (amount: number) => {
		return new Intl.NumberFormat('fr-FR', {
			style: 'currency',
			currency: 'XAF',
		}).format(amount);
	};

	const formatDate = (dateString: string) => {
		return new Date(dateString).toLocaleDateString('fr-FR', {
			year: 'numeric',
			month: 'long',
			day: 'numeric',
		});
	};

	const getPaymentFunctionBadge = (func: PaymentFunction) => {
		const variants = {
			[PaymentFunction.CONTRIBUTION]: "default",
			[PaymentFunction.TRANSFER]: "secondary",
			[PaymentFunction.EXTERNAL]: "outline",
		} as const;

		const labels = {
			[PaymentFunction.CONTRIBUTION]: "Cotisation",
			[PaymentFunction.TRANSFER]: "Transfert",
			[PaymentFunction.EXTERNAL]: "Extérieur",
		};

		return (
			<Badge variant={variants[func]}>
				{labels[func]}
			</Badge>
		);
	};

	const getDirectionIcon = (direction: PaymentDirection) => {
		return direction === PaymentDirection.IN ? (
			<TrendingUp className="h-4 w-4 text-green-600" />
		) : (
			<TrendingDown className="h-4 w-4 text-red-600" />
		);
	};

	if (!canViewMembers) {
		return (
			<div className="space-y-6">
				<div className="flex items-center gap-4">
					<Link href="/dashboard/members">
						<Button variant="ghost" size="sm">
							<ArrowLeft className="h-4 w-4 mr-2" />
							Retour
						</Button>
					</Link>
				</div>
				<div className="flex items-center justify-center h-64">
					<div className="text-center">
						<h2 className="text-lg font-semibold text-gray-900">Accès refusé</h2>
						<p className="text-gray-600">
							Vous n'avez pas les permissions pour accéder à cette page.
						</p>
					</div>
				</div>
			</div>
		);
	}

	if (loading) {
		return (
			<div className="space-y-6">
				<div className="flex items-center gap-4">
					<Link href="/dashboard/members">
						<Button variant="ghost" size="sm">
							<ArrowLeft className="h-4 w-4 mr-2" />
							Retour
						</Button>
					</Link>
				</div>
				<div className="flex justify-center py-8">
					<div className="text-gray-500">Chargement...</div>
				</div>
			</div>
		);
	}

	if (error || !member) {
		return (
			<div className="space-y-6">
				<div className="flex items-center gap-4">
					<Link href="/dashboard/members">
						<Button variant="ghost" size="sm">
							<ArrowLeft className="h-4 w-4 mr-2" />
							Retour
						</Button>
					</Link>
				</div>
				<div className="flex items-center justify-center h-64">
					<div className="text-center">
						<h2 className="text-lg font-semibold text-gray-900">Erreur</h2>
						<p className="text-gray-600">{error || "Membre introuvable"}</p>
					</div>
				</div>
			</div>
		);
	}

	return (
		<div className="space-y-6">
			{/* En-tête */}
			<div className="flex items-center justify-between">
				<div className="flex items-center gap-4">
					<Link href="/dashboard/members">
						<Button variant="ghost" size="sm">
							<ArrowLeft className="h-4 w-4 mr-2" />
							Retour
						</Button>
					</Link>
					<div>
						<h1 className="text-2xl font-bold text-gray-900">
							{member.firstName} {member.lastName}
						</h1>
						<p className="text-gray-600">Détails du membre</p>
					</div>
				</div>
				<Link href={`/dashboard/members/${member._id}/edit`}>
					<Button>Modifier</Button>
				</Link>
			</div>

			{/* Informations du membre */}
			<Card>
				<CardHeader>
					<CardTitle>Informations personnelles</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="grid grid-cols-1 md:grid-cols-3 gap-6">
						<div>
							<h3 className="font-medium text-gray-900 mb-2">Nom complet</h3>
							<p className="text-gray-600">{member.firstName} {member.lastName}</p>
						</div>
						{member.email && (
							<div>
								<h3 className="font-medium text-gray-900 mb-2">Email</h3>
								<div className="flex items-center text-gray-600">
									<Mail className="h-4 w-4 mr-2" />
									{member.email}
								</div>
							</div>
						)}
						{member.phone && (
							<div>
								<h3 className="font-medium text-gray-900 mb-2">Téléphone</h3>
								<div className="flex items-center text-gray-600">
									<Phone className="h-4 w-4 mr-2" />
									{member.phone}
								</div>
							</div>
						)}
						{member.address && (
							<div className="md:col-span-3">
								<h3 className="font-medium text-gray-900 mb-2">Adresse</h3>
								<div className="flex items-center text-gray-600">
									<MapPin className="h-4 w-4 mr-2" />
									{member.address}
								</div>
							</div>
						)}
					</div>
				</CardContent>
			</Card>

			{/* Debrief financier */}
			{debrief && (
				<>
					{/* Statistiques financières */}
					<div className="grid grid-cols-1 md:grid-cols-4 gap-4">
						<Card>
							<CardHeader className="pb-2">
								<CardTitle className="text-sm font-medium text-gray-600">
									Total Entrées
								</CardTitle>
							</CardHeader>
							<CardContent>
								<div className="text-2xl font-bold text-green-600">
									{formatCurrency(debrief.totalIn)}
								</div>
							</CardContent>
						</Card>
						<Card>
							<CardHeader className="pb-2">
								<CardTitle className="text-sm font-medium text-gray-600">
									Total Sorties
								</CardTitle>
							</CardHeader>
							<CardContent>
								<div className="text-2xl font-bold text-red-600">
									{formatCurrency(debrief.totalOut)}
								</div>
							</CardContent>
						</Card>
						<Card>
							<CardHeader className="pb-2">
								<CardTitle className="text-sm font-medium text-gray-600">
									Solde Net
								</CardTitle>
							</CardHeader>
							<CardContent>
								<div className={`text-2xl font-bold ${debrief.netAmount >= 0 ? 'text-green-600' : 'text-red-600'}`}>
									{formatCurrency(debrief.netAmount)}
								</div>
							</CardContent>
						</Card>
						<Card>
							<CardHeader className="pb-2">
								<CardTitle className="text-sm font-medium text-gray-600">
									Cotisations
								</CardTitle>
							</CardHeader>
							<CardContent>
								<div className="text-2xl font-bold text-blue-600">
									{formatCurrency(debrief.contributionsTotal)}
								</div>
							</CardContent>
						</Card>
					</div>

					{/* Historique des paiements */}
					<Card>
						<CardHeader>
							<CardTitle>Historique des paiements</CardTitle>
							<CardDescription>
								{debrief.payments.length} paiement(s) enregistré(s)
							</CardDescription>
						</CardHeader>
						<CardContent>
							{debrief.payments.length > 0 ? (
								<Table>
									<TableHeader>
										<TableRow>
											<TableHead>Date</TableHead>
											<TableHead>Type</TableHead>
											<TableHead>Fonction</TableHead>
											<TableHead>Montant</TableHead>
										</TableRow>
									</TableHeader>
									<TableBody>
										{debrief.payments.map((payment) => (
											<TableRow key={payment._id}>
												<TableCell>{formatDate(payment.date)}</TableCell>
												<TableCell>
													<div className="flex items-center">
														{getDirectionIcon(payment.direction)}
														<span className="ml-2">
															{payment.direction === PaymentDirection.IN ? 'Entrée' : 'Sortie'}
														</span>
													</div>
												</TableCell>
												<TableCell>{getPaymentFunctionBadge(payment.func)}</TableCell>
												<TableCell>
													<span className={payment.direction === PaymentDirection.IN ? 'text-green-600' : 'text-red-600'}>
														{formatCurrency(payment.amount)}
													</span>
												</TableCell>
											</TableRow>
										))}
									</TableBody>
								</Table>
							) : (
								<div className="text-center py-8 text-gray-500">
									Aucun paiement enregistré
								</div>
							)}
						</CardContent>
					</Card>
				</>
			)}
		</div>
	);
}
