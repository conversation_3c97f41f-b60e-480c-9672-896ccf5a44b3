"use client";

import { useState, useEffect } from "react";
import { useR<PERSON>er, useSearchPara<PERSON> } from "next/navigation";
import { useSession } from "next-auth/react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@/components/ui/form";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { useApi } from "@/hooks/use-api";
import { 
	UserRole, 
	PaymentDirection, 
	PaymentFunction, 
	Caisse, 
	Member, 
	Session,
	PaymentForm 
} from "@/types";

const paymentSchema = z.object({
	direction: z.nativeEnum(PaymentDirection),
	func: z.nativeEnum(PaymentFunction),
	amount: z.number().positive("Le montant doit être positif"),
	caisseId: z.string().min(1, "Veuillez sélectionner une caisse"),
	receivingCaisseId: z.string().optional(),
	sessionId: z.string().optional(),
	reunionId: z.string().optional(),
	memberId: z.string().optional(),
	reason: z.string().optional(),
});

type PaymentFormData = z.infer<typeof paymentSchema>;

export default function NewPaymentPage() {
	const { data: session } = useSession();
	const router = useRouter();
	const searchParams = useSearchParams();
	const api = useApi();

	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const [caisses, setCaisses] = useState<Caisse[]>([]);
	const [members, setMembers] = useState<Member[]>([]);
	const [sessions, setSessions] = useState<Session[]>([]);

	// Type de paiement pré-sélectionné depuis l'URL
	const preselectedType = searchParams.get('type');

	// Vérifier les permissions
	const canCreatePayments =
		session?.user &&
		((session.user as any).role === UserRole.SECRETARY_GENERAL ||
			(session.user as any).role === UserRole.CONTROLLER ||
			(session.user as any).role === UserRole.CASHIER);

	const form = useForm<PaymentFormData>({
		resolver: zodResolver(paymentSchema),
		defaultValues: {
			direction: PaymentDirection.IN,
			func: preselectedType === 'contribution' ? PaymentFunction.CONTRIBUTION :
				  preselectedType === 'transfer' ? PaymentFunction.TRANSFER :
				  preselectedType === 'external' ? PaymentFunction.EXTERNAL :
				  PaymentFunction.CONTRIBUTION,
			amount: 0,
			caisseId: "",
			receivingCaisseId: "",
			sessionId: "",
			reunionId: "",
			memberId: "",
			reason: "",
		},
	});

	const watchedFunc = form.watch("func");
	const watchedDirection = form.watch("direction");

	useEffect(() => {
		if (session?.accessToken) {
			loadData();
		}
	}, [session]);

	// Ajuster la direction selon la fonction
	useEffect(() => {
		if (watchedFunc === PaymentFunction.CONTRIBUTION) {
			form.setValue("direction", PaymentDirection.IN);
		}
	}, [watchedFunc, form]);

	const loadData = async () => {
		try {
			const [caissesData, membersData, sessionsData] = await Promise.all([
				api.getCaisses(),
				api.getMembers(),
				api.getSessions(),
			]);

			setCaisses(caissesData);
			setMembers(membersData);
			setSessions(sessionsData);
		} catch (error) {
			console.error("Erreur lors du chargement des données:", error);
		}
	};

	const onSubmit = async (data: PaymentFormData) => {
		setIsLoading(true);
		setError(null);

		try {
			// Nettoyer les données selon le type de paiement
			const cleanData: any = {
				direction: data.direction,
				func: data.func,
				amount: data.amount,
				caisseId: data.caisseId,
			};

			// Ajouter les champs conditionnels
			if (data.receivingCaisseId) cleanData.receivingCaisseId = data.receivingCaisseId;
			if (data.sessionId) cleanData.sessionId = data.sessionId;
			if (data.reunionId) cleanData.reunionId = data.reunionId;
			if (data.memberId) cleanData.memberId = data.memberId;
			if (data.reason) cleanData.reason = data.reason;

			await api.createPayment(cleanData);
			router.push("/dashboard/payments");
		} catch (error) {
			console.error("Erreur lors de la création du paiement:", error);
			if (error instanceof Error) {
				setError(error.message);
			} else {
				setError("Erreur lors de la création du paiement. Veuillez réessayer.");
			}
		} finally {
			setIsLoading(false);
		}
	};

	if (!canCreatePayments) {
		return (
			<div className="space-y-6">
				<div className="flex items-center gap-4">
					<Link href="/dashboard/payments">
						<Button variant="ghost" size="sm">
							<ArrowLeft className="h-4 w-4 mr-2" />
							Retour
						</Button>
					</Link>
				</div>
				<div className="flex items-center justify-center h-64">
					<div className="text-center">
						<h2 className="text-lg font-semibold text-gray-900">Accès refusé</h2>
						<p className="text-gray-600">
							Vous n'avez pas les permissions pour créer des paiements.
						</p>
					</div>
				</div>
			</div>
		);
	}

	return (
		<div className="space-y-6">
			{/* En-tête */}
			<div className="flex items-center gap-4">
				<Link href="/dashboard/payments">
					<Button variant="ghost" size="sm">
						<ArrowLeft className="h-4 w-4 mr-2" />
						Retour
					</Button>
				</Link>
				<div>
					<h1 className="text-2xl font-bold text-gray-900">Nouveau Paiement</h1>
					<p className="text-gray-600">Enregistrer un nouveau paiement</p>
				</div>
			</div>

			{/* Formulaire */}
			<Card className="max-w-2xl">
				<CardHeader>
					<CardTitle>Informations du paiement</CardTitle>
					<CardDescription>
						Remplissez les informations du paiement à enregistrer
					</CardDescription>
				</CardHeader>
				<CardContent>
					<Form {...form}>
						<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
							{/* Type de paiement */}
							<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
								<FormField
									control={form.control}
									name="func"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Fonction *</FormLabel>
											<Select
												onValueChange={field.onChange}
												value={field.value}
											>
												<FormControl>
													<SelectTrigger disabled={isLoading}>
														<SelectValue placeholder="Type de paiement" />
													</SelectTrigger>
												</FormControl>
												<SelectContent>
													<SelectItem value={PaymentFunction.CONTRIBUTION}>
														Cotisation
													</SelectItem>
													<SelectItem value={PaymentFunction.TRANSFER}>
														Transfert
													</SelectItem>
													<SelectItem value={PaymentFunction.EXTERNAL}>
														Externe
													</SelectItem>
												</SelectContent>
											</Select>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="direction"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Direction *</FormLabel>
											<Select
												onValueChange={field.onChange}
												value={field.value}
												disabled={watchedFunc === PaymentFunction.CONTRIBUTION}
											>
												<FormControl>
													<SelectTrigger disabled={isLoading}>
														<SelectValue placeholder="Direction" />
													</SelectTrigger>
												</FormControl>
												<SelectContent>
													<SelectItem value={PaymentDirection.IN}>
														Entrée
													</SelectItem>
													<SelectItem value={PaymentDirection.OUT}>
														Sortie
													</SelectItem>
												</SelectContent>
											</Select>
											<FormMessage />
										</FormItem>
									)}
								/>
							</div>

							{/* Montant */}
							<FormField
								control={form.control}
								name="amount"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Montant *</FormLabel>
										<FormControl>
											<Input
												type="number"
												placeholder="0"
												{...field}
												onChange={(e) => field.onChange(Number(e.target.value))}
												disabled={isLoading}
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							{/* Caisse */}
							<FormField
								control={form.control}
								name="caisseId"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Caisse *</FormLabel>
										<Select
											onValueChange={field.onChange}
											value={field.value}
										>
											<FormControl>
												<SelectTrigger disabled={isLoading}>
													<SelectValue placeholder="Sélectionner une caisse" />
												</SelectTrigger>
											</FormControl>
											<SelectContent>
												{caisses.map((caisse) => (
													<SelectItem key={caisse._id} value={caisse._id}>
														{caisse.nom} ({caisse.type})
													</SelectItem>
												))}
											</SelectContent>
										</Select>
										<FormMessage />
									</FormItem>
								)}
							/>

							{/* Champs conditionnels */}
							{watchedFunc === PaymentFunction.TRANSFER && (
								<FormField
									control={form.control}
									name="receivingCaisseId"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Caisse de destination *</FormLabel>
											<Select
												onValueChange={field.onChange}
												value={field.value}
											>
												<FormControl>
													<SelectTrigger disabled={isLoading}>
														<SelectValue placeholder="Caisse de destination" />
													</SelectTrigger>
												</FormControl>
												<SelectContent>
													{caisses.map((caisse) => (
														<SelectItem key={caisse._id} value={caisse._id}>
															{caisse.nom} ({caisse.type})
														</SelectItem>
													))}
												</SelectContent>
											</Select>
											<FormMessage />
										</FormItem>
									)}
								/>
							)}

							{watchedFunc === PaymentFunction.CONTRIBUTION && (
								<>
									<FormField
										control={form.control}
										name="memberId"
										render={({ field }) => (
											<FormItem>
												<FormLabel>Membre *</FormLabel>
												<Select
													onValueChange={field.onChange}
													value={field.value}
												>
													<FormControl>
														<SelectTrigger disabled={isLoading}>
															<SelectValue placeholder="Sélectionner un membre" />
														</SelectTrigger>
													</FormControl>
													<SelectContent>
														{members.map((member) => (
															<SelectItem key={member._id} value={member._id}>
																{member.firstName} {member.lastName}
															</SelectItem>
														))}
													</SelectContent>
												</Select>
												<FormMessage />
											</FormItem>
										)}
									/>

									<FormField
										control={form.control}
										name="sessionId"
										render={({ field }) => (
											<FormItem>
												<FormLabel>Session</FormLabel>
												<Select
													onValueChange={field.onChange}
													value={field.value}
												>
													<FormControl>
														<SelectTrigger disabled={isLoading}>
															<SelectValue placeholder="Sélectionner une session" />
														</SelectTrigger>
													</FormControl>
													<SelectContent>
														{sessions.map((session) => (
															<SelectItem key={session._id} value={session._id}>
																Session {session.annee}
															</SelectItem>
														))}
													</SelectContent>
												</Select>
												<FormMessage />
											</FormItem>
										)}
									/>
								</>
							)}

							{watchedFunc === PaymentFunction.EXTERNAL && watchedDirection === PaymentDirection.OUT && (
								<FormField
									control={form.control}
									name="reason"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Motif *</FormLabel>
											<FormControl>
												<Textarea
													placeholder="Motif du paiement externe"
													{...field}
													disabled={isLoading}
													rows={3}
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
							)}

							{/* Message d'erreur */}
							{error && (
								<div className="text-red-600 text-sm bg-red-50 p-3 rounded">
									{error}
								</div>
							)}

							{/* Actions */}
							<div className="flex justify-end gap-4 pt-6">
								<Link href="/dashboard/payments">
									<Button variant="outline" disabled={isLoading}>
										Annuler
									</Button>
								</Link>
								<Button type="submit" disabled={isLoading}>
									{isLoading ? "Enregistrement..." : "Enregistrer le paiement"}
								</Button>
							</div>
						</form>
					</Form>
				</CardContent>
			</Card>
		</div>
	);
}
